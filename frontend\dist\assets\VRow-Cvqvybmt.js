import{aC as e,aG as t,aH as n,af as r,ar as i,bP as a,bf as o,bo as s}from"./index-BSnscBhv.js";const c=(()=>i.reduce((e,t)=>(e[t]={type:[Bo<PERSON><PERSON>,String,Number],default:!1},e),{}))(),l=(()=>i.reduce((e,t)=>{let n=`offset`+a(t);return e[n]={type:[String,Number],default:null},e},{}))(),u=(()=>i.reduce((e,t)=>{let n=`order`+a(t);return e[n]={type:[String,Number],default:null},e},{}))(),d={col:Object.keys(c),offset:Object.keys(l),order:Object.keys(u)};function f(e,t,n){let r=e;if(!(n==null||n===!1)){if(t){let n=t.replace(e,``);r+=`-${n}`}return e===`col`&&(r=`v-`+r),e===`col`&&(n===``||n===!0)||(r+=`-${n}`),r.toLowerCase()}}const p=[`auto`,`start`,`end`,`center`,`baseline`,`stretch`],m=n({cols:{type:[Boolean,String,Number],default:!1},...c,offset:{type:[String,Number],default:null},...l,order:{type:[String,Number],default:null},...u,alignSelf:{type:String,default:null,validator:e=>p.includes(e)},...t(),...r()},`VCol`),h=e()({name:`VCol`,props:m(),setup(e,t){let{slots:n}=t,r=o(()=>{let t=[],n;for(n in d)d[n].forEach(r=>{let i=e[r],a=f(n,r,i);a&&t.push(a)});let r=t.some(e=>e.startsWith(`v-col-`));return t.push({"v-col":!r||!e.cols,[`v-col-${e.cols}`]:e.cols,[`offset-${e.offset}`]:e.offset,[`order-${e.order}`]:e.order,[`align-self-${e.alignSelf}`]:e.alignSelf}),t});return()=>s(e.tag,{class:[r.value,e.class],style:e.style},n.default?.())}}),g=[`start`,`end`,`center`],_=[`space-between`,`space-around`,`space-evenly`];function v(e,t){return i.reduce((n,r)=>{let i=e+a(r);return n[i]=t(),n},{})}const y=[...g,`baseline`,`stretch`],b=e=>y.includes(e),x=v(`align`,()=>({type:String,default:null,validator:b})),S=[...g,..._],C=e=>S.includes(e),w=v(`justify`,()=>({type:String,default:null,validator:C})),T=[...g,..._,`stretch`],E=e=>T.includes(e),D=v(`alignContent`,()=>({type:String,default:null,validator:E})),O={align:Object.keys(x),justify:Object.keys(w),alignContent:Object.keys(D)},k={align:`align`,justify:`justify`,alignContent:`align-content`};function A(e,t,n){let r=k[e];if(n!=null){if(t){let n=t.replace(e,``);r+=`-${n}`}return r+=`-${n}`,r.toLowerCase()}}const j=n({dense:Boolean,noGutters:Boolean,align:{type:String,default:null,validator:b},...x,justify:{type:String,default:null,validator:C},...w,alignContent:{type:String,default:null,validator:E},...D,...t(),...r()},`VRow`),M=e()({name:`VRow`,props:j(),setup(e,t){let{slots:n}=t,r=o(()=>{let t=[],n;for(n in O)O[n].forEach(r=>{let i=e[r],a=A(n,r,i);a&&t.push(a)});return t.push({"v-row--no-gutters":e.noGutters,"v-row--dense":e.dense,[`align-${e.align}`]:e.align,[`justify-${e.justify}`]:e.justify,[`align-content-${e.alignContent}`]:e.alignContent}),t});return()=>s(e.tag,{class:[`v-row`,r.value,e.class],style:e.style},n.default?.())}});export{M as b,h as c};