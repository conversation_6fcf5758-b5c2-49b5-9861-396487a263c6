import{I as e,J as t,K as n,M as r,N as i,O as a,P as o,Q as s,R as c,S as l,T as u,U as d,W as f,X as p,Y as m,Z as h,a3 as g,a4 as _,a9 as v,aC as y,aD as b,aG as x,aH as S,aM as C,aU as w,aX as ee,aa as T,ab as E,af as D,am as O,an as k,ao as A,ap as j,aq as M,as as N,at as te,au as P,av as ne,aw as F,ax as I,b8 as L,bD as re,bG as R,bL as z,bM as B,bQ as V,bR as ie,bS as H,bb as U,bd as W,bf as G,bg as K,bm as q,bq as J,t as Y,w as X,x as ae,y as oe}from"./index-BSnscBhv.js";function se(e){let{selectedElement:t,containerElement:n,isRtl:r,isHorizontal:i}=e,a=Z(i,n),o=de(i,r,n),s=Z(i,t),c=fe(i,t),l=s*.4;return o>c?c-l:o+a<c+s?c-a+s+l:o}function ce(e){let{selectedElement:t,containerElement:n,isHorizontal:r}=e,i=Z(r,n),a=fe(r,t),o=Z(r,t);return a-i/2+o/2}function le(e,t){let n=e?`scrollWidth`:`scrollHeight`;return t?.[n]||0}function ue(e,t){let n=e?`clientWidth`:`clientHeight`;return t?.[n]||0}function de(e,t,n){if(!n)return 0;let{scrollLeft:r,offsetWidth:i,scrollWidth:a}=n;return e?t?a-i+r:r:n.scrollTop}function Z(e,t){let n=e?`offsetWidth`:`offsetHeight`;return t?.[n]||0}function fe(e,t){let n=e?`offsetLeft`:`offsetTop`;return t?.[n]||0}const pe=Symbol.for(`vuetify:v-slide-group`),Q=S({centerActive:Boolean,contentClass:null,direction:{type:String,default:`horizontal`},symbol:{type:null,default:pe},nextIcon:{type:j,default:`$next`},prevIcon:{type:j,default:`$prev`},showArrows:{type:[Boolean,String],validator:e=>typeof e==`boolean`||[`always`,`desktop`,`mobile`].includes(e)},...x(),...N({mobile:null}),...D(),...i({selectedClass:`v-slide-group-item--active`})},`VSlideGroup`),$=y()({name:`VSlideGroup`,props:Q(),emits:{"update:modelValue":e=>!0},setup(t,n){let{slots:r}=n,{isRtl:i}=ne(),{displayClasses:o,mobile:s}=te(t),c=a(t,t.symbol),l=z(!1),u=z(0),d=z(0),f=z(0),p=G(()=>t.direction===`horizontal`),{resizeRef:m,contentRect:h}=O(),{resizeRef:g,contentRect:_}=O(),v=M(),y=G(()=>({container:m.el,duration:200,easing:`easeOutQuart`})),b=G(()=>c.selected.value.length?c.items.value.findIndex(e=>e.id===c.selected.value[0]):-1),x=G(()=>c.selected.value.length?c.items.value.findIndex(e=>e.id===c.selected.value[c.selected.value.length-1]):-1);if(L){let e=-1;re(()=>[c.selected.value,h.value,_.value,p.value],()=>{cancelAnimationFrame(e),e=requestAnimationFrame(()=>{if(h.value&&_.value){let e=p.value?`width`:`height`;d.value=h.value[e],f.value=_.value[e],l.value=d.value+1<f.value}if(b.value>=0&&g.el){let e=g.el.children[x.value];C(e,t.centerActive)}})})}let S=z(!1);function C(e,t){let n=0;n=t?ce({containerElement:m.el,isHorizontal:p.value,selectedElement:e}):se({containerElement:m.el,isHorizontal:p.value,isRtl:i.value,selectedElement:e}),w(n)}function w(e){if(!L||!m.el)return;let t=Z(p.value,m.el),n=de(p.value,i.value,m.el),r=le(p.value,m.el);if(!(r<=t||Math.abs(e-n)<16)){if(p.value&&i.value&&m.el){let{scrollWidth:t,offsetWidth:n}=m.el;e=t-n-e}p.value?v.horizontal(e,y.value):v(e,y.value)}}function T(e){let{scrollTop:t,scrollLeft:n}=e.target;u.value=p.value?n:t}function D(e){if(S.value=!0,!(!l.value||!g.el)){for(let t of e.composedPath())for(let e of g.el.children)if(e===t){C(e);return}}}function k(e){S.value=!1}let A=!1;function j(e){!A&&!S.value&&!(e.relatedTarget&&g.el?.contains(e.relatedTarget))&&R(),A=!1}function N(){A=!0}function P(e){if(!g.el)return;function t(t){e.preventDefault(),R(t)}p.value?e.key===`ArrowRight`?t(i.value?`prev`:`next`):e.key===`ArrowLeft`&&t(i.value?`next`:`prev`):e.key===`ArrowDown`?t(`next`):e.key===`ArrowUp`&&t(`prev`),e.key===`Home`?t(`first`):e.key===`End`&&t(`last`)}function F(e,t){if(!e)return;let n=e;do n=n?.[t===`next`?`nextElementSibling`:`previousElementSibling`];while(n?.hasAttribute(`disabled`));return n}function R(e){if(!g.el)return;let t;if(e)if(e===`next`){if(t=F(g.el.querySelector(`:focus`),e),!t)return R(`first`)}else if(e===`prev`){if(t=F(g.el.querySelector(`:focus`),e),!t)return R(`last`)}else e===`first`?(t=g.el.firstElementChild,t?.hasAttribute(`disabled`)&&(t=F(t,`next`))):e===`last`&&(t=g.el.lastElementChild,t?.hasAttribute(`disabled`)&&(t=F(t,`prev`)));else{let e=ee(g.el);t=e[0]}t&&t.focus({preventScroll:!0})}function B(e){let t=p.value&&i.value?-1:1,n=(e===`prev`?-t:t)*d.value,r=u.value+n;if(p.value&&i.value&&m.el){let{scrollWidth:e,offsetWidth:t}=m.el;r+=e-t}w(r)}let H=G(()=>({next:c.next,prev:c.prev,select:c.select,isSelected:c.isSelected})),U=G(()=>{switch(t.showArrows){case`always`:return!0;case`desktop`:return!s.value;case!0:return l.value||Math.abs(u.value)>0;case`mobile`:return s.value||l.value||Math.abs(u.value)>0;default:return!s.value&&(l.value||Math.abs(u.value)>0)}}),W=G(()=>Math.abs(u.value)>1),J=G(()=>{if(!m.value)return!1;let e=le(p.value,m.el),t=ue(p.value,m.el),n=e-t;return n-Math.abs(u.value)>1});return I(()=>q(t.tag,{class:V([`v-slide-group`,{"v-slide-group--vertical":!p.value,"v-slide-group--has-affixes":U.value,"v-slide-group--is-overflowing":l.value},o.value,t.class]),style:ie(t.style),tabindex:S.value||c.selected.value.length?-1:0,onFocus:j},{default:()=>[U.value&&K(`div`,{key:`prev`,class:V([`v-slide-group__prev`,{"v-slide-group__prev--disabled":!W.value}]),onMousedown:N,onClick:()=>W.value&&B(`prev`)},[r.prev?.(H.value)??q(E,null,{default:()=>[q(e,{icon:i.value?t.nextIcon:t.prevIcon},null)]})]),K(`div`,{key:`container`,ref:m,class:V([`v-slide-group__container`,t.contentClass]),onScroll:T},[K(`div`,{ref:g,class:`v-slide-group__content`,onFocusin:D,onFocusout:k,onKeydown:P},[r.default?.(H.value)])]),U.value&&K(`div`,{key:`next`,class:V([`v-slide-group__next`,{"v-slide-group__next--disabled":!J.value}]),onMousedown:N,onClick:()=>J.value&&B(`next`)},[r.next?.(H.value)??q(E,null,{default:()=>[q(e,{icon:i.value?t.prevIcon:t.nextIcon},null)]})])]})),{selected:c.selected,scrollTo:B,scrollOffset:u,focus:R,hasPrev:W,hasNext:J}}}),me=Symbol.for(`vuetify:v-chip-group`),he=S({baseColor:String,column:Boolean,filter:Boolean,valueComparator:{type:Function,default:w},...Q(),...x(),...i({selectedClass:`v-chip--selected`}),...D(),...k(),...c({variant:`tonal`})},`VChipGroup`);y()({name:`VChipGroup`,props:he(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,{themeClasses:r}=A(e),{isSelected:i,select:o,next:s,prev:c,selected:l}=a(e,me);return b({VChip:{baseColor:B(()=>e.baseColor),color:B(()=>e.color),disabled:B(()=>e.disabled),filter:B(()=>e.filter),variant:B(()=>e.variant)}}),I(()=>{let t=$.filterProps(e);return q($,J(t,{class:[`v-chip-group`,{"v-chip-group--column":e.column},r.value,e.class],style:e.style}),{default:()=>[n.default?.({isSelected:i,select:o,next:s,prev:c,selected:l.value})]})}),{}}});const ge=S({activeClass:String,appendAvatar:String,appendIcon:j,baseColor:String,closable:Boolean,closeIcon:{type:j,default:`$delete`},closeLabel:{type:String,default:`$vuetify.close`},draggable:Boolean,filter:Boolean,filterIcon:{type:j,default:`$complete`},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:j,ripple:{type:[Boolean,Object],default:!0},text:{type:[String,Number,Boolean],default:void 0},modelValue:{type:Boolean,default:!0},onClick:C(),onClickOnce:C(),...m(),...x(),...u(),...f(),...r(),...g(),...ae(),...t(),...D({tag:`span`}),...k(),...c({variant:`tonal`})},`VChip`),_e=y()({name:`VChip`,directives:{vRipple:X},props:ge(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(t,r){let{attrs:i,emit:a,slots:c}=r,{t:u}=P(),{borderClasses:f}=h(t),{densityClasses:m}=d(t),{elevationClasses:g}=p(t),{roundedClasses:y}=_(t),{sizeClasses:b}=n(t),{themeClasses:x}=A(t),S=F(t,`modelValue`),C=o(t,me,!1),w=oe(t,i),ee=B(()=>t.link!==!1&&w.isLink.value),E=G(()=>!t.disabled&&t.link!==!1&&(!!C||t.link||w.isClickable.value)),D=B(()=>({"aria-label":u(t.closeLabel),disabled:t.disabled,onClick(e){e.preventDefault(),e.stopPropagation(),S.value=!1,a(`click:close`,e)}})),{colorClasses:O,colorStyles:k,variantClasses:j}=l(()=>{let e=!C||C.isSelected.value;return{color:e?t.color??t.baseColor:t.baseColor,variant:t.variant}});function M(e){a(`click`,e),E.value&&(w.navigate?.(e),C?.toggle())}function N(e){(e.key===`Enter`||e.key===` `)&&(e.preventDefault(),M(e))}return()=>{let n=w.isLink.value?`a`:t.tag,r=!!(t.appendIcon||t.appendAvatar),i=!!(r||c.append),a=!!(c.close||t.closable),o=!!(c.filter||t.filter)&&C,l=!!(t.prependIcon||t.prependAvatar),u=!!(l||c.prepend);return S.value&&R(q(n,J({class:[`v-chip`,{"v-chip--disabled":t.disabled,"v-chip--label":t.label,"v-chip--link":E.value,"v-chip--filter":o,"v-chip--pill":t.pill,[`${t.activeClass}`]:t.activeClass&&w.isActive?.value},x.value,f.value,O.value,m.value,g.value,y.value,b.value,j.value,C?.selectedClass.value,t.class],style:[k.value,t.style],disabled:t.disabled||void 0,draggable:t.draggable,tabindex:E.value?0:void 0,onClick:M,onKeydown:E.value&&!ee.value&&N},w.linkProps),{default:()=>[s(E.value,`v-chip`),o&&q(T,{key:`filter`},{default:()=>[R(K(`div`,{class:`v-chip__filter`},[c.filter?q(v,{key:`filter-defaults`,disabled:!t.filterIcon,defaults:{VIcon:{icon:t.filterIcon}}},c.filter):q(e,{key:`filter-icon`,icon:t.filterIcon},null)]),[[U,C.isSelected.value]])]}),u&&K(`div`,{key:`prepend`,class:`v-chip__prepend`},[c.prepend?q(v,{key:`prepend-defaults`,disabled:!l,defaults:{VAvatar:{image:t.prependAvatar,start:!0},VIcon:{icon:t.prependIcon,start:!0}}},c.prepend):K(W,null,[t.prependIcon&&q(e,{key:`prepend-icon`,icon:t.prependIcon,start:!0},null),t.prependAvatar&&q(Y,{key:`prepend-avatar`,image:t.prependAvatar,start:!0},null)])]),K(`div`,{class:`v-chip__content`,"data-no-activator":``},[c.default?.({isSelected:C?.isSelected.value,selectedClass:C?.selectedClass.value,select:C?.select,toggle:C?.toggle,value:C?.value.value,disabled:t.disabled})??H(t.text)]),i&&K(`div`,{key:`append`,class:`v-chip__append`},[c.append?q(v,{key:`append-defaults`,disabled:!r,defaults:{VAvatar:{end:!0,image:t.appendAvatar},VIcon:{end:!0,icon:t.appendIcon}}},c.append):K(W,null,[t.appendIcon&&q(e,{key:`append-icon`,end:!0,icon:t.appendIcon},null),t.appendAvatar&&q(Y,{key:`append-avatar`,end:!0,image:t.appendAvatar},null)])]),a&&K(`button`,J({key:`close`,class:`v-chip__close`,type:`button`,"data-testid":`close-chip`},D.value),[c.close?q(v,{key:`close-defaults`,defaults:{VIcon:{icon:t.closeIcon,size:`x-small`}}},c.close):q(e,{key:`close-icon`,icon:t.closeIcon,size:`x-small`},null)])]}),[[X,E.value&&t.ripple,null]])}}});export{_e as b,$ as c,Q as d};