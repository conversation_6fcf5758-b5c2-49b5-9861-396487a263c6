<?php

/**
 * RPS Management System - Backend Testing Configuration
 * 
 * This file contains all configuration settings for the testing framework
 */

return [
    // Test Environment Settings
    'environment' => [
        'name' => 'testing',
        'debug' => true,
        'verbose' => true,
        'timeout' => 30, // seconds
    ],

    // Database Configuration for Testing
    'database' => [
        'hostname' => 'localhost',
        'database' => 'rps_management',
        'username' => 'root',
        'password' => '',
        'DBDriver' => 'MySQLi',
        'port' => 3306,
        'charset' => 'utf8mb4',
        'DBCollat' => 'utf8mb4_general_ci',
    ],

    // API Testing Configuration
    'api' => [
        'base_url' => 'http://localhost:8080/api/v1',
        'timeout' => 10,
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
        'test_endpoints' => [
            // Authentication endpoints
            'auth' => [
                'login' => '/auth/login',
                'logout' => '/auth/logout',
                'refresh' => '/auth/refresh',
                'profile' => '/auth/profile',
            ],
            // User management
            'users' => [
                'list' => '/users',
                'create' => '/users',
                'show' => '/users/{id}',
                'update' => '/users/{id}',
                'delete' => '/users/{id}',
            ],
            // Master data
            'faculties' => [
                'list' => '/faculties',
                'create' => '/faculties',
                'show' => '/faculties/{id}',
                'update' => '/faculties/{id}',
                'delete' => '/faculties/{id}',
            ],
            'study_programs' => [
                'list' => '/study-programs',
                'create' => '/study-programs',
                'show' => '/study-programs/{id}',
                'update' => '/study-programs/{id}',
                'delete' => '/study-programs/{id}',
            ],
            // Course management
            'courses' => [
                'list' => '/courses',
                'create' => '/courses',
                'show' => '/courses/{id}',
                'update' => '/courses/{id}',
                'delete' => '/courses/{id}',
                'references' => '/courses/{id}/references',
                'topics' => '/courses/{id}/topics',
            ],
            // CPMK management
            'cpmk' => [
                'list' => '/cpmk',
                'create' => '/cpmk',
                'show' => '/cpmk/{id}',
                'update' => '/cpmk/{id}',
                'delete' => '/cpmk/{id}',
                'sub_cpmk' => '/cpmk/{id}/sub-cpmk',
                'cpl_relations' => '/cpmk/{id}/cpl-relations',
            ],
            // CPL management
            'cpl' => [
                'list' => '/cpl',
                'create' => '/cpl',
                'show' => '/cpl/{id}',
                'update' => '/cpl/{id}',
                'delete' => '/cpl/{id}',
            ],
            // Assessment management
            'assessments' => [
                'methods' => '/assessments/methods',
                'plans' => '/assessments/plans',
            ],
            // Reports and dashboard
            'reports' => [
                'cpmk_achievement' => '/reports/cpmk-achievement',
                'cpl_mapping' => '/reports/cpl-mapping',
                'dashboard' => '/reports/dashboard',
            ],
            'dashboard' => [
                'stats' => '/dashboard/stats',
            ],
        ],
    ],

    // Test Data Configuration
    'test_data' => [
        'users' => [
            'admin' => [
                'username' => 'test_admin',
                'email' => '<EMAIL>',
                'password' => 'test123456',
                'full_name' => 'Test Administrator',
                'role_id' => 1,
            ],
            'dosen' => [
                'username' => 'test_dosen',
                'email' => '<EMAIL>',
                'password' => 'test123456',
                'full_name' => 'Test Dosen',
                'role_id' => 2,
            ],
            'mahasiswa' => [
                'username' => 'test_mahasiswa',
                'email' => '<EMAIL>',
                'password' => 'test123456',
                'full_name' => 'Test Mahasiswa',
                'role_id' => 3,
            ],
        ],
        'faculty' => [
            'name' => 'Test Faculty',
            'code' => 'TF',
            'description' => 'Test Faculty for Testing',
        ],
        'study_program' => [
            'name' => 'Test Study Program',
            'code' => 'TSP',
            'description' => 'Test Study Program for Testing',
        ],
        'course' => [
            'code' => 'TEST101',
            'name' => 'Test Course',
            'credits' => 3,
            'semester' => 1,
            'description' => 'Test Course for Testing',
        ],
    ],

    // Performance Testing Thresholds
    'performance' => [
        'response_time' => [
            'excellent' => 100,  // ms
            'good' => 500,       // ms
            'acceptable' => 1000, // ms
            'poor' => 2000,      // ms
        ],
        'memory_usage' => [
            'max_mb' => 128,     // MB
        ],
        'concurrent_users' => 10,
    ],

    // Logging Configuration
    'logging' => [
        'level' => 'debug', // debug, info, warning, error
        'file_path' => __DIR__ . '/../reports/logs/',
        'max_file_size' => 10485760, // 10MB
        'backup_count' => 5,
        'formats' => [
            'console' => '[{timestamp}] {level}: {message}',
            'file' => '[{timestamp}] [{level}] {category}: {message}',
            'html' => '<div class="{level}">[{timestamp}] <strong>{level}</strong>: {message}</div>',
        ],
    ],

    // Report Generation
    'reports' => [
        'output_dir' => __DIR__ . '/../reports/',
        'formats' => ['html', 'json', 'txt'],
        'include_screenshots' => false,
        'include_performance_charts' => true,
    ],

    // Test Categories to Run
    'test_categories' => [
        '01_environment' => true,
        '02_database' => true,
        '03_authentication' => true,
        '04_api_endpoints' => true,
        '05_models' => true,
        '06_integration' => true,
        '07_performance' => true,
    ],

    // Required PHP Extensions
    'required_extensions' => [
        'pdo',
        'pdo_mysql',
        'mysqli',
        'json',
        'curl',
        'mbstring',
        'openssl',
        'intl',
    ],

    // Required PHP Version
    'required_php_version' => '8.1.0',

    // CodeIgniter Requirements
    'codeigniter' => [
        'min_version' => '4.0.0',
        'required_files' => [
            'app/Config/App.php',
            'app/Config/Database.php',
            'app/Config/Routes.php',
        ],
    ],
];
