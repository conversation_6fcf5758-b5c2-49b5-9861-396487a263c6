import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{I as n,bF as r,bJ as i,bK as a,bS as o,bd as s,bf as ee,bg as c,bh as l,bi as u,bj as d,bl as f,bm as p,bn as m,bq as h,bv as g,bx as _,bz as v,c as y,j as te,n as b,o as x,p as S,q as C,s as w,u as T}from"./index-BSnscBhv.js";import{i as E}from"./api-BWRuf0Vj.js";import{b as D,c as O,d as k,e as A}from"./VCard-DVRc-Pxh.js";import{b as j}from"./VChip-CBN0Kf2u.js";import{b as M}from"./VSelect-DqM1bu6y.js";import{b as N}from"./VDialog-VHlGBbps.js";import{b as ne}from"./VTextField-BU8lnKH2.js";import{b as P}from"./VSnackbar-KpoxlJmd.js";import{b as F,c as I,d as L,e as R}from"./VTabs-Dp1ayKKb.js";const z={class:`d-flex align-center justify-space-between`},B={class:`text-h4 font-weight-bold`},V={class:`d-flex align-center justify-space-between`},H={class:`text-h4 font-weight-bold`},U={class:`d-flex align-center justify-space-between`},W={class:`text-h4 font-weight-bold`},G={class:`d-flex align-center justify-space-between`},re={class:`text-h4 font-weight-bold`},K={class:`d-flex align-center`},ie={class:`text-center py-8`},ae={class:`d-flex align-center`},q={class:`d-flex align-center mb-2`},oe={class:`font-weight-medium`},se={class:`text-body-2 text-medium-emphasis mb-3`},ce={class:`d-flex align-center justify-space-between`},le={class:`text-caption text-medium-emphasis`};var J=m({__name:`ReportsDashboardTab`,emits:[`switchTab`],setup(ee){let u=a(!1),m=a(!1),h=a(!1),y=a(``),C=a(``),w=i({totalReports:0,monthlyReports:0,scheduledReports:0,templates:0}),M=a([]),N=a([]),ne=async()=>{u.value=!0;try{let[e,t,n]=await Promise.all([E.getDashboard(),E.getReportInstances({limit:5,sort:`generated_at`,order:`desc`}),E.getPopularReports({limit:6})]);Object.assign(w,e.data),M.value=t.data.data||[],N.value=n.data.data||[]}catch(e){C.value=`Failed to load dashboard data`,h.value=!0,console.error(`Load dashboard error:`,e)}finally{u.value=!1}},F=async()=>{try{await E.generateReport(1,{type:`cpmk_achievement`}),y.value=`CPMK Achievement report generation started`,m.value=!0}catch{C.value=`Failed to generate CPMK report`,h.value=!0}},I=async()=>{try{await E.generateReport(2,{type:`cpl_mapping`}),y.value=`CPL Mapping report generation started`,m.value=!0}catch{C.value=`Failed to generate CPL report`,h.value=!0}},L=async()=>{try{await E.generateReport(3,{type:`assessment_analytics`}),y.value=`Assessment Analytics report generation started`,m.value=!0}catch{C.value=`Failed to generate Assessment report`,h.value=!0}},R=async()=>{try{await E.generateReport(4,{type:`course_performance`}),y.value=`Course Performance report generation started`,m.value=!0}catch{C.value=`Failed to generate Course report`,h.value=!0}},J=async e=>{try{let t=await E.downloadReport(e),n=window.URL.createObjectURL(t.data),r=document.createElement(`a`);r.href=n,r.download=`report-${e}.pdf`,r.click(),window.URL.revokeObjectURL(n)}catch{C.value=`Failed to download report`,h.value=!0}},Y=async e=>{try{await E.generateReport(e,{}),y.value=`Report generation started from template`,m.value=!0}catch{C.value=`Failed to generate report from template`,h.value=!0}},X=e=>{console.log(`View template:`,e)},Z=e=>{let t={pending:`warning`,processing:`info`,completed:`success`,failed:`error`};return t[e]||`grey`},Q=e=>{let t={pending:`mdi-clock`,processing:`mdi-loading`,completed:`mdi-check-circle`,failed:`mdi-alert-circle`};return t[e]||`mdi-help-circle`},ue=e=>{let t={cpmk_achievement:`primary`,cpl_mapping:`secondary`,assessment_analytics:`success`,course_performance:`warning`,student_progress:`info`,custom:`purple`};return t[e]||`grey`},$=e=>{let t={cpmk_achievement:`mdi-target`,cpl_mapping:`mdi-bullseye-arrow`,assessment_analytics:`mdi-clipboard-check`,course_performance:`mdi-book-open-page-variant`,student_progress:`mdi-account-school`,custom:`mdi-file-document`};return t[e]||`mdi-file`},de=e=>{let t={academic:`blue`,administrative:`green`,analytics:`purple`,compliance:`orange`};return t[e]||`grey`},fe=e=>{let t={academic:`Academic`,administrative:`Administrative`,analytics:`Analytics`,compliance:`Compliance`};return t[e]||e},pe=e=>e?new Date(e).toLocaleDateString(`id-ID`,{day:`numeric`,month:`short`,year:`numeric`,hour:`2-digit`,minute:`2-digit`}):`-`;return g(()=>{ne()}),(i,a)=>(_(),d(`div`,null,[p(e,{class:`mb-6`},{default:r(()=>[p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(D,{color:`primary`,variant:`tonal`},{default:r(()=>[p(O,null,{default:r(()=>[c(`div`,z,[c(`div`,null,[a[6]||=c(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total Reports`,-1),c(`h2`,B,o(w.totalReports),1)]),p(n,{size:`48`,color:`primary`},{default:r(()=>a[7]||=[f(`mdi-file-chart`,-1)]),_:1,__:[7]})])]),_:1})]),_:1})]),_:1}),p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(D,{color:`success`,variant:`tonal`},{default:r(()=>[p(O,null,{default:r(()=>[c(`div`,V,[c(`div`,null,[a[8]||=c(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`This Month`,-1),c(`h2`,H,o(w.monthlyReports),1)]),p(n,{size:`48`,color:`success`},{default:r(()=>a[9]||=[f(`mdi-calendar-month`,-1)]),_:1,__:[9]})])]),_:1})]),_:1})]),_:1}),p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(D,{color:`warning`,variant:`tonal`},{default:r(()=>[p(O,null,{default:r(()=>[c(`div`,U,[c(`div`,null,[a[10]||=c(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Scheduled`,-1),c(`h2`,W,o(w.scheduledReports),1)]),p(n,{size:`48`,color:`warning`},{default:r(()=>a[11]||=[f(`mdi-calendar-clock`,-1)]),_:1,__:[11]})])]),_:1})]),_:1})]),_:1}),p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(D,{color:`info`,variant:`tonal`},{default:r(()=>[p(O,null,{default:r(()=>[c(`div`,G,[c(`div`,null,[a[12]||=c(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Templates`,-1),c(`h2`,re,o(w.templates),1)]),p(n,{size:`48`,color:`info`},{default:r(()=>a[13]||=[f(`mdi-file-document-multiple`,-1)]),_:1,__:[13]})])]),_:1})]),_:1})]),_:1})]),_:1}),p(e,{class:`mb-6`},{default:r(()=>[p(t,{cols:`12`},{default:r(()=>[p(D,null,{default:r(()=>[p(k,{class:`d-flex align-center`},{default:r(()=>[p(n,{class:`mr-2`},{default:r(()=>a[14]||=[f(`mdi-lightning-bolt`,-1)]),_:1,__:[14]}),a[15]||=f(` Quick Actions `,-1)]),_:1,__:[15]}),p(O,null,{default:r(()=>[p(e,null,{default:r(()=>[p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(T,{block:``,color:`primary`,size:`large`,onClick:F},{default:r(()=>[p(n,{start:``},{default:r(()=>a[16]||=[f(`mdi-target`,-1)]),_:1,__:[16]}),a[17]||=f(` CPMK Achievement `,-1)]),_:1,__:[17]})]),_:1}),p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(T,{block:``,color:`secondary`,size:`large`,onClick:I},{default:r(()=>[p(n,{start:``},{default:r(()=>a[18]||=[f(`mdi-bullseye-arrow`,-1)]),_:1,__:[18]}),a[19]||=f(` CPL Mapping `,-1)]),_:1,__:[19]})]),_:1}),p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(T,{block:``,color:`success`,size:`large`,onClick:L},{default:r(()=>[p(n,{start:``},{default:r(()=>a[20]||=[f(`mdi-clipboard-check`,-1)]),_:1,__:[20]}),a[21]||=f(` Assessment Analytics `,-1)]),_:1,__:[21]})]),_:1}),p(t,{cols:`12`,sm:`6`,md:`3`},{default:r(()=>[p(T,{block:``,color:`warning`,size:`large`,onClick:R},{default:r(()=>[p(n,{start:``},{default:r(()=>a[22]||=[f(`mdi-book-open-page-variant`,-1)]),_:1,__:[22]}),a[23]||=f(` Course Performance `,-1)]),_:1,__:[23]})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),p(e,{class:`mb-6`},{default:r(()=>[p(t,{cols:`12`,md:`8`},{default:r(()=>[p(D,null,{default:r(()=>[p(k,{class:`d-flex align-center justify-space-between`},{default:r(()=>[c(`div`,K,[p(n,{class:`mr-2`},{default:r(()=>a[24]||=[f(`mdi-history`,-1)]),_:1,__:[24]}),a[25]||=f(` Recent Reports `,-1)]),p(T,{variant:`text`,size:`small`,onClick:a[0]||=e=>i.$emit(`switchTab`,`instances`)},{default:r(()=>a[26]||=[f(` View All `,-1)]),_:1,__:[26]})]),_:1}),p(O,null,{default:r(()=>[p(te,null,{default:r(()=>[(_(!0),d(s,null,v(M.value,e=>(_(),l(b,{key:e.id,class:`px-0`},{prepend:r(()=>[p(n,{color:Z(e.status)},{default:r(()=>[f(o(Q(e.status)),1)]),_:2},1032,[`color`])]),append:r(()=>[p(T,{icon:``,size:`small`,variant:`text`,onClick:t=>J(e.id),disabled:e.status!==`completed`},{default:r(()=>[p(n,null,{default:r(()=>a[27]||=[f(`mdi-download`,-1)]),_:1,__:[27]})]),_:2},1032,[`onClick`,`disabled`])]),default:r(()=>[p(x,null,{default:r(()=>[f(o(e.name),1)]),_:2},1024),p(S,null,{default:r(()=>[f(o(pe(e.generated_at))+` • `+o(e.format.toUpperCase()),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1}),p(t,{cols:`12`,md:`4`},{default:r(()=>[p(D,null,{default:r(()=>[p(k,{class:`d-flex align-center`},{default:r(()=>[p(n,{class:`mr-2`},{default:r(()=>a[28]||=[f(`mdi-chart-pie`,-1)]),_:1,__:[28]}),a[29]||=f(` Report Types Distribution `,-1)]),_:1,__:[29]}),p(O,null,{default:r(()=>[c(`div`,ie,[p(n,{size:`64`,color:`grey-lighten-2`},{default:r(()=>a[30]||=[f(`mdi-chart-pie`,-1)]),_:1,__:[30]}),a[31]||=c(`p`,{class:`text-body-2 text-medium-emphasis mt-4`},` Chart showing distribution of report types `,-1),a[32]||=c(`p`,{class:`text-caption text-medium-emphasis`},` Chart visualization will be implemented in the next phase `,-1)])]),_:1})]),_:1})]),_:1})]),_:1}),p(e,null,{default:r(()=>[p(t,{cols:`12`},{default:r(()=>[p(D,null,{default:r(()=>[p(k,{class:`d-flex align-center justify-space-between`},{default:r(()=>[c(`div`,ae,[p(n,{class:`mr-2`},{default:r(()=>a[33]||=[f(`mdi-star`,-1)]),_:1,__:[33]}),a[34]||=f(` Popular Report Templates `,-1)]),p(T,{variant:`text`,size:`small`,onClick:a[1]||=e=>i.$emit(`switchTab`,`templates`)},{default:r(()=>a[35]||=[f(` View All Templates `,-1)]),_:1,__:[35]})]),_:1}),p(O,null,{default:r(()=>[p(e,null,{default:r(()=>[(_(!0),d(s,null,v(N.value,e=>(_(),l(t,{key:e.id,cols:`12`,sm:`6`,md:`4`},{default:r(()=>[p(D,{variant:`outlined`,class:`h-100`},{default:r(()=>[p(O,null,{default:r(()=>[c(`div`,q,[p(n,{color:ue(e.type),class:`mr-2`},{default:r(()=>[f(o($(e.type)),1)]),_:2},1032,[`color`]),c(`span`,oe,o(e.name),1)]),c(`p`,se,o(e.description),1),c(`div`,ce,[p(j,{color:de(e.category),size:`small`,variant:`tonal`},{default:r(()=>[f(o(fe(e.category)),1)]),_:2},1032,[`color`]),c(`div`,le,o(e.usage_count)+` uses `,1)])]),_:2},1024),p(A,null,{default:r(()=>[p(T,{color:`primary`,variant:`text`,size:`small`,onClick:t=>Y(e.id)},{default:r(()=>a[36]||=[f(` Generate `,-1)]),_:2,__:[36]},1032,[`onClick`]),p(T,{variant:`text`,size:`small`,onClick:t=>X(e.id)},{default:r(()=>a[37]||=[f(` View `,-1)]),_:2,__:[37]},1032,[`onClick`])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),p(P,{modelValue:m.value,"onUpdate:modelValue":a[3]||=e=>m.value=e,color:`success`,timeout:`3000`},{actions:r(()=>[p(T,{onClick:a[2]||=e=>m.value=!1},{default:r(()=>a[38]||=[f(`Close`,-1)]),_:1,__:[38]})]),default:r(()=>[f(o(y.value)+` `,1)]),_:1},8,[`modelValue`]),p(P,{modelValue:h.value,"onUpdate:modelValue":a[5]||=e=>h.value=e,color:`error`,timeout:`5000`},{actions:r(()=>[p(T,{onClick:a[4]||=e=>h.value=!1},{default:r(()=>a[39]||=[f(`Close`,-1)]),_:1,__:[39]})]),default:r(()=>[f(o(C.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),Y=J;const X={class:`text-center py-8`},Z={class:`text-body-2 text-medium-emphasis`};var Q=m({__name:`ReportParametersForm`,props:{report:{},modelValue:{}},emits:[`update:modelValue`],setup(e){return(e,t)=>(_(),d(`div`,null,[c(`div`,X,[p(n,{size:`48`,color:`grey-lighten-2`},{default:r(()=>t[0]||=[f(`mdi-form-select`,-1)]),_:1,__:[0]}),t[1]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Report Parameters Form`,-1),c(`p`,Z,` Dynamic form for configuring report parameters for `+o(e.report.name),1),t[2]||=c(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),ue=Q;const $={class:`text-center py-8`},de={class:`text-body-2 text-medium-emphasis`};var fe=m({__name:`ReportPreview`,props:{report:{}},setup(e){return(e,t)=>(_(),d(`div`,null,[c(`div`,$,[p(n,{size:`48`,color:`grey-lighten-2`},{default:r(()=>t[0]||=[f(`mdi-eye`,-1)]),_:1,__:[0]}),t[1]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Report Preview`,-1),c(`p`,de,` Live preview of `+o(e.report.name)+` with sample data and formatting `,1),t[2]||=c(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),pe=fe;const me={class:`d-flex align-center mb-3`},he={class:`flex-grow-1`},ge={class:`text-h6 font-weight-bold`},_e={class:`text-body-2 text-medium-emphasis mb-4`},ve={class:`d-flex align-center justify-space-between mb-3`},ye={class:`text-caption text-medium-emphasis`},be={class:`text-caption text-medium-emphasis`},xe={class:`mb-3`},Se={class:`d-flex gap-1`},Ce={key:0,class:`text-center py-12`},we={class:`d-flex align-center`};var Te=m({__name:`PrebuiltReportsTab`,setup(i){a(!1);let m=a(!1),S=a(!1),F=a(!1),I=a(!1),L=a(!1),R=a(``),z=a(``),B=a(``),V=a(``),H=a(``),U=a(null),W=a({}),G=a(new Set),re=a([{id:1,name:`CPMK Achievement Report`,description:`Comprehensive analysis of Course Learning Outcomes achievement across all courses`,type:`cpmk_achievement`,category:`academic`,usage_count:45,estimated_time:`3-5`,export_formats:[`pdf`,`excel`,`csv`]},{id:2,name:`CPL Mapping Matrix`,description:`Visual mapping between Graduate Learning Outcomes and Course Learning Outcomes`,type:`cpl_mapping`,category:`academic`,usage_count:32,estimated_time:`2-4`,export_formats:[`pdf`,`excel`]},{id:3,name:`Assessment Analytics`,description:`Detailed analysis of assessment methods, performance, and trends`,type:`assessment_analytics`,category:`analytics`,usage_count:28,estimated_time:`4-6`,export_formats:[`pdf`,`excel`,`csv`]},{id:4,name:`Course Performance Dashboard`,description:`Performance metrics and analytics for individual courses`,type:`course_performance`,category:`analytics`,usage_count:38,estimated_time:`2-3`,export_formats:[`pdf`,`excel`]},{id:5,name:`Student Progress Tracking`,description:`Individual and cohort student progress analysis`,type:`student_progress`,category:`academic`,usage_count:22,estimated_time:`3-5`,export_formats:[`pdf`,`excel`,`csv`]},{id:6,name:`Faculty Overview Report`,description:`Comprehensive faculty performance and resource utilization`,type:`faculty_overview`,category:`administrative`,usage_count:15,estimated_time:`5-8`,export_formats:[`pdf`,`excel`]},{id:7,name:`Compliance Audit Report`,description:`Academic compliance and accreditation readiness assessment`,type:`compliance_audit`,category:`compliance`,usage_count:12,estimated_time:`6-10`,export_formats:[`pdf`]},{id:8,name:`Grade Distribution Analysis`,description:`Statistical analysis of grade distributions across courses and programs`,type:`grade_distribution`,category:`analytics`,usage_count:25,estimated_time:`3-4`,export_formats:[`pdf`,`excel`,`csv`]}]),K=ee(()=>{let e=re.value;if(B.value&&(e=e.filter(e=>e.category===B.value)),V.value&&(e=e.filter(e=>e.type===V.value)),H.value){let t=H.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t))}return e}),ie=[{title:`Academic`,value:`academic`},{title:`Administrative`,value:`administrative`},{title:`Analytics`,value:`analytics`},{title:`Compliance`,value:`compliance`}],ae=[{title:`CPMK Achievement`,value:`cpmk_achievement`},{title:`CPL Mapping`,value:`cpl_mapping`},{title:`Assessment Analytics`,value:`assessment_analytics`},{title:`Course Performance`,value:`course_performance`},{title:`Student Progress`,value:`student_progress`},{title:`Faculty Overview`,value:`faculty_overview`},{title:`Compliance Audit`,value:`compliance_audit`},{title:`Grade Distribution`,value:`grade_distribution`}],q=()=>{},oe=e=>{U.value=e,W.value={},S.value=!0},se=async()=>{if(U.value){m.value=!0,G.value.add(U.value.id);try{await E.generateReport(U.value.id,W.value),R.value=`${U.value.name} generation started successfully`,I.value=!0,S.value=!1}catch(e){z.value=e.response?.data?.message||`Failed to generate report`,L.value=!0}finally{m.value=!1,G.value.delete(U.value.id)}}},ce=e=>{U.value=e,F.value=!0},le=e=>{console.log(`Schedule report:`,e.name)},J=e=>{console.log(`Share report:`,e.name)},Y=e=>{console.log(`View analytics for:`,e.name)},X=e=>{let t={cpmk_achievement:`primary`,cpl_mapping:`secondary`,assessment_analytics:`success`,course_performance:`warning`,student_progress:`info`,faculty_overview:`purple`,compliance_audit:`orange`,grade_distribution:`teal`};return t[e]||`grey`},Z=e=>{let t={cpmk_achievement:`mdi-target`,cpl_mapping:`mdi-bullseye-arrow`,assessment_analytics:`mdi-clipboard-check`,course_performance:`mdi-book-open-page-variant`,student_progress:`mdi-account-school`,faculty_overview:`mdi-account-group`,compliance_audit:`mdi-shield-check`,grade_distribution:`mdi-chart-histogram`};return t[e||``]||`mdi-file-chart`},Q=e=>{let t={academic:`blue`,administrative:`green`,analytics:`purple`,compliance:`orange`};return t[e]||`grey`},$=e=>{let t={academic:`Academic`,administrative:`Administrative`,analytics:`Analytics`,compliance:`Compliance`};return t[e]||e};return g(()=>{}),(i,a)=>(_(),d(`div`,null,[p(e,{class:`mb-6`},{default:r(()=>[p(t,{cols:`12`,md:`4`},{default:r(()=>[p(M,{modelValue:B.value,"onUpdate:modelValue":[a[0]||=e=>B.value=e,q],items:ie,label:`Report Category`,variant:`outlined`,"prepend-inner-icon":`mdi-tag`,clearable:``},null,8,[`modelValue`])]),_:1}),p(t,{cols:`12`,md:`4`},{default:r(()=>[p(M,{modelValue:V.value,"onUpdate:modelValue":[a[1]||=e=>V.value=e,q],items:ae,label:`Report Type`,variant:`outlined`,"prepend-inner-icon":`mdi-file-chart`,clearable:``},null,8,[`modelValue`])]),_:1}),p(t,{cols:`12`,md:`4`},{default:r(()=>[p(ne,{modelValue:H.value,"onUpdate:modelValue":[a[2]||=e=>H.value=e,q],label:`Search Reports`,variant:`outlined`,"prepend-inner-icon":`mdi-magnify`,clearable:``},null,8,[`modelValue`])]),_:1})]),_:1}),p(e,null,{default:r(()=>[(_(!0),d(s,null,v(K.value,e=>(_(),l(t,{key:e.id,cols:`12`,sm:`6`,md:`4`,lg:`3`},{default:r(()=>[p(D,{class:`h-100`,variant:`outlined`},{default:r(()=>[p(O,null,{default:r(()=>[c(`div`,me,[p(n,{color:X(e.type),size:`32`,class:`mr-3`},{default:r(()=>[f(o(Z(e.type)),1)]),_:2},1032,[`color`]),c(`div`,he,[c(`h3`,ge,o(e.name),1),p(j,{color:Q(e.category),size:`small`,variant:`tonal`},{default:r(()=>[f(o($(e.category)),1)]),_:2},1032,[`color`])])]),c(`p`,_e,o(e.description),1),c(`div`,ve,[c(`div`,ye,[p(n,{size:`small`,class:`mr-1`},{default:r(()=>a[12]||=[f(`mdi-download`,-1)]),_:1,__:[12]}),f(` `+o(e.usage_count||0)+` downloads `,1)]),c(`div`,be,[p(n,{size:`small`,class:`mr-1`},{default:r(()=>a[13]||=[f(`mdi-clock`,-1)]),_:1,__:[13]}),f(` ~`+o(e.estimated_time||`2-5`)+` min `,1)])]),c(`div`,xe,[a[14]||=c(`div`,{class:`text-caption text-medium-emphasis mb-1`},`Export Formats:`,-1),c(`div`,Se,[(_(!0),d(s,null,v(e.export_formats,e=>(_(),l(j,{key:e,size:`x-small`,variant:`outlined`},{default:r(()=>[f(o(e.toUpperCase()),1)]),_:2},1024))),128))])])]),_:2},1024),p(A,null,{default:r(()=>[p(T,{color:`primary`,variant:`flat`,size:`small`,onClick:t=>oe(e),loading:G.value.has(e.id)},{default:r(()=>[p(n,{start:``},{default:r(()=>a[15]||=[f(`mdi-play`,-1)]),_:1,__:[15]}),a[16]||=f(` Generate `,-1)]),_:2,__:[16]},1032,[`onClick`,`loading`]),p(T,{variant:`text`,size:`small`,onClick:t=>ce(e)},{default:r(()=>[p(n,{start:``},{default:r(()=>a[17]||=[f(`mdi-eye`,-1)]),_:1,__:[17]}),a[18]||=f(` Preview `,-1)]),_:2,__:[18]},1032,[`onClick`]),p(C),p(y,null,{activator:r(({props:e})=>[p(T,h({icon:``,size:`small`,variant:`text`},{ref_for:!0},e),{default:r(()=>[p(n,null,{default:r(()=>a[19]||=[f(`mdi-dots-vertical`,-1)]),_:1,__:[19]})]),_:2},1040)]),default:r(()=>[p(te,null,{default:r(()=>[p(b,{onClick:t=>le(e)},{prepend:r(()=>[p(n,null,{default:r(()=>a[21]||=[f(`mdi-calendar-clock`,-1)]),_:1,__:[21]})]),default:r(()=>[p(x,null,{default:r(()=>a[20]||=[f(`Schedule`,-1)]),_:1,__:[20]})]),_:2},1032,[`onClick`]),p(b,{onClick:t=>J(e)},{prepend:r(()=>[p(n,null,{default:r(()=>a[23]||=[f(`mdi-share`,-1)]),_:1,__:[23]})]),default:r(()=>[p(x,null,{default:r(()=>a[22]||=[f(`Share`,-1)]),_:1,__:[22]})]),_:2},1032,[`onClick`]),p(b,{onClick:t=>Y(e)},{prepend:r(()=>[p(n,null,{default:r(()=>a[25]||=[f(`mdi-chart-bar`,-1)]),_:1,__:[25]})]),default:r(()=>[p(x,null,{default:r(()=>a[24]||=[f(`Analytics`,-1)]),_:1,__:[24]})]),_:2},1032,[`onClick`])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),K.value.length===0?(_(),d(`div`,Ce,[p(n,{size:`64`,color:`grey-lighten-2`},{default:r(()=>a[26]||=[f(`mdi-file-search`,-1)]),_:1,__:[26]}),a[27]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`No Reports Found`,-1),a[28]||=c(`p`,{class:`text-body-2 text-medium-emphasis`},` Try adjusting your filters or search criteria `,-1)])):u(``,!0),p(N,{modelValue:S.value,"onUpdate:modelValue":a[5]||=e=>S.value=e,"max-width":`600`},{default:r(()=>[p(D,null,{default:r(()=>[p(k,{class:`d-flex align-center`},{default:r(()=>[p(n,{class:`mr-2`},{default:r(()=>[f(o(Z(U.value?.type)),1)]),_:1}),f(` Generate `+o(U.value?.name),1)]),_:1}),p(w),p(O,null,{default:r(()=>[U.value?(_(),l(ue,{key:0,report:U.value,modelValue:W.value,"onUpdate:modelValue":a[3]||=e=>W.value=e},null,8,[`report`,`modelValue`])):u(``,!0)]),_:1}),p(A,null,{default:r(()=>[p(C),p(T,{onClick:a[4]||=e=>S.value=!1},{default:r(()=>a[29]||=[f(`Cancel`,-1)]),_:1,__:[29]}),p(T,{color:`primary`,loading:m.value,onClick:se},{default:r(()=>a[30]||=[f(` Generate Report `,-1)]),_:1,__:[30]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),p(N,{modelValue:F.value,"onUpdate:modelValue":a[7]||=e=>F.value=e,"max-width":`1000`,scrollable:``},{default:r(()=>[p(D,null,{default:r(()=>[p(k,{class:`d-flex align-center justify-space-between`},{default:r(()=>[c(`div`,we,[p(n,{class:`mr-2`},{default:r(()=>a[31]||=[f(`mdi-eye`,-1)]),_:1,__:[31]}),f(` Preview: `+o(U.value?.name),1)]),p(T,{icon:``,variant:`text`,onClick:a[6]||=e=>F.value=!1},{default:r(()=>[p(n,null,{default:r(()=>a[32]||=[f(`mdi-close`,-1)]),_:1,__:[32]})]),_:1})]),_:1}),p(w),p(O,null,{default:r(()=>[U.value?(_(),l(pe,{key:0,report:U.value},null,8,[`report`])):u(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),p(P,{modelValue:I.value,"onUpdate:modelValue":a[9]||=e=>I.value=e,color:`success`,timeout:`3000`},{actions:r(()=>[p(T,{onClick:a[8]||=e=>I.value=!1},{default:r(()=>a[33]||=[f(`Close`,-1)]),_:1,__:[33]})]),default:r(()=>[f(o(R.value)+` `,1)]),_:1},8,[`modelValue`]),p(P,{modelValue:L.value,"onUpdate:modelValue":a[11]||=e=>L.value=e,color:`error`,timeout:`5000`},{actions:r(()=>[p(T,{onClick:a[10]||=e=>L.value=!1},{default:r(()=>a[34]||=[f(`Close`,-1)]),_:1,__:[34]})]),default:r(()=>[f(o(z.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),Ee=Te;const De={class:`text-center py-12`};var Oe=m({__name:`ReportTemplatesTab`,setup(e){return(e,t)=>(_(),d(`div`,null,[c(`div`,De,[p(n,{size:`64`,color:`grey-lighten-2`},{default:r(()=>t[0]||=[f(`mdi-file-document-multiple`,-1)]),_:1,__:[0]}),t[1]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Report Templates Management`,-1),t[2]||=c(`p`,{class:`text-body-2 text-medium-emphasis`},` Create, edit, and manage custom report templates with advanced configuration options `,-1),t[3]||=c(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),ke=Oe;const Ae={class:`text-center py-12`};var je=m({__name:`GeneratedReportsTab`,setup(e){return(e,t)=>(_(),d(`div`,null,[c(`div`,Ae,[p(n,{size:`64`,color:`grey-lighten-2`},{default:r(()=>t[0]||=[f(`mdi-history`,-1)]),_:1,__:[0]}),t[1]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Generated Reports History`,-1),t[2]||=c(`p`,{class:`text-body-2 text-medium-emphasis`},` View, download, and manage all previously generated reports with status tracking `,-1),t[3]||=c(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),Me=je;const Ne={class:`text-center py-12`};var Pe=m({__name:`ScheduledReportsTab`,setup(e){return(e,t)=>(_(),d(`div`,null,[c(`div`,Ne,[p(n,{size:`64`,color:`grey-lighten-2`},{default:r(()=>t[0]||=[f(`mdi-calendar-clock`,-1)]),_:1,__:[0]}),t[1]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Scheduled Reports Management`,-1),t[2]||=c(`p`,{class:`text-body-2 text-medium-emphasis`},` Set up automated report generation with flexible scheduling options and email delivery `,-1),t[3]||=c(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),Fe=Pe;const Ie={class:`text-center py-12`};var Le=m({__name:`ReportsAnalyticsTab`,setup(e){return(e,t)=>(_(),d(`div`,null,[c(`div`,Ie,[p(n,{size:`64`,color:`grey-lighten-2`},{default:r(()=>t[0]||=[f(`mdi-chart-line`,-1)]),_:1,__:[0]}),t[1]||=c(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Reports Usage Analytics`,-1),t[2]||=c(`p`,{class:`text-body-2 text-medium-emphasis`},` Analyze report usage patterns, performance metrics, and user engagement statistics `,-1),t[3]||=c(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),Re=Le,ze=m({__name:`ReportsView`,setup(i){let o=a(`dashboard`);return(i,a)=>(_(),d(`div`,null,[p(e,{class:`mb-6`},{default:r(()=>[p(t,null,{default:r(()=>a[2]||=[c(`h1`,{class:`text-h4 font-weight-bold text-primary`},`Reports & Analytics`,-1),c(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Comprehensive reporting system with analytics, export functionality, and automated scheduling `,-1)]),_:1,__:[2]})]),_:1}),p(F,{modelValue:o.value,"onUpdate:modelValue":a[0]||=e=>o.value=e,color:`primary`,class:`mb-6`},{default:r(()=>[p(R,{value:`dashboard`},{default:r(()=>[p(n,{start:``},{default:r(()=>a[3]||=[f(`mdi-view-dashboard`,-1)]),_:1,__:[3]}),a[4]||=f(` Dashboard `,-1)]),_:1,__:[4]}),p(R,{value:`prebuilt`},{default:r(()=>[p(n,{start:``},{default:r(()=>a[5]||=[f(`mdi-file-chart`,-1)]),_:1,__:[5]}),a[6]||=f(` Pre-built Reports `,-1)]),_:1,__:[6]}),p(R,{value:`templates`},{default:r(()=>[p(n,{start:``},{default:r(()=>a[7]||=[f(`mdi-file-document-multiple`,-1)]),_:1,__:[7]}),a[8]||=f(` Report Templates `,-1)]),_:1,__:[8]}),p(R,{value:`instances`},{default:r(()=>[p(n,{start:``},{default:r(()=>a[9]||=[f(`mdi-history`,-1)]),_:1,__:[9]}),a[10]||=f(` Generated Reports `,-1)]),_:1,__:[10]}),p(R,{value:`scheduled`},{default:r(()=>[p(n,{start:``},{default:r(()=>a[11]||=[f(`mdi-calendar-clock`,-1)]),_:1,__:[11]}),a[12]||=f(` Scheduled Reports `,-1)]),_:1,__:[12]}),p(R,{value:`analytics`},{default:r(()=>[p(n,{start:``},{default:r(()=>a[13]||=[f(`mdi-chart-line`,-1)]),_:1,__:[13]}),a[14]||=f(` Usage Analytics `,-1)]),_:1,__:[14]})]),_:1},8,[`modelValue`]),p(L,{modelValue:o.value,"onUpdate:modelValue":a[1]||=e=>o.value=e},{default:r(()=>[p(I,{value:`dashboard`},{default:r(()=>[p(Y)]),_:1}),p(I,{value:`prebuilt`},{default:r(()=>[p(Ee)]),_:1}),p(I,{value:`templates`},{default:r(()=>[p(ke)]),_:1}),p(I,{value:`instances`},{default:r(()=>[p(Me)]),_:1}),p(I,{value:`scheduled`},{default:r(()=>[p(Fe)]),_:1}),p(I,{value:`analytics`},{default:r(()=>[p(Re)]),_:1})]),_:1},8,[`modelValue`])]))}}),Be=ze;export{Be as default};