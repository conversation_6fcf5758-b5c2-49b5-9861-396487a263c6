import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{E as n,H as r,I as i,L as a,ai as o,b as s,bB as c,bD as l,bF as u,bK as d,bO as f,bQ as p,bS as m,bd as h,bf as g,bg as _,bh as v,bi as y,bj as b,bl as x,bm as S,bn as C,bv as w,bw as T,bx as E,bz as D,j as O,n as ee,o as te,p as ne,t as re,u as ie}from"./index-BSnscBhv.js";import{g as ae}from"./api-BWRuf0Vj.js";import{b as oe,c as se,d as ce}from"./VCard-DVRc-Pxh.js";import{b as le}from"./VChip-CBN0Kf2u.js";import{b as ue,c as de}from"./VTimeline-uQEqPSC6.js";
/*!
* @kurkle/color v0.3.4
* https://github.com/kurkle/color#readme
* (c) 2024 Ju<PERSON>
* Released under the MIT License
*/
function k(e){return e+.5|0}const A=(e,t,n)=>Math.max(Math.min(e,n),t);function fe(e){return A(k(e*2.55),0,255)}function j(e){return A(k(e*255),0,255)}function M(e){return A(k(e/2.55)/100,0,1)}function pe(e){return A(k(e*100),0,100)}const N={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},me=[...`0123456789ABCDEF`],he=e=>me[e&15],ge=e=>me[(e&240)>>4]+me[e&15],_e=e=>(e&240)>>4==(e&15),ve=e=>_e(e.r)&&_e(e.g)&&_e(e.b)&&_e(e.a);function ye(e){var t=e.length,n;return e[0]===`#`&&(t===4||t===5?n={r:255&N[e[1]]*17,g:255&N[e[2]]*17,b:255&N[e[3]]*17,a:t===5?N[e[4]]*17:255}:(t===7||t===9)&&(n={r:N[e[1]]<<4|N[e[2]],g:N[e[3]]<<4|N[e[4]],b:N[e[5]]<<4|N[e[6]],a:t===9?N[e[7]]<<4|N[e[8]]:255})),n}const be=(e,t)=>e<255?t(e):``;function xe(e){var t=ve(e)?he:ge;return e?`#`+t(e.r)+t(e.g)+t(e.b)+be(e.a,t):void 0}const Se=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Ce(e,t,n){let r=t*Math.min(n,1-n),i=(t,i=(t+e/30)%12)=>n-r*Math.max(Math.min(i-3,9-i,1),-1);return[i(0),i(8),i(4)]}function we(e,t,n){let r=(r,i=(r+e/60)%6)=>n-n*t*Math.max(Math.min(i,4-i,1),0);return[r(5),r(3),r(1)]}function Te(e,t,n){let r=Ce(e,1,.5),i;for(t+n>1&&(i=1/(t+n),t*=i,n*=i),i=0;i<3;i++)r[i]*=1-t-n,r[i]+=t;return r}function Ee(e,t,n,r,i){return e===i?(t-n)/r+(t<n?6:0):t===i?(n-e)/r+2:(e-t)/r+4}function De(e){let t=255,n=e.r/t,r=e.g/t,i=e.b/t,a=Math.max(n,r,i),o=Math.min(n,r,i),s=(a+o)/2,c,l,u;return a!==o&&(u=a-o,l=s>.5?u/(2-a-o):u/(a+o),c=Ee(n,r,i,u,a),c=c*60+.5),[c|0,l||0,s]}function Oe(e,t,n,r){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,r)).map(j)}function ke(e,t,n){return Oe(Ce,e,t,n)}function Ae(e,t,n){return Oe(Te,e,t,n)}function je(e,t,n){return Oe(we,e,t,n)}function Me(e){return(e%360+360)%360}function Ne(e){let t=Se.exec(e),n=255,r;if(!t)return;t[5]!==r&&(n=t[6]?fe(+t[5]):j(+t[5]));let i=Me(+t[2]),a=t[3]/100,o=t[4]/100;return r=t[1]===`hwb`?Ae(i,a,o):t[1]===`hsv`?je(i,a,o):ke(i,a,o),{r:r[0],g:r[1],b:r[2],a:n}}function Pe(e,t){var n=De(e);n[0]=Me(n[0]+t),n=ke(n),e.r=n[0],e.g=n[1],e.b=n[2]}function Fe(e){if(!e)return;let t=De(e),n=t[0],r=pe(t[1]),i=pe(t[2]);return e.a<255?`hsla(${n}, ${r}%, ${i}%, ${M(e.a)})`:`hsl(${n}, ${r}%, ${i}%)`}const Ie={x:`dark`,Z:`light`,Y:`re`,X:`blu`,W:`gr`,V:`medium`,U:`slate`,A:`ee`,T:`ol`,S:`or`,B:`ra`,C:`lateg`,D:`ights`,R:`in`,Q:`turquois`,E:`hi`,P:`ro`,O:`al`,N:`le`,M:`de`,L:`yello`,F:`en`,K:`ch`,G:`arks`,H:`ea`,I:`ightg`,J:`wh`},Le={OiceXe:`f0f8ff`,antiquewEte:`faebd7`,aqua:`ffff`,aquamarRe:`7fffd4`,azuY:`f0ffff`,beige:`f5f5dc`,bisque:`ffe4c4`,black:`0`,blanKedOmond:`ffebcd`,Xe:`ff`,XeviTet:`8a2be2`,bPwn:`a52a2a`,burlywood:`deb887`,caMtXe:`5f9ea0`,KartYuse:`7fff00`,KocTate:`d2691e`,cSO:`ff7f50`,cSnflowerXe:`6495ed`,cSnsilk:`fff8dc`,crimson:`dc143c`,cyan:`ffff`,xXe:`8b`,xcyan:`8b8b`,xgTMnPd:`b8860b`,xWay:`a9a9a9`,xgYF:`6400`,xgYy:`a9a9a9`,xkhaki:`bdb76b`,xmagFta:`8b008b`,xTivegYF:`556b2f`,xSange:`ff8c00`,xScEd:`9932cc`,xYd:`8b0000`,xsOmon:`e9967a`,xsHgYF:`8fbc8f`,xUXe:`483d8b`,xUWay:`2f4f4f`,xUgYy:`2f4f4f`,xQe:`ced1`,xviTet:`9400d3`,dAppRk:`ff1493`,dApskyXe:`bfff`,dimWay:`696969`,dimgYy:`696969`,dodgerXe:`1e90ff`,fiYbrick:`b22222`,flSOwEte:`fffaf0`,foYstWAn:`228b22`,fuKsia:`ff00ff`,gaRsbSo:`dcdcdc`,ghostwEte:`f8f8ff`,gTd:`ffd700`,gTMnPd:`daa520`,Way:`808080`,gYF:`8000`,gYFLw:`adff2f`,gYy:`808080`,honeyMw:`f0fff0`,hotpRk:`ff69b4`,RdianYd:`cd5c5c`,Rdigo:`4b0082`,ivSy:`fffff0`,khaki:`f0e68c`,lavFMr:`e6e6fa`,lavFMrXsh:`fff0f5`,lawngYF:`7cfc00`,NmoncEffon:`fffacd`,ZXe:`add8e6`,ZcSO:`f08080`,Zcyan:`e0ffff`,ZgTMnPdLw:`fafad2`,ZWay:`d3d3d3`,ZgYF:`90ee90`,ZgYy:`d3d3d3`,ZpRk:`ffb6c1`,ZsOmon:`ffa07a`,ZsHgYF:`20b2aa`,ZskyXe:`87cefa`,ZUWay:`778899`,ZUgYy:`778899`,ZstAlXe:`b0c4de`,ZLw:`ffffe0`,lime:`ff00`,limegYF:`32cd32`,lRF:`faf0e6`,magFta:`ff00ff`,maPon:`800000`,VaquamarRe:`66cdaa`,VXe:`cd`,VScEd:`ba55d3`,VpurpN:`9370db`,VsHgYF:`3cb371`,VUXe:`7b68ee`,VsprRggYF:`fa9a`,VQe:`48d1cc`,VviTetYd:`c71585`,midnightXe:`191970`,mRtcYam:`f5fffa`,mistyPse:`ffe4e1`,moccasR:`ffe4b5`,navajowEte:`ffdead`,navy:`80`,Tdlace:`fdf5e6`,Tive:`808000`,TivedBb:`6b8e23`,Sange:`ffa500`,SangeYd:`ff4500`,ScEd:`da70d6`,pOegTMnPd:`eee8aa`,pOegYF:`98fb98`,pOeQe:`afeeee`,pOeviTetYd:`db7093`,papayawEp:`ffefd5`,pHKpuff:`ffdab9`,peru:`cd853f`,pRk:`ffc0cb`,plum:`dda0dd`,powMrXe:`b0e0e6`,purpN:`800080`,YbeccapurpN:`663399`,Yd:`ff0000`,Psybrown:`bc8f8f`,PyOXe:`4169e1`,saddNbPwn:`8b4513`,sOmon:`fa8072`,sandybPwn:`f4a460`,sHgYF:`2e8b57`,sHshell:`fff5ee`,siFna:`a0522d`,silver:`c0c0c0`,skyXe:`87ceeb`,UXe:`6a5acd`,UWay:`708090`,UgYy:`708090`,snow:`fffafa`,sprRggYF:`ff7f`,stAlXe:`4682b4`,tan:`d2b48c`,teO:`8080`,tEstN:`d8bfd8`,tomato:`ff6347`,Qe:`40e0d0`,viTet:`ee82ee`,JHt:`f5deb3`,wEte:`ffffff`,wEtesmoke:`f5f5f5`,Lw:`ffff00`,LwgYF:`9acd32`};function Re(){let e={},t=Object.keys(Le),n=Object.keys(Ie),r,i,a,o,s;for(r=0;r<t.length;r++){for(o=s=t[r],i=0;i<n.length;i++)a=n[i],s=s.replace(a,Ie[a]);a=parseInt(Le[o],16),e[s]=[a>>16&255,a>>8&255,a&255]}return e}let ze;function Be(e){ze||(ze=Re(),ze.transparent=[0,0,0,0]);let t=ze[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Ve=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function He(e){let t=Ve.exec(e),n=255,r,i,a;if(t){if(t[7]!==r){let e=+t[7];n=t[8]?fe(e):A(e*255,0,255)}return r=+t[1],i=+t[3],a=+t[5],r=255&(t[2]?fe(r):A(r,0,255)),i=255&(t[4]?fe(i):A(i,0,255)),a=255&(t[6]?fe(a):A(a,0,255)),{r,g:i,b:a,a:n}}}function Ue(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${M(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const We=e=>e<=.0031308?e*12.92:e**(1/2.4)*1.055-.055,Ge=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function Ke(e,t,n){let r=Ge(M(e.r)),i=Ge(M(e.g)),a=Ge(M(e.b));return{r:j(We(r+n*(Ge(M(t.r))-r))),g:j(We(i+n*(Ge(M(t.g))-i))),b:j(We(a+n*(Ge(M(t.b))-a))),a:e.a+n*(t.a-e.a)}}function qe(e,t,n){if(e){let r=De(e);r[t]=Math.max(0,Math.min(r[t]+r[t]*n,t===0?360:1)),r=ke(r),e.r=r[0],e.g=r[1],e.b=r[2]}}function Je(e,t){return e&&Object.assign(t||{},e)}function Ye(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=j(e[3]))):(t=Je(e,{r:0,g:0,b:0,a:1}),t.a=j(t.a)),t}function Xe(e){return e.charAt(0)===`r`?He(e):Ne(e)}var Ze=class e{constructor(t){if(t instanceof e)return t;let n=typeof t,r;n===`object`?r=Ye(t):n===`string`&&(r=ye(t)||Be(t)||Xe(t)),this._rgb=r,this._valid=!!r}get valid(){return this._valid}get rgb(){var e=Je(this._rgb);return e&&(e.a=M(e.a)),e}set rgb(e){this._rgb=Ye(e)}rgbString(){return this._valid?Ue(this._rgb):void 0}hexString(){return this._valid?xe(this._rgb):void 0}hslString(){return this._valid?Fe(this._rgb):void 0}mix(e,t){if(e){let n=this.rgb,r=e.rgb,i,a=t===i?.5:t,o=2*a-1,s=n.a-r.a,c=((o*s===-1?o:(o+s)/(1+o*s))+1)/2;i=1-c,n.r=255&c*n.r+i*r.r+.5,n.g=255&c*n.g+i*r.g+.5,n.b=255&c*n.b+i*r.b+.5,n.a=a*n.a+(1-a)*r.a,this.rgb=n}return this}interpolate(e,t){return e&&(this._rgb=Ke(this._rgb,e._rgb,t)),this}clone(){return new e(this.rgb)}alpha(e){return this._rgb.a=j(e),this}clearer(e){let t=this._rgb;return t.a*=1-e,this}greyscale(){let e=this._rgb,t=k(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=t,this}opaquer(e){let t=this._rgb;return t.a*=1+e,this}negate(){let e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return qe(this._rgb,2,e),this}darken(e){return qe(this._rgb,2,-e),this}saturate(e){return qe(this._rgb,1,e),this}desaturate(e){return qe(this._rgb,1,-e),this}rotate(e){return Pe(this._rgb,e),this}};function Qe(){}const $e=(()=>{let e=0;return()=>e++})();function P(e){return e==null}function F(e){if(Array.isArray&&Array.isArray(e))return!0;let t=Object.prototype.toString.call(e);return t.slice(0,7)===`[object`&&t.slice(-6)===`Array]`}function I(e){return e!==null&&Object.prototype.toString.call(e)===`[object Object]`}function L(e){return(typeof e==`number`||e instanceof Number)&&isFinite(+e)}function R(e,t){return L(e)?e:t}function z(e,t){return e===void 0?t:e}const et=(e,t)=>typeof e==`string`&&e.endsWith(`%`)?parseFloat(e)/100*t:+e;function B(e,t,n){if(e&&typeof e.call==`function`)return e.apply(n,t)}function V(e,t,n,r){let i,a,o;if(F(e))if(a=e.length,r)for(i=a-1;i>=0;i--)t.call(n,e[i],i);else for(i=0;i<a;i++)t.call(n,e[i],i);else if(I(e))for(o=Object.keys(e),a=o.length,i=0;i<a;i++)t.call(n,e[o[i]],o[i])}function tt(e,t){let n,r,i,a;if(!e||!t||e.length!==t.length)return!1;for(n=0,r=e.length;n<r;++n)if(i=e[n],a=t[n],i.datasetIndex!==a.datasetIndex||i.index!==a.index)return!1;return!0}function nt(e){if(F(e))return e.map(nt);if(I(e)){let t=Object.create(null),n=Object.keys(e),r=n.length,i=0;for(;i<r;++i)t[n[i]]=nt(e[n[i]]);return t}return e}function rt(e){return[`__proto__`,`prototype`,`constructor`].indexOf(e)===-1}function it(e,t,n,r){if(!rt(e))return;let i=t[e],a=n[e];I(i)&&I(a)?at(i,a,r):t[e]=nt(a)}function at(e,t,n){let r=F(t)?t:[t],i=r.length;if(!I(e))return e;n||={};let a=n.merger||it,o;for(let t=0;t<i;++t){if(o=r[t],!I(o))continue;let i=Object.keys(o);for(let t=0,r=i.length;t<r;++t)a(i[t],e,o,n)}return e}function ot(e,t){return at(e,t,{merger:st})}function st(e,t,n){if(!rt(e))return;let r=t[e],i=n[e];I(r)&&I(i)?ot(r,i):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=nt(i))}const ct={"":e=>e,x:e=>e.x,y:e=>e.y};function lt(e){let t=e.split(`.`),n=[],r=``;for(let e of t)r+=e,r.endsWith(`\\`)?r=r.slice(0,-1)+`.`:(n.push(r),r=``);return n}function ut(e){let t=lt(e);return e=>{for(let n of t){if(n===``)break;e&&=e[n]}return e}}function dt(e,t){let n=ct[t]||(ct[t]=ut(t));return n(e)}function ft(e){return e.charAt(0).toUpperCase()+e.slice(1)}const pt=e=>e!==void 0,mt=e=>typeof e==`function`,ht=(e,t)=>{if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0};function gt(e){return e.type===`mouseup`||e.type===`click`||e.type===`contextmenu`}const H=Math.PI,U=2*H,_t=U+H,vt=1/0,yt=H/180,W=H/2,bt=H/4,xt=H*2/3,St=Math.log10,Ct=Math.sign;function wt(e,t,n){return Math.abs(e-t)<n}function Tt(e){let t=Math.round(e);e=wt(e,t,e/1e3)?t:e;let n=10**Math.floor(St(e)),r=e/n,i=r<=1?1:r<=2?2:r<=5?5:10;return i*n}function Et(e){let t=[],n=Math.sqrt(e),r;for(r=1;r<n;r++)e%r===0&&(t.push(r),t.push(e/r));return n===(n|0)&&t.push(n),t.sort((e,t)=>e-t).pop(),t}function Dt(e){return typeof e==`symbol`||typeof e==`object`&&!!e&&!(Symbol.toPrimitive in e||`toString`in e||`valueOf`in e)}function Ot(e){return!Dt(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function kt(e,t){let n=Math.round(e);return n-t<=e&&n+t>=e}function At(e,t,n){let r,i,a;for(r=0,i=e.length;r<i;r++)a=e[r][n],isNaN(a)||(t.min=Math.min(t.min,a),t.max=Math.max(t.max,a))}function jt(e){return e*(H/180)}function Mt(e){return e*(180/H)}function Nt(e){if(!L(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function Pt(e,t){let n=t.x-e.x,r=t.y-e.y,i=Math.sqrt(n*n+r*r),a=Math.atan2(r,n);return a<-.5*H&&(a+=U),{angle:a,distance:i}}function Ft(e,t){return Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2)}function It(e,t){return(e-t+_t)%U-H}function G(e){return(e%U+U)%U}function Lt(e,t,n,r){let i=G(e),a=G(t),o=G(n),s=G(a-i),c=G(o-i),l=G(i-a),u=G(i-o);return i===a||i===o||r&&a===o||s>c&&l<u}function K(e,t,n){return Math.max(t,Math.min(n,e))}function Rt(e){return K(e,-32768,32767)}function zt(e,t,n,r=1e-6){return e>=Math.min(t,n)-r&&e<=Math.max(t,n)+r}function Bt(e,t,n){n||=(n=>e[n]<t);let r=e.length-1,i=0,a;for(;r-i>1;)a=i+r>>1,n(a)?i=a:r=a;return{lo:i,hi:r}}const Vt=(e,t,n,r)=>Bt(e,n,r?r=>{let i=e[r][t];return i<n||i===n&&e[r+1][t]===n}:r=>e[r][t]<n),Ht=(e,t,n)=>Bt(e,n,r=>e[r][t]>=n);function Ut(e,t,n){let r=0,i=e.length;for(;r<i&&e[r]<t;)r++;for(;i>r&&e[i-1]>n;)i--;return r>0||i<e.length?e.slice(r,i):e}const Wt=[`push`,`pop`,`shift`,`splice`,`unshift`];function Gt(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,`_chartjs`,{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Wt.forEach(t=>{let n=`_onData`+ft(t),r=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value(...t){let i=r.apply(this,t);return e._chartjs.listeners.forEach(e=>{typeof e[n]==`function`&&e[n](...t)}),i}})})}function Kt(e,t){let n=e._chartjs;if(!n)return;let r=n.listeners,i=r.indexOf(t);i!==-1&&r.splice(i,1),!(r.length>0)&&(Wt.forEach(t=>{delete e[t]}),delete e._chartjs)}function qt(e){let t=new Set(e);return t.size===e.length?e:Array.from(t)}const Jt=function(){return typeof window>`u`?function(e){return e()}:window.requestAnimationFrame}();function Yt(e,t){let n=[],r=!1;return function(...i){n=i,r||(r=!0,Jt.call(window,()=>{r=!1,e.apply(t,n)}))}}function Xt(e,t){let n;return function(...r){return t?(clearTimeout(n),n=setTimeout(e,t,r)):e.apply(this,r),t}}const Zt=e=>e===`start`?`left`:e===`end`?`right`:`center`,q=(e,t,n)=>e===`start`?t:e===`end`?n:(t+n)/2,Qt=(e,t,n,r)=>{let i=r?`left`:`right`;return e===i?n:e===`center`?(t+n)/2:t},$t=e=>e===0||e===1,en=(e,t,n)=>-(2**(10*--e)*Math.sin((e-t)*U/n)),tn=(e,t,n)=>2**(-10*e)*Math.sin((e-t)*U/n)+1,nn={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>--e*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-(--e*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>--e*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*W)+1,easeOutSine:e=>Math.sin(e*W),easeInOutSine:e=>-.5*(Math.cos(H*e)-1),easeInExpo:e=>e===0?0:2**(10*(e-1)),easeOutExpo:e=>e===1?1:-(2**(-10*e))+1,easeInOutExpo:e=>$t(e)?e:e<.5?.5*2**(10*(e*2-1)):.5*(-(2**(-10*(e*2-1)))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1- --e*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>$t(e)?e:en(e,.075,.3),easeOutElastic:e=>$t(e)?e:tn(e,.075,.3),easeInOutElastic(e){let t=.1125,n=.45;return $t(e)?e:e<.5?.5*en(e*2,t,n):.5+.5*tn(e*2-1,t,n)},easeInBack(e){let t=1.70158;return e*e*((t+1)*e-t)},easeOutBack(e){let t=1.70158;return--e*e*((t+1)*e+t)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-nn.easeOutBounce(1-e),easeOutBounce(e){let t=7.5625,n=2.75;return e<1/n?t*e*e:e<2/n?t*(e-=1.5/n)*e+.75:e<2.5/n?t*(e-=2.25/n)*e+.9375:t*(e-=2.625/n)*e+.984375},easeInOutBounce:e=>e<.5?nn.easeInBounce(e*2)*.5:nn.easeOutBounce(e*2-1)*.5+.5};function rn(e){if(e&&typeof e==`object`){let t=e.toString();return t===`[object CanvasPattern]`||t===`[object CanvasGradient]`}return!1}function an(e){return rn(e)?e:new Ze(e)}function on(e){return rn(e)?e:new Ze(e).saturate(.5).darken(.1).hexString()}const sn=[`x`,`y`,`borderWidth`,`radius`,`tension`],cn=[`color`,`borderColor`,`backgroundColor`];function ln(e){e.set(`animation`,{delay:void 0,duration:1e3,easing:`easeOutQuart`,fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe(`animation`,{_fallback:!1,_indexable:!1,_scriptable:e=>e!==`onProgress`&&e!==`onComplete`&&e!==`fn`}),e.set(`animations`,{colors:{type:`color`,properties:cn},numbers:{type:`number`,properties:sn}}),e.describe(`animations`,{_fallback:`animation`}),e.set(`transitions`,{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:`transparent`},visible:{type:`boolean`,duration:0}}},hide:{animations:{colors:{to:`transparent`},visible:{type:`boolean`,easing:`linear`,fn:e=>e|0}}}})}function un(e){e.set(`layout`,{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const dn=new Map;function fn(e,t){t||={};let n=e+JSON.stringify(t),r=dn.get(n);return r||(r=new Intl.NumberFormat(e,t),dn.set(n,r)),r}function pn(e,t,n){return fn(t,n).format(e)}const mn={values(e){return F(e)?e:``+e},numeric(e,t,n){if(e===0)return`0`;let r=this.chart.options.locale,i,a=e;if(n.length>1){let t=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(t<1e-4||t>1e15)&&(i=`scientific`),a=hn(e,n)}let o=St(Math.abs(a)),s=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),c={notation:i,minimumFractionDigits:s,maximumFractionDigits:s};return Object.assign(c,this.options.ticks.format),pn(e,r,c)},logarithmic(e,t,n){if(e===0)return`0`;let r=n[t].significand||e/10**Math.floor(St(e));return[1,2,3,5,10,15].includes(r)||t>.8*n.length?mn.numeric.call(this,e,t,n):``}};function hn(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e)),n}var gn={formatters:mn};function _n(e){e.set(`scale`,{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:`ticks`,clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,t)=>t.lineWidth,tickColor:(e,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:``,padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:``,padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:gn.formatters.values,minor:{},major:{},align:`center`,crossAlign:`near`,showLabelBackdrop:!1,backdropColor:`rgba(255, 255, 255, 0.75)`,backdropPadding:2}}),e.route(`scale.ticks`,`color`,``,`color`),e.route(`scale.grid`,`color`,``,`borderColor`),e.route(`scale.border`,`color`,``,`borderColor`),e.route(`scale.title`,`color`,``,`color`),e.describe(`scale`,{_fallback:!1,_scriptable:e=>!e.startsWith(`before`)&&!e.startsWith(`after`)&&e!==`callback`&&e!==`parser`,_indexable:e=>e!==`borderDash`&&e!==`tickBorderDash`&&e!==`dash`}),e.describe(`scales`,{_fallback:`scale`}),e.describe(`scale.ticks`,{_scriptable:e=>e!==`backdropPadding`&&e!==`callback`,_indexable:e=>e!==`backdropPadding`})}const vn=Object.create(null),yn=Object.create(null);function bn(e,t){if(!t)return e;let n=t.split(`.`);for(let t=0,r=n.length;t<r;++t){let r=n[t];e=e[r]||(e[r]=Object.create(null))}return e}function xn(e,t,n){return typeof t==`string`?at(bn(e,t),n):at(bn(e,``),t)}var Sn=class{constructor(e,t){this.animation=void 0,this.backgroundColor=`rgba(0,0,0,0.1)`,this.borderColor=`rgba(0,0,0,0.1)`,this.color=`#666`,this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=[`mousemove`,`mouseout`,`click`,`touchstart`,`touchmove`],this.font={family:`'Helvetica Neue', 'Helvetica', 'Arial', sans-serif`,size:12,style:`normal`,lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,t)=>on(t.backgroundColor),this.hoverBorderColor=(e,t)=>on(t.borderColor),this.hoverColor=(e,t)=>on(t.color),this.indexAxis=`x`,this.interaction={mode:`nearest`,intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(t)}set(e,t){return xn(this,e,t)}get(e){return bn(this,e)}describe(e,t){return xn(yn,e,t)}override(e,t){return xn(vn,e,t)}route(e,t,n,r){let i=bn(this,e),a=bn(this,n),o=`_`+t;Object.defineProperties(i,{[o]:{value:i[t],writable:!0},[t]:{enumerable:!0,get(){let e=this[o],t=a[r];return I(e)?Object.assign({},t,e):z(e,t)},set(e){this[o]=e}}})}apply(e){e.forEach(e=>e(this))}},J=new Sn({_scriptable:e=>!e.startsWith(`on`),_indexable:e=>e!==`events`,hover:{_fallback:`interaction`},interaction:{_scriptable:!1,_indexable:!1}},[ln,un,_n]);function Cn(e){return!e||P(e.size)||P(e.family)?null:(e.style?e.style+` `:``)+(e.weight?e.weight+` `:``)+e.size+`px `+e.family}function wn(e,t,n,r,i){let a=t[i];return a||(a=t[i]=e.measureText(i).width,n.push(i)),a>r&&(r=a),r}function Tn(e,t,n,r){r||={};let i=r.data=r.data||{},a=r.garbageCollect=r.garbageCollect||[];r.font!==t&&(i=r.data={},a=r.garbageCollect=[],r.font=t),e.save(),e.font=t;let o=0,s=n.length,c,l,u,d,f;for(c=0;c<s;c++)if(d=n[c],d!=null&&!F(d))o=wn(e,i,a,o,d);else if(F(d))for(l=0,u=d.length;l<u;l++)f=d[l],f!=null&&!F(f)&&(o=wn(e,i,a,o,f));e.restore();let p=a.length/2;if(p>n.length){for(c=0;c<p;c++)delete i[a[c]];a.splice(0,p)}return o}function En(e,t,n){let r=e.currentDevicePixelRatio,i=n===0?0:Math.max(n/2,.5);return Math.round((t-i)*r)/r+i}function Dn(e,t){!t&&!e||(t||=e.getContext(`2d`),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function On(e,t,n,r){kn(e,t,n,r,null)}function kn(e,t,n,r,i){let a,o,s,c,l,u,d,f,p=t.pointStyle,m=t.rotation,h=t.radius,g=(m||0)*yt;if(p&&typeof p==`object`&&(a=p.toString(),a===`[object HTMLImageElement]`||a===`[object HTMLCanvasElement]`)){e.save(),e.translate(n,r),e.rotate(g),e.drawImage(p,-p.width/2,-p.height/2,p.width,p.height),e.restore();return}if(!(isNaN(h)||h<=0)){switch(e.beginPath(),p){default:i?e.ellipse(n,r,i/2,h,0,0,U):e.arc(n,r,h,0,U),e.closePath();break;case`triangle`:u=i?i/2:h,e.moveTo(n+Math.sin(g)*u,r-Math.cos(g)*h),g+=xt,e.lineTo(n+Math.sin(g)*u,r-Math.cos(g)*h),g+=xt,e.lineTo(n+Math.sin(g)*u,r-Math.cos(g)*h),e.closePath();break;case`rectRounded`:l=h*.516,c=h-l,o=Math.cos(g+bt)*c,d=Math.cos(g+bt)*(i?i/2-l:c),s=Math.sin(g+bt)*c,f=Math.sin(g+bt)*(i?i/2-l:c),e.arc(n-d,r-s,l,g-H,g-W),e.arc(n+f,r-o,l,g-W,g),e.arc(n+d,r+s,l,g,g+W),e.arc(n-f,r+o,l,g+W,g+H),e.closePath();break;case`rect`:if(!m){c=Math.SQRT1_2*h,u=i?i/2:c,e.rect(n-u,r-c,2*u,2*c);break}g+=bt;case`rectRot`:d=Math.cos(g)*(i?i/2:h),o=Math.cos(g)*h,s=Math.sin(g)*h,f=Math.sin(g)*(i?i/2:h),e.moveTo(n-d,r-s),e.lineTo(n+f,r-o),e.lineTo(n+d,r+s),e.lineTo(n-f,r+o),e.closePath();break;case`crossRot`:g+=bt;case`cross`:d=Math.cos(g)*(i?i/2:h),o=Math.cos(g)*h,s=Math.sin(g)*h,f=Math.sin(g)*(i?i/2:h),e.moveTo(n-d,r-s),e.lineTo(n+d,r+s),e.moveTo(n+f,r-o),e.lineTo(n-f,r+o);break;case`star`:d=Math.cos(g)*(i?i/2:h),o=Math.cos(g)*h,s=Math.sin(g)*h,f=Math.sin(g)*(i?i/2:h),e.moveTo(n-d,r-s),e.lineTo(n+d,r+s),e.moveTo(n+f,r-o),e.lineTo(n-f,r+o),g+=bt,d=Math.cos(g)*(i?i/2:h),o=Math.cos(g)*h,s=Math.sin(g)*h,f=Math.sin(g)*(i?i/2:h),e.moveTo(n-d,r-s),e.lineTo(n+d,r+s),e.moveTo(n+f,r-o),e.lineTo(n-f,r+o);break;case`line`:o=i?i/2:Math.cos(g)*h,s=Math.sin(g)*h,e.moveTo(n-o,r-s),e.lineTo(n+o,r+s);break;case`dash`:e.moveTo(n,r),e.lineTo(n+Math.cos(g)*(i?i/2:h),r+Math.sin(g)*h);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function An(e,t,n){return n||=.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function jn(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function Mn(e){e.restore()}function Nn(e,t,n,r,i){if(!t)return e.lineTo(n.x,n.y);if(i===`middle`){let r=(t.x+n.x)/2;e.lineTo(r,t.y),e.lineTo(r,n.y)}else i===`after`==!!r?e.lineTo(n.x,t.y):e.lineTo(t.x,n.y);e.lineTo(n.x,n.y)}function Pn(e,t,n,r){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(r?t.cp1x:t.cp2x,r?t.cp1y:t.cp2y,r?n.cp2x:n.cp1x,r?n.cp2y:n.cp1y,n.x,n.y)}function Fn(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),P(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function In(e,t,n,r,i){if(i.strikethrough||i.underline){let a=e.measureText(r),o=t-a.actualBoundingBoxLeft,s=t+a.actualBoundingBoxRight,c=n-a.actualBoundingBoxAscent,l=n+a.actualBoundingBoxDescent,u=i.strikethrough?(c+l)/2:l;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=i.decorationWidth||2,e.moveTo(o,u),e.lineTo(s,u),e.stroke()}}function Ln(e,t){let n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function Rn(e,t,n,r,i,a={}){let o=F(t)?t:[t],s=a.strokeWidth>0&&a.strokeColor!==``,c,l;for(e.save(),e.font=i.string,Fn(e,a),c=0;c<o.length;++c)l=o[c],a.backdrop&&Ln(e,a.backdrop),s&&(a.strokeColor&&(e.strokeStyle=a.strokeColor),P(a.strokeWidth)||(e.lineWidth=a.strokeWidth),e.strokeText(l,n,r,a.maxWidth)),e.fillText(l,n,r,a.maxWidth),In(e,n,r,l,a),r+=Number(i.lineHeight);e.restore()}function zn(e,t){let{x:n,y:r,w:i,h:a,radius:o}=t;e.arc(n+o.topLeft,r+o.topLeft,o.topLeft,1.5*H,H,!0),e.lineTo(n,r+a-o.bottomLeft),e.arc(n+o.bottomLeft,r+a-o.bottomLeft,o.bottomLeft,H,W,!0),e.lineTo(n+i-o.bottomRight,r+a),e.arc(n+i-o.bottomRight,r+a-o.bottomRight,o.bottomRight,W,0,!0),e.lineTo(n+i,r+o.topRight),e.arc(n+i-o.topRight,r+o.topRight,o.topRight,0,-W,!0),e.lineTo(n+o.topLeft,r)}const Bn=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Vn=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Hn(e,t){let n=(``+e).match(Bn);if(!n||n[1]===`normal`)return t*1.2;switch(e=+n[2],n[3]){case`px`:return e;case`%`:e/=100;break}return t*e}const Un=e=>+e||0;function Wn(e,t){let n={},r=I(t),i=r?Object.keys(t):t,a=I(e)?r?n=>z(e[n],e[t[n]]):t=>e[t]:()=>e;for(let e of i)n[e]=Un(a(e));return n}function Gn(e){return Wn(e,{top:`y`,right:`x`,bottom:`y`,left:`x`})}function Kn(e){return Wn(e,[`topLeft`,`topRight`,`bottomLeft`,`bottomRight`])}function Y(e){let t=Gn(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function X(e,t){e||={},t||=J.font;let n=z(e.size,t.size);typeof n==`string`&&(n=parseInt(n,10));let r=z(e.style,t.style);r&&!(``+r).match(Vn)&&(console.warn(`Invalid font style specified: "`+r+`"`),r=void 0);let i={family:z(e.family,t.family),lineHeight:Hn(z(e.lineHeight,t.lineHeight),n),size:n,style:r,weight:z(e.weight,t.weight),string:``};return i.string=Cn(i),i}function qn(e,t,n,r){let i=!0,a,o,s;for(a=0,o=e.length;a<o;++a)if(s=e[a],s!==void 0&&(t!==void 0&&typeof s==`function`&&(s=s(t),i=!1),n!==void 0&&F(s)&&(s=s[n%s.length],i=!1),s!==void 0))return r&&!i&&(r.cacheable=!1),s}function Jn(e,t,n){let{min:r,max:i}=e,a=et(t,(i-r)/2),o=(e,t)=>n&&e===0?0:e+t;return{min:o(r,-Math.abs(a)),max:o(i,a)}}function Yn(e,t){return Object.assign(Object.create(e),t)}function Xn(e,t=[``],n,r,i=()=>e[0]){let a=n||e;r===void 0&&(r=fr(`_fallback`,e));let o={[Symbol.toStringTag]:`Object`,_cacheable:!0,_scopes:e,_rootScopes:a,_fallback:r,_getTarget:i,override:n=>Xn([n,...e],t,a,r)};return new Proxy(o,{deleteProperty(t,n){return delete t[n],delete t._keys,delete e[0][n],!0},get(n,r){return tr(n,r,()=>dr(r,t,e,n))},getOwnPropertyDescriptor(e,t){return Reflect.getOwnPropertyDescriptor(e._scopes[0],t)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(e,t){return pr(e).includes(t)},ownKeys(e){return pr(e)},set(e,t,n){let r=e._storage||=i();return e[t]=r[t]=n,delete e._keys,!0}})}function Zn(e,t,n,r){let i={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:Qn(e,r),setContext:t=>Zn(e,t,n,r),override:i=>Zn(e.override(i),t,n,r)};return new Proxy(i,{deleteProperty(t,n){return delete t[n],delete e[n],!0},get(e,t,n){return tr(e,t,()=>nr(e,t,n))},getOwnPropertyDescriptor(t,n){return t._descriptors.allKeys?Reflect.has(e,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,n)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(t,n){return Reflect.has(e,n)},ownKeys(){return Reflect.ownKeys(e)},set(t,n,r){return e[n]=r,delete t[n],!0}})}function Qn(e,t={scriptable:!0,indexable:!0}){let{_scriptable:n=t.scriptable,_indexable:r=t.indexable,_allKeys:i=t.allKeys}=e;return{allKeys:i,scriptable:n,indexable:r,isScriptable:mt(n)?n:()=>n,isIndexable:mt(r)?r:()=>r}}const $n=(e,t)=>e?e+ft(t):t,er=(e,t)=>I(t)&&e!==`adapters`&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function tr(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||t===`constructor`)return e[t];let r=n();return e[t]=r,r}function nr(e,t,n){let{_proxy:r,_context:i,_subProxy:a,_descriptors:o}=e,s=r[t];return mt(s)&&o.isScriptable(t)&&(s=rr(t,s,e,n)),F(s)&&s.length&&(s=ir(t,s,e,o.isIndexable)),er(t,s)&&(s=Zn(s,i,a&&a[t],o)),s}function rr(e,t,n,r){let{_proxy:i,_context:a,_subProxy:o,_stack:s}=n;if(s.has(e))throw Error(`Recursion detected: `+Array.from(s).join(`->`)+`->`+e);s.add(e);let c=t(a,o||r);return s.delete(e),er(e,c)&&(c=cr(i._scopes,i,e,c)),c}function ir(e,t,n,r){let{_proxy:i,_context:a,_subProxy:o,_descriptors:s}=n;if(a.index!==void 0&&r(e))return t[a.index%t.length];if(I(t[0])){let n=t,r=i._scopes.filter(e=>e!==n);t=[];for(let c of n){let n=cr(r,i,e,c);t.push(Zn(n,a,o&&o[e],s))}}return t}function ar(e,t,n){return mt(e)?e(t,n):e}const or=(e,t)=>e===!0?t:typeof e==`string`?dt(t,e):void 0;function sr(e,t,n,r,i){for(let a of t){let t=or(n,a);if(t){e.add(t);let a=ar(t._fallback,n,i);if(a!==void 0&&a!==n&&a!==r)return a}else if(t===!1&&r!==void 0&&n!==r)return null}return!1}function cr(e,t,n,r){let i=t._rootScopes,a=ar(t._fallback,n,r),o=[...e,...i],s=new Set;s.add(r);let c=lr(s,o,n,a||n,r);return c===null||a!==void 0&&a!==n&&(c=lr(s,o,a,c,r),c===null)?!1:Xn(Array.from(s),[``],i,a,()=>ur(t,n,r))}function lr(e,t,n,r,i){for(;n;)n=sr(e,t,n,r,i);return n}function ur(e,t,n){let r=e._getTarget();t in r||(r[t]={});let i=r[t];return F(i)&&I(n)?n:i||{}}function dr(e,t,n,r){let i;for(let a of t)if(i=fr($n(a,e),n),i!==void 0)return er(e,i)?cr(n,r,e,i):i}function fr(e,t){for(let n of t){if(!n)continue;let t=n[e];if(t!==void 0)return t}}function pr(e){let t=e._keys;return t||=e._keys=mr(e._scopes),t}function mr(e){let t=new Set;for(let n of e)for(let e of Object.keys(n).filter(e=>!e.startsWith(`_`)))t.add(e);return Array.from(t)}const hr=2**-52||1e-14,gr=(e,t)=>t<e.length&&!e[t].skip&&e[t],_r=e=>e===`x`?`y`:`x`;function vr(e,t,n,r){let i=e.skip?t:e,a=t,o=n.skip?t:n,s=Ft(a,i),c=Ft(o,a),l=s/(s+c),u=c/(s+c);l=isNaN(l)?0:l,u=isNaN(u)?0:u;let d=r*l,f=r*u;return{previous:{x:a.x-d*(o.x-i.x),y:a.y-d*(o.y-i.y)},next:{x:a.x+f*(o.x-i.x),y:a.y+f*(o.y-i.y)}}}function yr(e,t,n){let r=e.length,i,a,o,s,c,l=gr(e,0);for(let u=0;u<r-1;++u)if(c=l,l=gr(e,u+1),!(!c||!l)){if(wt(t[u],0,hr)){n[u]=n[u+1]=0;continue}i=n[u]/t[u],a=n[u+1]/t[u],s=i**2+a**2,!(s<=9)&&(o=3/Math.sqrt(s),n[u]=i*o*t[u],n[u+1]=a*o*t[u])}}function br(e,t,n=`x`){let r=_r(n),i=e.length,a,o,s,c=gr(e,0);for(let l=0;l<i;++l){if(o=s,s=c,c=gr(e,l+1),!s)continue;let i=s[n],u=s[r];o&&(a=(i-o[n])/3,s[`cp1${n}`]=i-a,s[`cp1${r}`]=u-a*t[l]),c&&(a=(c[n]-i)/3,s[`cp2${n}`]=i+a,s[`cp2${r}`]=u+a*t[l])}}function xr(e,t=`x`){let n=_r(t),r=e.length,i=Array(r).fill(0),a=Array(r),o,s,c,l=gr(e,0);for(o=0;o<r;++o)if(s=c,c=l,l=gr(e,o+1),c){if(l){let e=l[t]-c[t];i[o]=e===0?0:(l[n]-c[n])/e}a[o]=s?l?Ct(i[o-1])===Ct(i[o])?(i[o-1]+i[o])/2:0:i[o-1]:i[o]}yr(e,i,a),br(e,a,t)}function Sr(e,t,n){return Math.max(Math.min(e,n),t)}function Cr(e,t){let n,r,i,a,o,s=An(e[0],t);for(n=0,r=e.length;n<r;++n)o=a,a=s,s=n<r-1&&An(e[n+1],t),a&&(i=e[n],o&&(i.cp1x=Sr(i.cp1x,t.left,t.right),i.cp1y=Sr(i.cp1y,t.top,t.bottom)),s&&(i.cp2x=Sr(i.cp2x,t.left,t.right),i.cp2y=Sr(i.cp2y,t.top,t.bottom)))}function wr(e,t,n,r,i){let a,o,s,c;if(t.spanGaps&&(e=e.filter(e=>!e.skip)),t.cubicInterpolationMode===`monotone`)xr(e,i);else{let n=r?e[e.length-1]:e[0];for(a=0,o=e.length;a<o;++a)s=e[a],c=vr(n,s,e[Math.min(a+1,o-(r?0:1))%o],t.tension),s.cp1x=c.previous.x,s.cp1y=c.previous.y,s.cp2x=c.next.x,s.cp2y=c.next.y,n=s}t.capBezierPoints&&Cr(e,n)}function Tr(){return typeof window<`u`&&typeof document<`u`}function Er(e){let t=e.parentNode;return t&&t.toString()===`[object ShadowRoot]`&&(t=t.host),t}function Dr(e,t,n){let r;return typeof e==`string`?(r=parseInt(e,10),e.indexOf(`%`)!==-1&&(r=r/100*t.parentNode[n])):r=e,r}const Or=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function kr(e,t){return Or(e).getPropertyValue(t)}const Ar=[`top`,`right`,`bottom`,`left`];function jr(e,t,n){let r={};n=n?`-`+n:``;for(let i=0;i<4;i++){let a=Ar[i];r[a]=parseFloat(e[t+`-`+a+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}const Mr=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function Nr(e,t){let n=e.touches,r=n&&n.length?n[0]:e,{offsetX:i,offsetY:a}=r,o=!1,s,c;if(Mr(i,a,e.target))s=i,c=a;else{let e=t.getBoundingClientRect();s=r.clientX-e.left,c=r.clientY-e.top,o=!0}return{x:s,y:c,box:o}}function Pr(e,t){if(`native`in e)return e;let{canvas:n,currentDevicePixelRatio:r}=t,i=Or(n),a=i.boxSizing===`border-box`,o=jr(i,`padding`),s=jr(i,`border`,`width`),{x:c,y:l,box:u}=Nr(e,n),d=o.left+(u&&s.left),f=o.top+(u&&s.top),{width:p,height:m}=t;return a&&(p-=o.width+s.width,m-=o.height+s.height),{x:Math.round((c-d)/p*n.width/r),y:Math.round((l-f)/m*n.height/r)}}function Fr(e,t,n){let r,i;if(t===void 0||n===void 0){let a=e&&Er(e);if(!a)t=e.clientWidth,n=e.clientHeight;else{let e=a.getBoundingClientRect(),o=Or(a),s=jr(o,`border`,`width`),c=jr(o,`padding`);t=e.width-c.width-s.width,n=e.height-c.height-s.height,r=Dr(o.maxWidth,a,`clientWidth`),i=Dr(o.maxHeight,a,`clientHeight`)}}return{width:t,height:n,maxWidth:r||vt,maxHeight:i||vt}}const Ir=e=>Math.round(e*10)/10;function Lr(e,t,n,r){let i=Or(e),a=jr(i,`margin`),o=Dr(i.maxWidth,e,`clientWidth`)||vt,s=Dr(i.maxHeight,e,`clientHeight`)||vt,c=Fr(e,t,n),{width:l,height:u}=c;if(i.boxSizing===`content-box`){let e=jr(i,`border`,`width`),t=jr(i,`padding`);l-=t.width+e.width,u-=t.height+e.height}l=Math.max(0,l-a.width),u=Math.max(0,r?l/r:u-a.height),l=Ir(Math.min(l,o,c.maxWidth)),u=Ir(Math.min(u,s,c.maxHeight)),l&&!u&&(u=Ir(l/2));let d=t!==void 0||n!==void 0;return d&&r&&c.height&&u>c.height&&(u=c.height,l=Ir(Math.floor(u*r))),{width:l,height:u}}function Rr(e,t,n){let r=t||1,i=Math.floor(e.height*r),a=Math.floor(e.width*r);e.height=Math.floor(e.height),e.width=Math.floor(e.width);let o=e.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${e.height}px`,o.style.width=`${e.width}px`),e.currentDevicePixelRatio!==r||o.height!==i||o.width!==a?(e.currentDevicePixelRatio=r,o.height=i,o.width=a,e.ctx.setTransform(r,0,0,r,0,0),!0):!1}const zr=function(){let e=!1;try{let t={get passive(){return e=!0,!1}};Tr()&&(window.addEventListener(`test`,null,t),window.removeEventListener(`test`,null,t))}catch{}return e}();function Br(e,t){let n=kr(e,t),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}function Vr(e,t,n,r){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function Hr(e,t,n,r){return{x:e.x+n*(t.x-e.x),y:r===`middle`?n<.5?e.y:t.y:r===`after`?n<1?e.y:t.y:n>0?t.y:e.y}}function Ur(e,t,n,r){let i={x:e.cp2x,y:e.cp2y},a={x:t.cp1x,y:t.cp1y},o=Vr(e,i,n),s=Vr(i,a,n),c=Vr(a,t,n),l=Vr(o,s,n),u=Vr(s,c,n);return Vr(l,u,n)}const Wr=function(e,t){return{x(n){return e+e+t-n},setWidth(e){t=e},textAlign(e){return e===`center`?e:e===`right`?`left`:`right`},xPlus(e,t){return e-t},leftForLtr(e,t){return e-t}}},Gr=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function Kr(e,t,n){return e?Wr(t,n):Gr()}function qr(e,t){let n,r;(t===`ltr`||t===`rtl`)&&(n=e.canvas.style,r=[n.getPropertyValue(`direction`),n.getPropertyPriority(`direction`)],n.setProperty(`direction`,t,`important`),e.prevTextDirection=r)}function Jr(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty(`direction`,t[0],t[1]))}function Yr(e){return e===`angle`?{between:Lt,compare:It,normalize:G}:{between:zt,compare:(e,t)=>e-t,normalize:e=>e}}function Xr({start:e,end:t,count:n,loop:r,style:i}){return{start:e%n,end:t%n,loop:r&&(t-e+1)%n===0,style:i}}function Zr(e,t,n){let{property:r,start:i,end:a}=n,{between:o,normalize:s}=Yr(r),c=t.length,{start:l,end:u,loop:d}=e,f,p;if(d){for(l+=c,u+=c,f=0,p=c;f<p&&o(s(t[l%c][r]),i,a);++f)l--,u--;l%=c,u%=c}return u<l&&(u+=c),{start:l,end:u,loop:d,style:e.style}}function Qr(e,t,n){if(!n)return[e];let{property:r,start:i,end:a}=n,o=t.length,{compare:s,between:c,normalize:l}=Yr(r),{start:u,end:d,loop:f,style:p}=Zr(e,t,n),m=[],h=!1,g=null,_,v,y,b=()=>c(i,y,_)&&s(i,y)!==0,x=()=>s(a,_)===0||c(a,y,_),S=()=>h||b(),C=()=>!h||x();for(let e=u,n=u;e<=d;++e)v=t[e%o],!v.skip&&(_=l(v[r]),_!==y&&(h=c(_,i,a),g===null&&S()&&(g=s(_,i)===0?e:n),g!==null&&C()&&(m.push(Xr({start:g,end:e,loop:f,count:o,style:p})),g=null),n=e,y=_));return g!==null&&m.push(Xr({start:g,end:d,loop:f,count:o,style:p})),m}function $r(e,t){let n=[],r=e.segments;for(let i=0;i<r.length;i++){let a=Qr(r[i],e.points,t);a.length&&n.push(...a)}return n}function ei(e,t,n,r){let i=0,a=t-1;if(n&&!r)for(;i<t&&!e[i].skip;)i++;for(;i<t&&e[i].skip;)i++;for(i%=t,n&&(a+=i);a>i&&e[a%t].skip;)a--;return a%=t,{start:i,end:a}}function ti(e,t,n,r){let i=e.length,a=[],o=t,s=e[t],c;for(c=t+1;c<=n;++c){let n=e[c%i];n.skip||n.stop?s.skip||(r=!1,a.push({start:t%i,end:(c-1)%i,loop:r}),t=o=n.stop?c:null):(o=c,s.skip&&(t=c)),s=n}return o!==null&&a.push({start:t%i,end:o%i,loop:r}),a}function ni(e,t){let n=e.points,r=e.options.spanGaps,i=n.length;if(!i)return[];let a=!!e._loop,{start:o,end:s}=ei(n,i,a,r);if(r===!0)return ri(e,[{start:o,end:s,loop:a}],n,t);let c=s<o?s+i:s,l=!!e._fullLoop&&o===0&&s===i-1;return ri(e,ti(n,o,c,l),n,t)}function ri(e,t,n,r){return!r||!r.setContext||!n?t:ii(e,t,n,r)}function ii(e,t,n,r){let i=e._chart.getContext(),a=ai(e.options),{_datasetIndex:o,options:{spanGaps:s}}=e,c=n.length,l=[],u=a,d=t[0].start,f=d;function p(e,t,r,i){let a=s?-1:1;if(e!==t){for(e+=c;n[e%c].skip;)e-=a;for(;n[t%c].skip;)t+=a;e%c!==t%c&&(l.push({start:e%c,end:t%c,loop:r,style:i}),u=i,d=t%c)}}for(let e of t){d=s?d:e.start;let t=n[d%c],a;for(f=d+1;f<=e.end;f++){let s=n[f%c];a=ai(r.setContext(Yn(i,{type:`segment`,p0:t,p1:s,p0DataIndex:(f-1)%c,p1DataIndex:f%c,datasetIndex:o}))),oi(a,u)&&p(d,f-1,e.loop,u),t=s,u=a}d<f-1&&p(d,f-1,e.loop,u)}return l}function ai(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function oi(e,t){if(!t)return!1;let n=[],r=function(e,t){return rn(t)?(n.includes(t)||n.push(t),n.indexOf(t)):t};return JSON.stringify(e,r)!==JSON.stringify(t,r)}function si(e,t,n){return e.options.clip?e[n]:t[n]}function ci(e,t){let{xScale:n,yScale:r}=e;return n&&r?{left:si(n,t,`left`),right:si(n,t,`right`),top:si(r,t,`top`),bottom:si(r,t,`bottom`)}:t}function li(e,t){let n=t._clip;if(n.disabled)return!1;let r=ci(t,e.chartArea);return{left:n.left===!1?0:r.left-(n.left===!0?0:n.left),right:n.right===!1?e.width:r.right+(n.right===!0?0:n.right),top:n.top===!1?0:r.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?e.height:r.bottom+(n.bottom===!0?0:n.bottom)}}var ui=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,t,n,r){let i=t.listeners[r],a=t.duration;i.forEach(r=>r({chart:e,initial:t.initial,numSteps:a,currentStep:Math.min(n-t.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=Jt.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let t=0;this._charts.forEach((n,r)=>{if(!n.running||!n.items.length)return;let i=n.items,a=i.length-1,o=!1,s;for(;a>=0;--a)s=i[a],s._active?(s._total>n.duration&&(n.duration=s._total),s.tick(e),o=!0):(i[a]=i[i.length-1],i.pop());o&&(r.draw(),this._notify(r,n,e,`progress`)),i.length||(n.running=!1,this._notify(r,n,e,`complete`),n.initial=!1),t+=i.length}),this._lastDate=e,t===0&&(this._running=!1)}_getAnims(e){let t=this._charts,n=t.get(e);return n||(n={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(e,n)),n}listen(e,t,n){this._getAnims(e).listeners[t].push(n)}add(e,t){!t||!t.length||this._getAnims(e).items.push(...t)}has(e){return this._getAnims(e).items.length>0}start(e){let t=this._charts.get(e);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((e,t)=>Math.max(e,t._duration),0),this._refresh())}running(e){if(!this._running)return!1;let t=this._charts.get(e);return!(!t||!t.running||!t.items.length)}stop(e){let t=this._charts.get(e);if(!t||!t.items.length)return;let n=t.items,r=n.length-1;for(;r>=0;--r)n[r].cancel();t.items=[],this._notify(e,t,Date.now(),`complete`)}remove(e){return this._charts.delete(e)}},di=new ui;const fi=`transparent`,pi={boolean(e,t,n){return n>.5?t:e},color(e,t,n){let r=an(e||fi),i=r.valid&&an(t||fi);return i&&i.valid?i.mix(r,n).hexString():t},number(e,t,n){return e+(t-e)*n}};var mi=class{constructor(e,t,n,r){let i=t[n];r=qn([e.to,r,i,e.from]);let a=qn([e.from,i,r]);this._active=!0,this._fn=e.fn||pi[e.type||typeof a],this._easing=nn[e.easing]||nn.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=t,this._prop=n,this._from=a,this._to=r,this._promises=void 0}active(){return this._active}update(e,t,n){if(this._active){this._notify(!1);let r=this._target[this._prop],i=n-this._start,a=this._duration-i;this._start=n,this._duration=Math.floor(Math.max(a,e.duration)),this._total+=i,this._loop=!!e.loop,this._to=qn([e.to,t,r,e.from]),this._from=qn([e.from,r,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){let t=e-this._start,n=this._duration,r=this._prop,i=this._from,a=this._loop,o=this._to,s;if(this._active=i!==o&&(a||t<n),!this._active){this._target[r]=o,this._notify(!0);return}if(t<0){this._target[r]=i;return}s=t/n%2,s=a&&s>1?2-s:s,s=this._easing(Math.min(1,Math.max(0,s))),this._target[r]=this._fn(i,o,s)}wait(){let e=this._promises||=[];return new Promise((t,n)=>{e.push({res:t,rej:n})})}_notify(e){let t=e?`res`:`rej`,n=this._promises||[];for(let e=0;e<n.length;e++)n[e][t]()}},hi=class{constructor(e,t){this._chart=e,this._properties=new Map,this.configure(t)}configure(e){if(!I(e))return;let t=Object.keys(J.animation),n=this._properties;Object.getOwnPropertyNames(e).forEach(r=>{let i=e[r];if(!I(i))return;let a={};for(let e of t)a[e]=i[e];(F(i.properties)&&i.properties||[r]).forEach(e=>{(e===r||!n.has(e))&&n.set(e,a)})})}_animateOptions(e,t){let n=t.options,r=_i(e,n);if(!r)return[];let i=this._createAnimations(r,n);return n.$shared&&gi(e.options.$animations,n).then(()=>{e.options=n},()=>{}),i}_createAnimations(e,t){let n=this._properties,r=[],i=e.$animations||={},a=Object.keys(t),o=Date.now(),s;for(s=a.length-1;s>=0;--s){let c=a[s];if(c.charAt(0)===`$`)continue;if(c===`options`){r.push(...this._animateOptions(e,t));continue}let l=t[c],u=i[c],d=n.get(c);if(u)if(d&&u.active()){u.update(d,l,o);continue}else u.cancel();if(!d||!d.duration){e[c]=l;continue}i[c]=u=new mi(d,e,c,l),r.push(u)}return r}update(e,t){if(this._properties.size===0){Object.assign(e,t);return}let n=this._createAnimations(e,t);if(n.length)return di.add(this._chart,n),!0}};function gi(e,t){let n=[],r=Object.keys(t);for(let t=0;t<r.length;t++){let i=e[r[t]];i&&i.active()&&n.push(i.wait())}return Promise.all(n)}function _i(e,t){if(!t)return;let n=e.options;if(!n){e.options=t;return}return n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function vi(e,t){let n=e&&e.options||{},r=n.reverse,i=n.min===void 0?t:0,a=n.max===void 0?t:0;return{start:r?a:i,end:r?i:a}}function yi(e,t,n){if(n===!1)return!1;let r=vi(e,n),i=vi(t,n);return{top:i.end,right:r.end,bottom:i.start,left:r.start}}function bi(e){let t,n,r,i;return I(e)?(t=e.top,n=e.right,r=e.bottom,i=e.left):t=n=r=i=e,{top:t,right:n,bottom:r,left:i,disabled:e===!1}}function xi(e,t){let n=[],r=e._getSortedDatasetMetas(t),i,a;for(i=0,a=r.length;i<a;++i)n.push(r[i].index);return n}function Si(e,t,n,r={}){let i=e.keys,a=r.mode===`single`,o,s,c,l;if(t===null)return;let u=!1;for(o=0,s=i.length;o<s;++o){if(c=+i[o],c===n){if(u=!0,r.all)continue;break}l=e.values[c],L(l)&&(a||t===0||Ct(t)===Ct(l))&&(t+=l)}return!u&&!r.all?0:t}function Ci(e,t){let{iScale:n,vScale:r}=t,i=n.axis===`x`?`x`:`y`,a=r.axis===`x`?`x`:`y`,o=Object.keys(e),s=Array(o.length),c,l,u;for(c=0,l=o.length;c<l;++c)u=o[c],s[c]={[i]:u,[a]:e[u]};return s}function wi(e,t){let n=e&&e.options.stacked;return n||n===void 0&&t.stack!==void 0}function Ti(e,t,n){return`${e.id}.${t.id}.${n.stack||n.type}`}function Ei(e){let{min:t,max:n,minDefined:r,maxDefined:i}=e.getUserBounds();return{min:r?t:-1/0,max:i?n:1/0}}function Di(e,t,n){let r=e[t]||(e[t]={});return r[n]||(r[n]={})}function Oi(e,t,n,r){for(let i of t.getMatchingVisibleMetas(r).reverse()){let t=e[i.index];if(n&&t>0||!n&&t<0)return i.index}return null}function ki(e,t){let{chart:n,_cachedMeta:r}=e,i=n._stacks||={},{iScale:a,vScale:o,index:s}=r,c=a.axis,l=o.axis,u=Ti(a,o,r),d=t.length,f;for(let e=0;e<d;++e){let n=t[e],{[c]:a,[l]:d}=n,p=n._stacks||={};f=p[l]=Di(i,u,a),f[s]=d,f._top=Oi(f,o,!0,r.type),f._bottom=Oi(f,o,!1,r.type);let m=f._visualValues||={};m[s]=d}}function Ai(e,t){let n=e.scales;return Object.keys(n).filter(e=>n[e].axis===t).shift()}function ji(e,t){return Yn(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:`default`,type:`dataset`})}function Mi(e,t,n){return Yn(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:`default`,type:`data`})}function Ni(e,t){let n=e.controller.index,r=e.vScale&&e.vScale.axis;if(r){t||=e._parsed;for(let e of t){let t=e._stacks;if(!t||t[r]===void 0||t[r][n]===void 0)return;delete t[r][n],t[r]._visualValues!==void 0&&t[r]._visualValues[n]!==void 0&&delete t[r]._visualValues[n]}}}const Pi=e=>e===`reset`||e===`none`,Fi=(e,t)=>t?e:Object.assign({},e),Ii=(e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:xi(n,!0),values:null};var Li=class{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(e,t){this.chart=e,this._ctx=e.ctx,this.index=t,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=wi(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled(`filler`)&&console.warn(`Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options`)}updateIndex(e){this.index!==e&&Ni(this._cachedMeta),this.index=e}linkScales(){let e=this.chart,t=this._cachedMeta,n=this.getDataset(),r=(e,t,n,r)=>e===`x`?t:e===`r`?r:n,i=t.xAxisID=z(n.xAxisID,Ai(e,`x`)),a=t.yAxisID=z(n.yAxisID,Ai(e,`y`)),o=t.rAxisID=z(n.rAxisID,Ai(e,`r`)),s=t.indexAxis,c=t.iAxisID=r(s,i,a,o),l=t.vAxisID=r(s,a,i,o);t.xScale=this.getScaleForId(i),t.yScale=this.getScaleForId(a),t.rScale=this.getScaleForId(o),t.iScale=this.getScaleForId(c),t.vScale=this.getScaleForId(l)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){let t=this._cachedMeta;return e===t.iScale?t.vScale:t.iScale}reset(){this._update(`reset`)}_destroy(){let e=this._cachedMeta;this._data&&Kt(this._data,this),e._stacked&&Ni(e)}_dataCheck(){let e=this.getDataset(),t=e.data||=[],n=this._data;if(I(t)){let e=this._cachedMeta;this._data=Ci(t,e)}else if(n!==t){if(n){Kt(n,this);let e=this._cachedMeta;Ni(e),e._parsed=[]}t&&Object.isExtensible(t)&&Gt(t,this),this._syncList=[],this._data=t}}addElements(){let e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){let t=this._cachedMeta,n=this.getDataset(),r=!1;this._dataCheck();let i=t._stacked;t._stacked=wi(t.vScale,t),t.stack!==n.stack&&(r=!0,Ni(t),t.stack=n.stack),this._resyncElements(e),(r||i!==t._stacked)&&(ki(this,t._parsed),t._stacked=wi(t.vScale,t))}configure(){let e=this.chart.config,t=e.datasetScopeKeys(this._type),n=e.getOptionScopes(this.getDataset(),t,!0);this.options=e.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,t){let{_cachedMeta:n,_data:r}=this,{iScale:i,_stacked:a}=n,o=i.axis,s=e===0&&t===r.length?!0:n._sorted,c=e>0&&n._parsed[e-1],l,u,d;if(this._parsing===!1)n._parsed=r,n._sorted=!0,d=r;else{d=F(r[e])?this.parseArrayData(n,r,e,t):I(r[e])?this.parseObjectData(n,r,e,t):this.parsePrimitiveData(n,r,e,t);let i=()=>u[o]===null||c&&u[o]<c[o];for(l=0;l<t;++l)n._parsed[l+e]=u=d[l],s&&(i()&&(s=!1),c=u);n._sorted=s}a&&ki(this,d)}parsePrimitiveData(e,t,n,r){let{iScale:i,vScale:a}=e,o=i.axis,s=a.axis,c=i.getLabels(),l=i===a,u=Array(r),d,f,p;for(d=0,f=r;d<f;++d)p=d+n,u[d]={[o]:l||i.parse(c[p],p),[s]:a.parse(t[p],p)};return u}parseArrayData(e,t,n,r){let{xScale:i,yScale:a}=e,o=Array(r),s,c,l,u;for(s=0,c=r;s<c;++s)l=s+n,u=t[l],o[s]={x:i.parse(u[0],l),y:a.parse(u[1],l)};return o}parseObjectData(e,t,n,r){let{xScale:i,yScale:a}=e,{xAxisKey:o=`x`,yAxisKey:s=`y`}=this._parsing,c=Array(r),l,u,d,f;for(l=0,u=r;l<u;++l)d=l+n,f=t[d],c[l]={x:i.parse(dt(f,o),d),y:a.parse(dt(f,s),d)};return c}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,t,n){let r=this.chart,i=this._cachedMeta,a=t[e.axis],o={keys:xi(r,!0),values:t._stacks[e.axis]._visualValues};return Si(o,a,i.index,{mode:n})}updateRangeFromParsed(e,t,n,r){let i=n[t.axis],a=i===null?NaN:i,o=r&&n._stacks[t.axis];r&&o&&(r.values=o,a=Si(r,i,this._cachedMeta.index)),e.min=Math.min(e.min,a),e.max=Math.max(e.max,a)}getMinMax(e,t){let n=this._cachedMeta,r=n._parsed,i=n._sorted&&e===n.iScale,a=r.length,o=this._getOtherScale(e),s=Ii(t,n,this.chart),c={min:1/0,max:-1/0},{min:l,max:u}=Ei(o),d,f;function p(){f=r[d];let t=f[o.axis];return!L(f[e.axis])||l>t||u<t}for(d=0;d<a&&!(!p()&&(this.updateRangeFromParsed(c,e,f,s),i));++d);if(i){for(d=a-1;d>=0;--d)if(!p()){this.updateRangeFromParsed(c,e,f,s);break}}return c}getAllParsedValues(e){let t=this._cachedMeta._parsed,n=[],r,i,a;for(r=0,i=t.length;r<i;++r)a=t[r][e.axis],L(a)&&n.push(a);return n}getMaxOverflow(){return!1}getLabelAndValue(e){let t=this._cachedMeta,n=t.iScale,r=t.vScale,i=this.getParsed(e);return{label:n?``+n.getLabelForValue(i[n.axis]):``,value:r?``+r.getLabelForValue(i[r.axis]):``}}_update(e){let t=this._cachedMeta;this.update(e||`default`),t._clip=bi(z(this.options.clip,yi(t.xScale,t.yScale,this.getMaxOverflow())))}update(e){}draw(){let e=this._ctx,t=this.chart,n=this._cachedMeta,r=n.data||[],i=t.chartArea,a=[],o=this._drawStart||0,s=this._drawCount||r.length-o,c=this.options.drawActiveElementsOnTop,l;for(n.dataset&&n.dataset.draw(e,i,o,s),l=o;l<o+s;++l){let t=r[l];t.hidden||(t.active&&c?a.push(t):t.draw(e,i))}for(l=0;l<a.length;++l)a[l].draw(e,i)}getStyle(e,t){let n=t?`active`:`default`;return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(e||0,n)}getContext(e,t,n){let r=this.getDataset(),i;if(e>=0&&e<this._cachedMeta.data.length){let t=this._cachedMeta.data[e];i=t.$context||=Mi(this.getContext(),e,t),i.parsed=this.getParsed(e),i.raw=r.data[e],i.index=i.dataIndex=e}else i=this.$context||=ji(this.chart.getContext(),this.index),i.dataset=r,i.index=i.datasetIndex=this.index;return i.active=!!t,i.mode=n,i}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,t){return this._resolveElementOptions(this.dataElementType.id,t,e)}_resolveElementOptions(e,t=`default`,n){let r=t===`active`,i=this._cachedDataOpts,a=e+`-`+t,o=i[a],s=this.enableOptionSharing&&pt(n);if(o)return Fi(o,s);let c=this.chart.config,l=c.datasetElementScopeKeys(this._type,e),u=r?[`${e}Hover`,`hover`,e,``]:[e,``],d=c.getOptionScopes(this.getDataset(),l),f=Object.keys(J.elements[e]),p=()=>this.getContext(n,r,t),m=c.resolveNamedOptions(d,f,p,u);return m.$shared&&(m.$shared=s,i[a]=Object.freeze(Fi(m,s))),m}_resolveAnimations(e,t,n){let r=this.chart,i=this._cachedDataOpts,a=`animation-${t}`,o=i[a];if(o)return o;let s;if(r.options.animation!==!1){let r=this.chart.config,i=r.datasetAnimationScopeKeys(this._type,t),a=r.getOptionScopes(this.getDataset(),i);s=r.createResolver(a,this.getContext(e,n,t))}let c=new hi(r,s&&s.animations);return s&&s._cacheable&&(i[a]=Object.freeze(c)),c}getSharedOptions(e){if(e.$shared)return this._sharedOptions||=Object.assign({},e)}includeOptions(e,t){return!t||Pi(e)||this.chart._animationsDisabled}_getSharedOptions(e,t){let n=this.resolveDataElementOptions(e,t),r=this._sharedOptions,i=this.getSharedOptions(n),a=this.includeOptions(t,i)||i!==r;return this.updateSharedOptions(i,t,n),{sharedOptions:i,includeOptions:a}}updateElement(e,t,n,r){Pi(r)?Object.assign(e,n):this._resolveAnimations(t,r).update(e,n)}updateSharedOptions(e,t,n){e&&!Pi(t)&&this._resolveAnimations(void 0,t).update(e,n)}_setStyle(e,t,n,r){e.active=r;let i=this.getStyle(t,r);this._resolveAnimations(t,n,r).update(e,{options:!r&&this.getSharedOptions(i)||i})}removeHoverStyle(e,t,n){this._setStyle(e,n,`active`,!1)}setHoverStyle(e,t,n){this._setStyle(e,n,`active`,!0)}_removeDatasetHoverStyle(){let e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,`active`,!1)}_setDatasetHoverStyle(){let e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,`active`,!0)}_resyncElements(e){let t=this._data,n=this._cachedMeta.data;for(let[e,t,n]of this._syncList)this[e](t,n);this._syncList=[];let r=n.length,i=t.length,a=Math.min(i,r);a&&this.parse(0,a),i>r?this._insertElements(r,i-r,e):i<r&&this._removeElements(i,r-i)}_insertElements(e,t,n=!0){let r=this._cachedMeta,i=r.data,a=e+t,o,s=e=>{for(e.length+=t,o=e.length-1;o>=a;o--)e[o]=e[o-t]};for(s(i),o=e;o<a;++o)i[o]=new this.dataElementType;this._parsing&&s(r._parsed),this.parse(e,t),n&&this.updateElements(i,e,t,`reset`)}updateElements(e,t,n,r){}_removeElements(e,t){let n=this._cachedMeta;if(this._parsing){let r=n._parsed.splice(e,t);n._stacked&&Ni(n,r)}n.data.splice(e,t)}_sync(e){if(this._parsing)this._syncList.push(e);else{let[t,n,r]=e;this[t](n,r)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){let e=arguments.length;this._sync([`_insertElements`,this.getDataset().data.length-e,e])}_onDataPop(){this._sync([`_removeElements`,this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync([`_removeElements`,0,1])}_onDataSplice(e,t){t&&this._sync([`_removeElements`,e,t]);let n=arguments.length-2;n&&this._sync([`_insertElements`,e,n])}_onDataUnshift(){this._sync([`_insertElements`,0,arguments.length])}};function Ri(){throw Error(`This method is not implemented: Check that a complete date adapter is provided.`)}var zi=class e{static override(t){Object.assign(e.prototype,t)}options;constructor(e){this.options=e||{}}init(){}formats(){return Ri()}parse(){return Ri()}format(){return Ri()}add(){return Ri()}diff(){return Ri()}startOf(){return Ri()}endOf(){return Ri()}},Bi={_date:zi};function Vi(e,t,n,r){let{controller:i,data:a,_sorted:o}=e,s=i._cachedMeta.iScale,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(s&&t===s.axis&&t!==`r`&&o&&a.length){let o=s._reversePixels?Ht:Vt;if(r){if(i._sharedOptions){let e=a[0],r=typeof e.getRange==`function`&&e.getRange(t);if(r){let e=o(a,t,n-r),i=o(a,t,n+r);return{lo:e.lo,hi:i.hi}}}}else{let r=o(a,t,n);if(c){let{vScale:t}=i._cachedMeta,{_parsed:n}=e,a=n.slice(0,r.lo+1).reverse().findIndex(e=>!P(e[t.axis]));r.lo-=Math.max(0,a);let o=n.slice(r.hi).findIndex(e=>!P(e[t.axis]));r.hi+=Math.max(0,o)}return r}}return{lo:0,hi:a.length-1}}function Hi(e,t,n,r,i){let a=e.getSortedVisibleDatasetMetas(),o=n[t];for(let e=0,n=a.length;e<n;++e){let{index:n,data:s}=a[e],{lo:c,hi:l}=Vi(a[e],t,o,i);for(let e=c;e<=l;++e){let t=s[e];t.skip||r(t,n,e)}}}function Ui(e){let t=e.indexOf(`x`)!==-1,n=e.indexOf(`y`)!==-1;return function(e,r){let i=t?Math.abs(e.x-r.x):0,a=n?Math.abs(e.y-r.y):0;return Math.sqrt(i**2+a**2)}}function Wi(e,t,n,r,i){let a=[];if(!i&&!e.isPointInArea(t))return a;let o=function(n,o,s){!i&&!An(n,e.chartArea,0)||n.inRange(t.x,t.y,r)&&a.push({element:n,datasetIndex:o,index:s})};return Hi(e,n,t,o,!0),a}function Gi(e,t,n,r){let i=[];function a(e,n,a){let{startAngle:o,endAngle:s}=e.getProps([`startAngle`,`endAngle`],r),{angle:c}=Pt(e,{x:t.x,y:t.y});Lt(c,o,s)&&i.push({element:e,datasetIndex:n,index:a})}return Hi(e,n,t,a),i}function Ki(e,t,n,r,i,a){let o=[],s=Ui(n),c=1/0;function l(n,l,u){let d=n.inRange(t.x,t.y,i);if(r&&!d)return;let f=n.getCenterPoint(i),p=!!a||e.isPointInArea(f);if(!p&&!d)return;let m=s(t,f);m<c?(o=[{element:n,datasetIndex:l,index:u}],c=m):m===c&&o.push({element:n,datasetIndex:l,index:u})}return Hi(e,n,t,l),o}function qi(e,t,n,r,i,a){return!a&&!e.isPointInArea(t)?[]:n===`r`&&!r?Gi(e,t,n,i):Ki(e,t,n,r,i,a)}function Ji(e,t,n,r,i){let a=[],o=n===`x`?`inXRange`:`inYRange`,s=!1;return Hi(e,n,t,(e,r,c)=>{e[o]&&e[o](t[n],i)&&(a.push({element:e,datasetIndex:r,index:c}),s||=e.inRange(t.x,t.y,i))}),r&&!s?[]:a}var Yi={evaluateInteractionItems:Hi,modes:{index(e,t,n,r){let i=Pr(t,e),a=n.axis||`x`,o=n.includeInvisible||!1,s=n.intersect?Wi(e,i,a,r,o):qi(e,i,a,!1,r,o),c=[];return s.length?(e.getSortedVisibleDatasetMetas().forEach(e=>{let t=s[0].index,n=e.data[t];n&&!n.skip&&c.push({element:n,datasetIndex:e.index,index:t})}),c):[]},dataset(e,t,n,r){let i=Pr(t,e),a=n.axis||`xy`,o=n.includeInvisible||!1,s=n.intersect?Wi(e,i,a,r,o):qi(e,i,a,!1,r,o);if(s.length>0){let t=s[0].datasetIndex,n=e.getDatasetMeta(t).data;s=[];for(let e=0;e<n.length;++e)s.push({element:n[e],datasetIndex:t,index:e})}return s},point(e,t,n,r){let i=Pr(t,e),a=n.axis||`xy`,o=n.includeInvisible||!1;return Wi(e,i,a,r,o)},nearest(e,t,n,r){let i=Pr(t,e),a=n.axis||`xy`,o=n.includeInvisible||!1;return qi(e,i,a,n.intersect,r,o)},x(e,t,n,r){let i=Pr(t,e);return Ji(e,i,`x`,n.intersect,r)},y(e,t,n,r){let i=Pr(t,e);return Ji(e,i,`y`,n.intersect,r)}}};const Xi=[`left`,`top`,`right`,`bottom`];function Zi(e,t){return e.filter(e=>e.pos===t)}function Qi(e,t){return e.filter(e=>Xi.indexOf(e.pos)===-1&&e.box.axis===t)}function $i(e,t){return e.sort((e,n)=>{let r=t?n:e,i=t?e:n;return r.weight===i.weight?r.index-i.index:r.weight-i.weight})}function ea(e){let t=[],n,r,i,a,o,s;for(n=0,r=(e||[]).length;n<r;++n)i=e[n],{position:a,options:{stack:o,stackWeight:s=1}}=i,t.push({index:n,box:i,pos:a,horizontal:i.isHorizontal(),weight:i.weight,stack:o&&a+o,stackWeight:s});return t}function ta(e){let t={};for(let n of e){let{stack:e,pos:r,stackWeight:i}=n;if(!e||!Xi.includes(r))continue;let a=t[e]||(t[e]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=i}return t}function na(e,t){let n=ta(e),{vBoxMaxWidth:r,hBoxMaxHeight:i}=t,a,o,s;for(a=0,o=e.length;a<o;++a){s=e[a];let{fullSize:o}=s.box,c=n[s.stack],l=c&&s.stackWeight/c.weight;s.horizontal?(s.width=l?l*r:o&&t.availableWidth,s.height=i):(s.width=r,s.height=l?l*i:o&&t.availableHeight)}return n}function ra(e){let t=ea(e),n=$i(t.filter(e=>e.box.fullSize),!0),r=$i(Zi(t,`left`),!0),i=$i(Zi(t,`right`)),a=$i(Zi(t,`top`),!0),o=$i(Zi(t,`bottom`)),s=Qi(t,`x`),c=Qi(t,`y`);return{fullSize:n,leftAndTop:r.concat(a),rightAndBottom:i.concat(c).concat(o).concat(s),chartArea:Zi(t,`chartArea`),vertical:r.concat(i).concat(c),horizontal:a.concat(o).concat(s)}}function ia(e,t,n,r){return Math.max(e[n],t[n])+Math.max(e[r],t[r])}function aa(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function oa(e,t,n,r){let{pos:i,box:a}=n,o=e.maxPadding;if(!I(i)){n.size&&(e[i]-=n.size);let t=r[n.stack]||{size:0,count:1};t.size=Math.max(t.size,n.horizontal?a.height:a.width),n.size=t.size/t.count,e[i]+=n.size}a.getPadding&&aa(o,a.getPadding());let s=Math.max(0,t.outerWidth-ia(o,e,`left`,`right`)),c=Math.max(0,t.outerHeight-ia(o,e,`top`,`bottom`)),l=s!==e.w,u=c!==e.h;return e.w=s,e.h=c,n.horizontal?{same:l,other:u}:{same:u,other:l}}function sa(e){let t=e.maxPadding;function n(n){let r=Math.max(t[n]-e[n],0);return e[n]+=r,r}e.y+=n(`top`),e.x+=n(`left`),n(`right`),n(`bottom`)}function ca(e,t){let n=t.maxPadding;function r(e){let r={left:0,top:0,right:0,bottom:0};return e.forEach(e=>{r[e]=Math.max(t[e],n[e])}),r}return r(e?[`left`,`right`]:[`top`,`bottom`])}function la(e,t,n,r){let i=[],a,o,s,c,l,u;for(a=0,o=e.length,l=0;a<o;++a){s=e[a],c=s.box,c.update(s.width||t.w,s.height||t.h,ca(s.horizontal,t));let{same:o,other:d}=oa(t,n,s,r);l|=o&&i.length,u||=d,c.fullSize||i.push(s)}return l&&la(i,t,n,r)||u}function ua(e,t,n,r,i){e.top=n,e.left=t,e.right=t+r,e.bottom=n+i,e.width=r,e.height=i}function da(e,t,n,r){let i=n.padding,{x:a,y:o}=t;for(let s of e){let e=s.box,c=r[s.stack]||{count:1,placed:0,weight:1},l=s.stackWeight/c.weight||1;if(s.horizontal){let r=t.w*l,a=c.size||e.height;pt(c.start)&&(o=c.start),e.fullSize?ua(e,i.left,o,n.outerWidth-i.right-i.left,a):ua(e,t.left+c.placed,o,r,a),c.start=o,c.placed+=r,o=e.bottom}else{let r=t.h*l,o=c.size||e.width;pt(c.start)&&(a=c.start),e.fullSize?ua(e,a,i.top,o,n.outerHeight-i.bottom-i.top):ua(e,a,t.top+c.placed,o,r),c.start=a,c.placed+=r,a=e.right}}t.x=a,t.y=o}var Z={addBox(e,t){e.boxes||=[],t.fullSize=t.fullSize||!1,t.position=t.position||`top`,t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},e.boxes.push(t)},removeBox(e,t){let n=e.boxes?e.boxes.indexOf(t):-1;n!==-1&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,r){if(!e)return;let i=Y(e.options.layout.padding),a=Math.max(t-i.width,0),o=Math.max(n-i.height,0),s=ra(e.boxes),c=s.vertical,l=s.horizontal;V(e.boxes,e=>{typeof e.beforeLayout==`function`&&e.beforeLayout()});let u=c.reduce((e,t)=>t.box.options&&t.box.options.display===!1?e:e+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:n,padding:i,availableWidth:a,availableHeight:o,vBoxMaxWidth:a/2/u,hBoxMaxHeight:o/2}),f=Object.assign({},i);aa(f,Y(r));let p=Object.assign({maxPadding:f,w:a,h:o,x:i.left,y:i.top},i),m=na(c.concat(l),d);la(s.fullSize,p,d,m),la(c,p,d,m),la(l,p,d,m)&&la(c,p,d,m),sa(p),da(s.leftAndTop,p,d,m),p.x+=p.w,p.y+=p.h,da(s.rightAndBottom,p,d,m),e.chartArea={left:p.left,top:p.top,right:p.left+p.w,bottom:p.top+p.h,height:p.h,width:p.w},V(s.chartArea,t=>{let n=t.box;Object.assign(n,e.chartArea),n.update(p.w,p.h,{left:0,top:0,right:0,bottom:0})})}},fa=class{acquireContext(e,t){}releaseContext(e){return!1}addEventListener(e,t,n){}removeEventListener(e,t,n){}getDevicePixelRatio(){return 1}getMaximumSize(e,t,n,r){return t=Math.max(0,t||e.width),n||=e.height,{width:t,height:Math.max(0,r?Math.floor(t/r):n)}}isAttached(e){return!0}updateConfig(e){}},pa=class extends fa{acquireContext(e){return e&&e.getContext&&e.getContext(`2d`)||null}updateConfig(e){e.options.animation=!1}};const ma=`$chartjs`,ha={touchstart:`mousedown`,touchmove:`mousemove`,touchend:`mouseup`,pointerenter:`mouseenter`,pointerdown:`mousedown`,pointermove:`mousemove`,pointerup:`mouseup`,pointerleave:`mouseout`,pointerout:`mouseout`},ga=e=>e===null||e===``;function _a(e,t){let n=e.style,r=e.getAttribute(`height`),i=e.getAttribute(`width`);if(e[ma]={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||`block`,n.boxSizing=n.boxSizing||`border-box`,ga(i)){let t=Br(e,`width`);t!==void 0&&(e.width=t)}if(ga(r))if(e.style.height===``)e.height=e.width/(t||2);else{let t=Br(e,`height`);t!==void 0&&(e.height=t)}return e}const va=zr?{passive:!0}:!1;function ya(e,t,n){e&&e.addEventListener(t,n,va)}function ba(e,t,n){e&&e.canvas&&e.canvas.removeEventListener(t,n,va)}function xa(e,t){let n=ha[e.type]||e.type,{x:r,y:i}=Pr(e,t);return{type:n,chart:t,native:e,x:r===void 0?null:r,y:i===void 0?null:i}}function Sa(e,t){for(let n of e)if(n===t||n.contains(t))return!0}function Ca(e,t,n){let r=e.canvas,i=new MutationObserver(e=>{let t=!1;for(let n of e)t||=Sa(n.addedNodes,r),t&&=!Sa(n.removedNodes,r);t&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function wa(e,t,n){let r=e.canvas,i=new MutationObserver(e=>{let t=!1;for(let n of e)t||=Sa(n.removedNodes,r),t&&=!Sa(n.addedNodes,r);t&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const Ta=new Map;let Ea=0;function Da(){let e=window.devicePixelRatio;e!==Ea&&(Ea=e,Ta.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function Oa(e,t){Ta.size||window.addEventListener(`resize`,Da),Ta.set(e,t)}function ka(e){Ta.delete(e),Ta.size||window.removeEventListener(`resize`,Da)}function Aa(e,t,n){let r=e.canvas,i=r&&Er(r);if(!i)return;let a=Yt((e,t)=>{let r=i.clientWidth;n(e,t),r<i.clientWidth&&n()},window),o=new ResizeObserver(e=>{let t=e[0],n=t.contentRect.width,r=t.contentRect.height;n===0&&r===0||a(n,r)});return o.observe(i),Oa(e,a),o}function ja(e,t,n){n&&n.disconnect(),t===`resize`&&ka(e)}function Ma(e,t,n){let r=e.canvas,i=Yt(t=>{e.ctx!==null&&n(xa(t,e))},e);return ya(r,t,i),i}var Na=class extends fa{acquireContext(e,t){let n=e&&e.getContext&&e.getContext(`2d`);return n&&n.canvas===e?(_a(e,t),n):null}releaseContext(e){let t=e.canvas;if(!t[ma])return!1;let n=t[ma].initial;[`height`,`width`].forEach(e=>{let r=n[e];P(r)?t.removeAttribute(e):t.setAttribute(e,r)});let r=n.style||{};return Object.keys(r).forEach(e=>{t.style[e]=r[e]}),t.width=t.width,delete t[ma],!0}addEventListener(e,t,n){this.removeEventListener(e,t);let r=e.$proxies||={},i={attach:Ca,detach:wa,resize:Aa},a=i[t]||Ma;r[t]=a(e,t,n)}removeEventListener(e,t){let n=e.$proxies||={},r=n[t];if(!r)return;let i={attach:ja,detach:ja,resize:ja},a=i[t]||ba;a(e,t,r),n[t]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,t,n,r){return Lr(e,t,n,r)}isAttached(e){let t=e&&Er(e);return!!(t&&t.isConnected)}};function Pa(e){return!Tr()||typeof OffscreenCanvas<`u`&&e instanceof OffscreenCanvas?pa:Na}var Fa=class{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(e){let{x:t,y:n}=this.getProps([`x`,`y`],e);return{x:t,y:n}}hasValue(){return Ot(this.x)&&Ot(this.y)}getProps(e,t){let n=this.$animations;if(!t||!n)return this;let r={};return e.forEach(e=>{r[e]=n[e]&&n[e].active()?n[e]._to:this[e]}),r}};function Ia(e,t){let n=e.options.ticks,r=La(e),i=Math.min(n.maxTicksLimit||r,r),a=n.major.enabled?za(t):[],o=a.length,s=a[0],c=a[o-1],l=[];if(o>i)return Ba(t,l,a,o/i),l;let u=Ra(a,t,i);if(o>0){let e,n,r=o>1?Math.round((c-s)/(o-1)):null;for(Va(t,l,u,P(r)?0:s-r,s),e=0,n=o-1;e<n;e++)Va(t,l,u,a[e],a[e+1]);return Va(t,l,u,c,P(r)?t.length:c+r),l}return Va(t,l,u),l}function La(e){let t=e.options.offset,n=e._tickSize(),r=e._length/n+(t?0:1),i=e._maxLength/n;return Math.floor(Math.min(r,i))}function Ra(e,t,n){let r=Ha(e),i=t.length/n;if(!r)return Math.max(i,1);let a=Et(r);for(let e=0,t=a.length-1;e<t;e++){let t=a[e];if(t>i)return t}return Math.max(i,1)}function za(e){let t=[],n,r;for(n=0,r=e.length;n<r;n++)e[n].major&&t.push(n);return t}function Ba(e,t,n,r){let i=0,a=n[0],o;for(r=Math.ceil(r),o=0;o<e.length;o++)o===a&&(t.push(e[o]),i++,a=n[i*r])}function Va(e,t,n,r,i){let a=z(r,0),o=Math.min(z(i,e.length),e.length),s=0,c,l,u;for(n=Math.ceil(n),i&&(c=i-r,n=c/Math.floor(c/n)),u=a;u<0;)s++,u=Math.round(a+s*n);for(l=Math.max(a,0);l<o;l++)l===u&&(t.push(e[l]),s++,u=Math.round(a+s*n))}function Ha(e){let t=e.length,n,r;if(t<2)return!1;for(r=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==r)return!1;return r}const Ua=e=>e===`left`?`right`:e===`right`?`left`:e,Wa=(e,t,n)=>t===`top`||t===`left`?e[t]+n:e[t]-n,Ga=(e,t)=>Math.min(t||e,e);function Ka(e,t){let n=[],r=e.length/t,i=e.length,a=0;for(;a<i;a+=r)n.push(e[Math.floor(a)]);return n}function qa(e,t,n){let r=e.ticks.length,i=Math.min(t,r-1),a=e._startPixel,o=e._endPixel,s=1e-6,c=e.getPixelForTick(i),l;if(!(n&&(l=r===1?Math.max(c-a,o-c):t===0?(e.getPixelForTick(1)-c)/2:(c-e.getPixelForTick(i-1))/2,c+=i<t?l:-l,c<a-s||c>o+s)))return c}function Ja(e,t){V(e,e=>{let n=e.gc,r=n.length/2,i;if(r>t){for(i=0;i<r;++i)delete e.data[n[i]];n.splice(0,r)}})}function Ya(e){return e.drawTicks?e.tickLength:0}function Xa(e,t){if(!e.display)return 0;let n=X(e.font,t),r=Y(e.padding),i=F(e.text)?e.text.length:1;return i*n.lineHeight+r.height}function Za(e,t){return Yn(e,{scale:t,type:`scale`})}function Qa(e,t,n){return Yn(e,{tick:n,index:t,type:`tick`})}function $a(e,t,n){let r=Zt(e);return(n&&t!==`right`||!n&&t===`right`)&&(r=Ua(r)),r}function eo(e,t,n,r){let{top:i,left:a,bottom:o,right:s,chart:c}=e,{chartArea:l,scales:u}=c,d=0,f,p,m,h=o-i,g=s-a;if(e.isHorizontal()){if(p=q(r,a,s),I(n)){let e=Object.keys(n)[0],r=n[e];m=u[e].getPixelForValue(r)+h-t}else m=n===`center`?(l.bottom+l.top)/2+h-t:Wa(e,n,t);f=s-a}else{if(I(n)){let e=Object.keys(n)[0],r=n[e];p=u[e].getPixelForValue(r)-g+t}else p=n===`center`?(l.left+l.right)/2-g+t:Wa(e,n,t);m=q(r,o,i),d=n===`left`?-W:W}return{titleX:p,titleY:m,maxWidth:f,rotation:d}}var to=class e extends Fa{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,t){return e}getUserBounds(){let{_userMin:e,_userMax:t,_suggestedMin:n,_suggestedMax:r}=this;return e=R(e,1/0),t=R(t,-1/0),n=R(n,1/0),r=R(r,-1/0),{min:R(e,n),max:R(t,r),minDefined:L(e),maxDefined:L(t)}}getMinMax(e){let{min:t,max:n,minDefined:r,maxDefined:i}=this.getUserBounds(),a;if(r&&i)return{min:t,max:n};let o=this.getMatchingVisibleMetas();for(let s=0,c=o.length;s<c;++s)a=o[s].controller.getMinMax(this,e),r||(t=Math.min(t,a.min)),i||(n=Math.max(n,a.max));return t=i&&t>n?n:t,n=r&&t>n?t:n,{min:R(t,R(n,t)),max:R(n,R(t,n))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){let t=this._labelItems||=this._computeLabelItems(e);return t}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){B(this.options.beforeUpdate,[this])}update(e,t,n){let{beginAtZero:r,grace:i,ticks:a}=this.options,o=a.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=t,this._margins=n=Object.assign({left:0,right:0,top:0,bottom:0},n),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+n.left+n.right:this.height+n.top+n.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Jn(this,i,r),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let s=o<this.ticks.length;this._convertTicksToLabels(s?Ka(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source===`auto`)&&(this.ticks=Ia(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),s&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,t,n;this.isHorizontal()?(t=this.left,n=this.right):(t=this.top,n=this.bottom,e=!e),this._startPixel=t,this._endPixel=n,this._reversePixels=e,this._length=n-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){B(this.options.afterUpdate,[this])}beforeSetDimensions(){B(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){B(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),B(this.options[e],[this])}beforeDataLimits(){this._callHooks(`beforeDataLimits`)}determineDataLimits(){}afterDataLimits(){this._callHooks(`afterDataLimits`)}beforeBuildTicks(){this._callHooks(`beforeBuildTicks`)}buildTicks(){return[]}afterBuildTicks(){this._callHooks(`afterBuildTicks`)}beforeTickToLabelConversion(){B(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){let t=this.options.ticks,n,r,i;for(n=0,r=e.length;n<r;n++)i=e[n],i.label=B(t.callback,[i.value,n,e],this)}afterTickToLabelConversion(){B(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){B(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let e=this.options,t=e.ticks,n=Ga(this.ticks.length,e.ticks.maxTicksLimit),r=t.minRotation||0,i=t.maxRotation,a=r,o,s,c;if(!this._isVisible()||!t.display||r>=i||n<=1||!this.isHorizontal()){this.labelRotation=r;return}let l=this._getLabelSizes(),u=l.widest.width,d=l.highest.height,f=K(this.chart.width-u,0,this.maxWidth);o=e.offset?this.maxWidth/n:f/(n-1),u+6>o&&(o=f/(n-(e.offset?.5:1)),s=this.maxHeight-Ya(e.grid)-t.padding-Xa(e.title,this.chart.options.font),c=Math.sqrt(u*u+d*d),a=Mt(Math.min(Math.asin(K((l.highest.height+6)/o,-1,1)),Math.asin(K(s/c,-1,1))-Math.asin(K(d/c,-1,1)))),a=Math.max(r,Math.min(i,a))),this.labelRotation=a}afterCalculateLabelRotation(){B(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){B(this.options.beforeFit,[this])}fit(){let e={width:0,height:0},{chart:t,options:{ticks:n,title:r,grid:i}}=this,a=this._isVisible(),o=this.isHorizontal();if(a){let a=Xa(r,t.options.font);if(o?(e.width=this.maxWidth,e.height=Ya(i)+a):(e.height=this.maxHeight,e.width=Ya(i)+a),n.display&&this.ticks.length){let{first:t,last:r,widest:i,highest:a}=this._getLabelSizes(),s=n.padding*2,c=jt(this.labelRotation),l=Math.cos(c),u=Math.sin(c);if(o){let t=n.mirror?0:u*i.width+l*a.height;e.height=Math.min(this.maxHeight,e.height+t+s)}else{let t=n.mirror?0:l*i.width+u*a.height;e.width=Math.min(this.maxWidth,e.width+t+s)}this._calculatePadding(t,r,u,l)}}this._handleMargins(),o?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,t,n,r){let{ticks:{align:i,padding:a},position:o}=this.options,s=this.labelRotation!==0,c=o!==`top`&&this.axis===`x`;if(this.isHorizontal()){let o=this.getPixelForTick(0)-this.left,l=this.right-this.getPixelForTick(this.ticks.length-1),u=0,d=0;s?c?(u=r*e.width,d=n*t.height):(u=n*e.height,d=r*t.width):i===`start`?d=t.width:i===`end`?u=e.width:i!==`inner`&&(u=e.width/2,d=t.width/2),this.paddingLeft=Math.max((u-o+a)*this.width/(this.width-o),0),this.paddingRight=Math.max((d-l+a)*this.width/(this.width-l),0)}else{let n=t.height/2,r=e.height/2;i===`start`?(n=0,r=e.height):i===`end`&&(n=t.height,r=0),this.paddingTop=n+a,this.paddingBottom=r+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){B(this.options.afterFit,[this])}isHorizontal(){let{axis:e,position:t}=this.options;return t===`top`||t===`bottom`||e===`x`}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let t,n;for(t=0,n=e.length;t<n;t++)P(e[t].label)&&(e.splice(t,1),n--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){let t=this.options.ticks.sampleSize,n=this.ticks;t<n.length&&(n=Ka(n,t)),this._labelSizes=e=this._computeLabelSizes(n,n.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,t,n){let{ctx:r,_longestTextCache:i}=this,a=[],o=[],s=Math.floor(t/Ga(t,n)),c=0,l=0,u,d,f,p,m,h,g,_,v,y,b;for(u=0;u<t;u+=s){if(p=e[u].label,m=this._resolveTickFontOptions(u),r.font=h=m.string,g=i[h]=i[h]||{data:{},gc:[]},_=m.lineHeight,v=y=0,!P(p)&&!F(p))v=wn(r,g.data,g.gc,v,p),y=_;else if(F(p))for(d=0,f=p.length;d<f;++d)b=p[d],!P(b)&&!F(b)&&(v=wn(r,g.data,g.gc,v,b),y+=_);a.push(v),o.push(y),c=Math.max(v,c),l=Math.max(y,l)}Ja(i,t);let x=a.indexOf(c),S=o.indexOf(l),C=e=>({width:a[e]||0,height:o[e]||0});return{first:C(0),last:C(t-1),widest:C(x),highest:C(S),widths:a,heights:o}}getLabelForValue(e){return e}getPixelForValue(e,t){return NaN}getValueForPixel(e){}getPixelForTick(e){let t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);let t=this._startPixel+e*this._length;return Rt(this._alignToPixels?En(this.chart,t,0):t)}getDecimalForPixel(e){let t=(e-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:e,max:t}=this;return e<0&&t<0?t:e>0&&t>0?e:0}getContext(e){let t=this.ticks||[];if(e>=0&&e<t.length){let n=t[e];return n.$context||=Qa(this.getContext(),e,n)}return this.$context||=Za(this.chart.getContext(),this)}_tickSize(){let e=this.options.ticks,t=jt(this.labelRotation),n=Math.abs(Math.cos(t)),r=Math.abs(Math.sin(t)),i=this._getLabelSizes(),a=e.autoSkipPadding||0,o=i?i.widest.width+a:0,s=i?i.highest.height+a:0;return this.isHorizontal()?s*n>o*r?o/n:s/r:s*r<o*n?s/n:o/r}_isVisible(){let e=this.options.display;return e===`auto`?this.getMatchingVisibleMetas().length>0:!!e}_computeGridLineItems(e){let t=this.axis,n=this.chart,r=this.options,{grid:i,position:a,border:o}=r,s=i.offset,c=this.isHorizontal(),l=this.ticks,u=l.length+(s?1:0),d=Ya(i),f=[],p=o.setContext(this.getContext()),m=p.display?p.width:0,h=m/2,g=function(e){return En(n,e,m)},_,v,y,b,x,S,C,w,T,E,D,O;if(a===`top`)_=g(this.bottom),S=this.bottom-d,w=_-h,E=g(e.top)+h,O=e.bottom;else if(a===`bottom`)_=g(this.top),E=e.top,O=g(e.bottom)-h,S=_+h,w=this.top+d;else if(a===`left`)_=g(this.right),x=this.right-d,C=_-h,T=g(e.left)+h,D=e.right;else if(a===`right`)_=g(this.left),T=e.left,D=g(e.right)-h,x=_+h,C=this.left+d;else if(t===`x`){if(a===`center`)_=g((e.top+e.bottom)/2+.5);else if(I(a)){let e=Object.keys(a)[0],t=a[e];_=g(this.chart.scales[e].getPixelForValue(t))}E=e.top,O=e.bottom,S=_+h,w=S+d}else if(t===`y`){if(a===`center`)_=g((e.left+e.right)/2);else if(I(a)){let e=Object.keys(a)[0],t=a[e];_=g(this.chart.scales[e].getPixelForValue(t))}x=_-h,C=x-d,T=e.left,D=e.right}let ee=z(r.ticks.maxTicksLimit,u),te=Math.max(1,Math.ceil(u/ee));for(v=0;v<u;v+=te){let e=this.getContext(v),t=i.setContext(e),r=o.setContext(e),a=t.lineWidth,l=t.color,u=r.dash||[],d=r.dashOffset,p=t.tickWidth,m=t.tickColor,h=t.tickBorderDash||[],g=t.tickBorderDashOffset;y=qa(this,v,s),y!==void 0&&(b=En(n,y,a),c?x=C=T=D=b:S=w=E=O=b,f.push({tx1:x,ty1:S,tx2:C,ty2:w,x1:T,y1:E,x2:D,y2:O,width:a,color:l,borderDash:u,borderDashOffset:d,tickWidth:p,tickColor:m,tickBorderDash:h,tickBorderDashOffset:g}))}return this._ticksLength=u,this._borderValue=_,f}_computeLabelItems(e){let t=this.axis,n=this.options,{position:r,ticks:i}=n,a=this.isHorizontal(),o=this.ticks,{align:s,crossAlign:c,padding:l,mirror:u}=i,d=Ya(n.grid),f=d+l,p=u?-l:f,m=-jt(this.labelRotation),h=[],g,_,v,y,b,x,S,C,w,T,E,D,O=`middle`;if(r===`top`)x=this.bottom-p,S=this._getXAxisLabelAlignment();else if(r===`bottom`)x=this.top+p,S=this._getXAxisLabelAlignment();else if(r===`left`){let e=this._getYAxisLabelAlignment(d);S=e.textAlign,b=e.x}else if(r===`right`){let e=this._getYAxisLabelAlignment(d);S=e.textAlign,b=e.x}else if(t===`x`){if(r===`center`)x=(e.top+e.bottom)/2+f;else if(I(r)){let e=Object.keys(r)[0],t=r[e];x=this.chart.scales[e].getPixelForValue(t)+f}S=this._getXAxisLabelAlignment()}else if(t===`y`){if(r===`center`)b=(e.left+e.right)/2-f;else if(I(r)){let e=Object.keys(r)[0],t=r[e];b=this.chart.scales[e].getPixelForValue(t)}S=this._getYAxisLabelAlignment(d).textAlign}t===`y`&&(s===`start`?O=`top`:s===`end`&&(O=`bottom`));let ee=this._getLabelSizes();for(g=0,_=o.length;g<_;++g){v=o[g],y=v.label;let e=i.setContext(this.getContext(g));C=this.getPixelForTick(g)+i.labelOffset,w=this._resolveTickFontOptions(g),T=w.lineHeight,E=F(y)?y.length:1;let t=E/2,n=e.color,s=e.textStrokeColor,l=e.textStrokeWidth,d=S;a?(b=C,S===`inner`&&(d=g===_-1?this.options.reverse?`left`:`right`:g===0?this.options.reverse?`right`:`left`:`center`),D=r===`top`?c===`near`||m!==0?-E*T+T/2:c===`center`?-ee.highest.height/2-t*T+T:-ee.highest.height+T/2:c===`near`||m!==0?T/2:c===`center`?ee.highest.height/2-t*T:ee.highest.height-E*T,u&&(D*=-1),m!==0&&!e.showLabelBackdrop&&(b+=T/2*Math.sin(m))):(x=C,D=(1-E)*T/2);let f;if(e.showLabelBackdrop){let t=Y(e.backdropPadding),n=ee.heights[g],r=ee.widths[g],i=D-t.top,a=0-t.left;switch(O){case`middle`:i-=n/2;break;case`bottom`:i-=n;break}switch(S){case`center`:a-=r/2;break;case`right`:a-=r;break;case`inner`:g===_-1?a-=r:g>0&&(a-=r/2);break}f={left:a,top:i,width:r+t.width,height:n+t.height,color:e.backdropColor}}h.push({label:y,font:w,textOffset:D,options:{rotation:m,color:n,strokeColor:s,strokeWidth:l,textAlign:d,textBaseline:O,translation:[b,x],backdrop:f}})}return h}_getXAxisLabelAlignment(){let{position:e,ticks:t}=this.options,n=-jt(this.labelRotation);if(n)return e===`top`?`left`:`right`;let r=`center`;return t.align===`start`?r=`left`:t.align===`end`?r=`right`:t.align===`inner`&&(r=`inner`),r}_getYAxisLabelAlignment(e){let{position:t,ticks:{crossAlign:n,mirror:r,padding:i}}=this.options,a=this._getLabelSizes(),o=e+i,s=a.widest.width,c,l;return t===`left`?r?(l=this.right+i,n===`near`?c=`left`:n===`center`?(c=`center`,l+=s/2):(c=`right`,l+=s)):(l=this.right-o,n===`near`?c=`right`:n===`center`?(c=`center`,l-=s/2):(c=`left`,l=this.left)):t===`right`?r?(l=this.left+i,n===`near`?c=`right`:n===`center`?(c=`center`,l-=s/2):(c=`left`,l-=s)):(l=this.left+o,n===`near`?c=`left`:n===`center`?(c=`center`,l+=s/2):(c=`right`,l=this.right)):c=`right`,{textAlign:c,x:l}}_computeLabelArea(){if(this.options.ticks.mirror)return;let e=this.chart,t=this.options.position;if(t===`left`||t===`right`)return{top:0,left:this.left,bottom:e.height,right:this.right};if(t===`top`||t===`bottom`)return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){let{ctx:e,options:{backgroundColor:t},left:n,top:r,width:i,height:a}=this;t&&(e.save(),e.fillStyle=t,e.fillRect(n,r,i,a),e.restore())}getLineWidthForValue(e){let t=this.options.grid;if(!this._isVisible()||!t.display)return 0;let n=this.ticks,r=n.findIndex(t=>t.value===e);if(r>=0){let e=t.setContext(this.getContext(r));return e.lineWidth}return 0}drawGrid(e){let t=this.options.grid,n=this.ctx,r=this._gridLineItems||=this._computeGridLineItems(e),i,a,o=(e,t,r)=>{!r.width||!r.color||(n.save(),n.lineWidth=r.width,n.strokeStyle=r.color,n.setLineDash(r.borderDash||[]),n.lineDashOffset=r.borderDashOffset,n.beginPath(),n.moveTo(e.x,e.y),n.lineTo(t.x,t.y),n.stroke(),n.restore())};if(t.display)for(i=0,a=r.length;i<a;++i){let e=r[i];t.drawOnChartArea&&o({x:e.x1,y:e.y1},{x:e.x2,y:e.y2},e),t.drawTicks&&o({x:e.tx1,y:e.ty1},{x:e.tx2,y:e.ty2},{color:e.tickColor,width:e.tickWidth,borderDash:e.tickBorderDash,borderDashOffset:e.tickBorderDashOffset})}}drawBorder(){let{chart:e,ctx:t,options:{border:n,grid:r}}=this,i=n.setContext(this.getContext()),a=n.display?i.width:0;if(!a)return;let o=r.setContext(this.getContext(0)).lineWidth,s=this._borderValue,c,l,u,d;this.isHorizontal()?(c=En(e,this.left,a)-a/2,l=En(e,this.right,o)+o/2,u=d=s):(u=En(e,this.top,a)-a/2,d=En(e,this.bottom,o)+o/2,c=l=s),t.save(),t.lineWidth=i.width,t.strokeStyle=i.color,t.beginPath(),t.moveTo(c,u),t.lineTo(l,d),t.stroke(),t.restore()}drawLabels(e){let t=this.options.ticks;if(!t.display)return;let n=this.ctx,r=this._computeLabelArea();r&&jn(n,r);let i=this.getLabelItems(e);for(let e of i){let t=e.options,r=e.font,i=e.label,a=e.textOffset;Rn(n,i,0,a,r,t)}r&&Mn(n)}drawTitle(){let{ctx:e,options:{position:t,title:n,reverse:r}}=this;if(!n.display)return;let i=X(n.font),a=Y(n.padding),o=n.align,s=i.lineHeight/2;t===`bottom`||t===`center`||I(t)?(s+=a.bottom,F(n.text)&&(s+=i.lineHeight*(n.text.length-1))):s+=a.top;let{titleX:c,titleY:l,maxWidth:u,rotation:d}=eo(this,s,t,o);Rn(e,n.text,0,0,i,{color:n.color,maxWidth:u,rotation:d,textAlign:$a(o,t,r),textBaseline:`middle`,translation:[c,l]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){let t=this.options,n=t.ticks&&t.ticks.z||0,r=z(t.grid&&t.grid.z,-1),i=z(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==e.prototype.draw?[{z:n,draw:e=>{this.draw(e)}}]:[{z:r,draw:e=>{this.drawBackground(),this.drawGrid(e),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:e=>{this.drawLabels(e)}}]}getMatchingVisibleMetas(e){let t=this.chart.getSortedVisibleDatasetMetas(),n=this.axis+`AxisID`,r=[],i,a;for(i=0,a=t.length;i<a;++i){let a=t[i];a[n]===this.id&&(!e||a.type===e)&&r.push(a)}return r}_resolveTickFontOptions(e){let t=this.options.ticks.setContext(this.getContext(e));return X(t.font)}_maxDigits(){let e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}},no=class{constructor(e,t,n){this.type=e,this.scope=t,this.override=n,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){let t=Object.getPrototypeOf(e),n;ao(t)&&(n=this.register(t));let r=this.items,i=e.id,a=this.scope+`.`+i;if(!i)throw Error(`class does not have id: `+e);return i in r?a:(r[i]=e,ro(e,a,n),this.override&&J.override(e.id,e.overrides),a)}get(e){return this.items[e]}unregister(e){let t=this.items,n=e.id,r=this.scope;n in t&&delete t[n],r&&n in J[r]&&(delete J[r][n],this.override&&delete vn[n])}};function ro(e,t,n){let r=at(Object.create(null),[n?J.get(n):{},J.get(t),e.defaults]);J.set(t,r),e.defaultRoutes&&io(t,e.defaultRoutes),e.descriptors&&J.describe(t,e.descriptors)}function io(e,t){Object.keys(t).forEach(n=>{let r=n.split(`.`),i=r.pop(),a=[e].concat(r).join(`.`),o=t[n].split(`.`),s=o.pop(),c=o.join(`.`);J.route(a,i,c,s)})}function ao(e){return`id`in e&&`defaults`in e}var oo=class{constructor(){this.controllers=new no(Li,`datasets`,!0),this.elements=new no(Fa,`elements`),this.plugins=new no(Object,`plugins`),this.scales=new no(to,`scales`),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each(`register`,e)}remove(...e){this._each(`unregister`,e)}addControllers(...e){this._each(`register`,e,this.controllers)}addElements(...e){this._each(`register`,e,this.elements)}addPlugins(...e){this._each(`register`,e,this.plugins)}addScales(...e){this._each(`register`,e,this.scales)}getController(e){return this._get(e,this.controllers,`controller`)}getElement(e){return this._get(e,this.elements,`element`)}getPlugin(e){return this._get(e,this.plugins,`plugin`)}getScale(e){return this._get(e,this.scales,`scale`)}removeControllers(...e){this._each(`unregister`,e,this.controllers)}removeElements(...e){this._each(`unregister`,e,this.elements)}removePlugins(...e){this._each(`unregister`,e,this.plugins)}removeScales(...e){this._each(`unregister`,e,this.scales)}_each(e,t,n){[...t].forEach(t=>{let r=n||this._getRegistryForType(t);n||r.isForType(t)||r===this.plugins&&t.id?this._exec(e,r,t):V(t,t=>{let r=n||this._getRegistryForType(t);this._exec(e,r,t)})})}_exec(e,t,n){let r=ft(e);B(n[`before`+r],[],n),t[e](n),B(n[`after`+r],[],n)}_getRegistryForType(e){for(let t=0;t<this._typedRegistries.length;t++){let n=this._typedRegistries[t];if(n.isForType(e))return n}return this.plugins}_get(e,t,n){let r=t.get(e);if(r===void 0)throw Error(`"`+e+`" is not a registered `+n+`.`);return r}},so=new oo,co=class{constructor(){this._init=[]}notify(e,t,n,r){t===`beforeInit`&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,`install`));let i=r?this._descriptors(e).filter(r):this._descriptors(e),a=this._notify(i,e,t,n);return t===`afterDestroy`&&(this._notify(i,e,`stop`),this._notify(this._init,e,`uninstall`)),a}_notify(e,t,n,r){r||={};for(let i of e){let e=i.plugin,a=e[n],o=[t,r,i.options];if(B(a,o,e)===!1&&r.cancelable)return!1}return!0}invalidate(){P(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;let t=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),t}_createDescriptors(e,t){let n=e&&e.config,r=z(n.options&&n.options.plugins,{}),i=lo(n);return r===!1&&!t?[]:fo(e,i,r,t)}_notifyStateChanges(e){let t=this._oldCache||[],n=this._cache,r=(e,t)=>e.filter(e=>!t.some(t=>e.plugin.id===t.plugin.id));this._notify(r(t,n),e,`stop`),this._notify(r(n,t),e,`start`)}};function lo(e){let t={},n=[],r=Object.keys(so.plugins.items);for(let e=0;e<r.length;e++)n.push(so.getPlugin(r[e]));let i=e.plugins||[];for(let e=0;e<i.length;e++){let r=i[e];n.indexOf(r)===-1&&(n.push(r),t[r.id]=!0)}return{plugins:n,localIds:t}}function uo(e,t){return!t&&e===!1?null:e===!0?{}:e}function fo(e,{plugins:t,localIds:n},r,i){let a=[],o=e.getContext();for(let s of t){let t=s.id,c=uo(r[t],i);if(c===null)continue;a.push({plugin:s,options:po(e.config,{plugin:s,local:n[t]},c,o)})}return a}function po(e,{plugin:t,local:n},r,i){let a=e.pluginScopeKeys(t),o=e.getOptionScopes(r,a);return n&&t.defaults&&o.push(t.defaults),e.createResolver(o,i,[``],{scriptable:!1,indexable:!1,allKeys:!0})}function mo(e,t){let n=J.datasets[e]||{},r=(t.datasets||{})[e]||{};return r.indexAxis||t.indexAxis||n.indexAxis||`x`}function ho(e,t){let n=e;return e===`_index_`?n=t:e===`_value_`&&(n=t===`x`?`y`:`x`),n}function go(e,t){return e===t?`_index_`:`_value_`}function _o(e){if(e===`x`||e===`y`||e===`r`)return e}function vo(e){if(e===`top`||e===`bottom`)return`x`;if(e===`left`||e===`right`)return`y`}function yo(e,...t){if(_o(e))return e;for(let n of t){let t=n.axis||vo(n.position)||e.length>1&&_o(e[0].toLowerCase());if(t)return t}throw Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function bo(e,t,n){if(n[t+`AxisID`]===e)return{axis:t}}function xo(e,t){if(t.data&&t.data.datasets){let n=t.data.datasets.filter(t=>t.xAxisID===e||t.yAxisID===e);if(n.length)return bo(e,`x`,n[0])||bo(e,`y`,n[0])}return{}}function So(e,t){let n=vn[e.type]||{scales:{}},r=t.scales||{},i=mo(e.type,t),a=Object.create(null);return Object.keys(r).forEach(t=>{let o=r[t];if(!I(o))return console.error(`Invalid scale configuration for scale: ${t}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${t}`);let s=yo(t,o,xo(t,e),J.scales[o.type]),c=go(s,i),l=n.scales||{};a[t]=ot(Object.create(null),[{axis:s},o,l[s],l[c]])}),e.data.datasets.forEach(n=>{let i=n.type||e.type,o=n.indexAxis||mo(i,t),s=vn[i]||{},c=s.scales||{};Object.keys(c).forEach(e=>{let t=ho(e,o),i=n[t+`AxisID`]||t;a[i]=a[i]||Object.create(null),ot(a[i],[{axis:t},r[i],c[e]])})}),Object.keys(a).forEach(e=>{let t=a[e];ot(t,[J.scales[t.type],J.scale])}),a}function Co(e){let t=e.options||={};t.plugins=z(t.plugins,{}),t.scales=So(e,t)}function wo(e){return e||={},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function To(e){return e||={},e.data=wo(e.data),Co(e),e}const Eo=new Map,Do=new Set;function Oo(e,t){let n=Eo.get(e);return n||(n=t(),Eo.set(e,n),Do.add(n)),n}const ko=(e,t,n)=>{let r=dt(t,n);r!==void 0&&e.add(r)};var Ao=class{constructor(e){this._config=To(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=wo(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){let e=this._config;this.clearCache(),Co(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Oo(e,()=>[[`datasets.${e}`,``]])}datasetAnimationScopeKeys(e,t){return Oo(`${e}.transition.${t}`,()=>[[`datasets.${e}.transitions.${t}`,`transitions.${t}`],[`datasets.${e}`,``]])}datasetElementScopeKeys(e,t){return Oo(`${e}-${t}`,()=>[[`datasets.${e}.elements.${t}`,`datasets.${e}`,`elements.${t}`,``]])}pluginScopeKeys(e){let t=e.id,n=this.type;return Oo(`${n}-plugin-${t}`,()=>[[`plugins.${t}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,t){let n=this._scopeCache,r=n.get(e);return(!r||t)&&(r=new Map,n.set(e,r)),r}getOptionScopes(e,t,n){let{options:r,type:i}=this,a=this._cachedScopes(e,n),o=a.get(t);if(o)return o;let s=new Set;t.forEach(t=>{e&&(s.add(e),t.forEach(t=>ko(s,e,t))),t.forEach(e=>ko(s,r,e)),t.forEach(e=>ko(s,vn[i]||{},e)),t.forEach(e=>ko(s,J,e)),t.forEach(e=>ko(s,yn,e))});let c=Array.from(s);return c.length===0&&c.push(Object.create(null)),Do.has(t)&&a.set(t,c),c}chartOptionScopes(){let{options:e,type:t}=this;return[e,vn[t]||{},J.datasets[t]||{},{type:t},J,yn]}resolveNamedOptions(e,t,n,r=[``]){let i={$shared:!0},{resolver:a,subPrefixes:o}=jo(this._resolverCache,e,r),s=a;if(No(a,t)){i.$shared=!1,n=mt(n)?n():n;let t=this.createResolver(e,n,o);s=Zn(a,n,t)}for(let e of t)i[e]=s[e];return i}createResolver(e,t,n=[``],r){let{resolver:i}=jo(this._resolverCache,e,n);return I(t)?Zn(i,t,void 0,r):i}};function jo(e,t,n){let r=e.get(t);r||(r=new Map,e.set(t,r));let i=n.join(),a=r.get(i);if(!a){let e=Xn(t,n);a={resolver:e,subPrefixes:n.filter(e=>!e.toLowerCase().includes(`hover`))},r.set(i,a)}return a}const Mo=e=>I(e)&&Object.getOwnPropertyNames(e).some(t=>mt(e[t]));function No(e,t){let{isScriptable:n,isIndexable:r}=Qn(e);for(let i of t){let t=n(i),a=r(i),o=(a||t)&&e[i];if(t&&(mt(o)||Mo(o))||a&&F(o))return!0}return!1}var Po=`4.5.0`;const Fo=[`top`,`bottom`,`left`,`right`,`chartArea`];function Io(e,t){return e===`top`||e===`bottom`||Fo.indexOf(e)===-1&&t===`x`}function Lo(e,t){return function(n,r){return n[e]===r[e]?n[t]-r[t]:n[e]-r[e]}}function Ro(e){let t=e.chart,n=t.options.animation;t.notifyPlugins(`afterRender`),B(n&&n.onComplete,[e],t)}function zo(e){let t=e.chart,n=t.options.animation;B(n&&n.onProgress,[e],t)}function Bo(e){return Tr()&&typeof e==`string`?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const Vo={},Ho=e=>{let t=Bo(e);return Object.values(Vo).filter(e=>e.canvas===t).pop()};function Uo(e,t,n){let r=Object.keys(e);for(let i of r){let r=+i;if(r>=t){let a=e[i];delete e[i],(n>0||r>t)&&(e[r+n]=a)}}}function Wo(e,t,n,r){return!n||e.type===`mouseout`?null:r?t:e}var Go=class{static defaults=J;static instances=Vo;static overrides=vn;static registry=so;static version=Po;static getChart=Ho;static register(...e){so.add(...e),Ko()}static unregister(...e){so.remove(...e),Ko()}constructor(e,t){let n=this.config=new Ao(t),r=Bo(e),i=Ho(r);if(i)throw Error(`Canvas is already in use. Chart with ID '`+i.id+`' must be destroyed before the canvas with ID '`+i.canvas.id+`' can be reused.`);let a=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||(Pa(r))),this.platform.updateConfig(n);let o=this.platform.acquireContext(r,a.aspectRatio),s=o&&o.canvas,c=s&&s.height,l=s&&s.width;if(this.id=$e(),this.ctx=o,this.canvas=s,this.width=l,this.height=c,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new co,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Xt(e=>this.update(e),a.resizeDelay||0),this._dataChanges=[],Vo[this.id]=this,!o||!s){console.error(`Failed to create chart: can't acquire context from the given item`);return}di.listen(this,`complete`,Ro),di.listen(this,`progress`,zo),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:e,maintainAspectRatio:t},width:n,height:r,_aspectRatio:i}=this;return P(e)?t&&i?i:r?n/r:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return so}_initialize(){return this.notifyPlugins(`beforeInit`),this.options.responsive?this.resize():Rr(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins(`afterInit`),this}clear(){return Dn(this.canvas,this.ctx),this}stop(){return di.stop(this),this}resize(e,t){di.running(this)?this._resizeBeforeDraw={width:e,height:t}:this._resize(e,t)}_resize(e,t){let n=this.options,r=this.canvas,i=n.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(r,e,t,i),o=n.devicePixelRatio||this.platform.getDevicePixelRatio(),s=this.width?`resize`:`attach`;this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,Rr(this,o,!0)&&(this.notifyPlugins(`resize`,{size:a}),B(n.onResize,[this,a],this),this.attached&&this._doResize(s)&&this.render())}ensureScalesHaveIDs(){let e=this.options,t=e.scales||{};V(t,(e,t)=>{e.id=t})}buildOrUpdateScales(){let e=this.options,t=e.scales,n=this.scales,r=Object.keys(n).reduce((e,t)=>(e[t]=!1,e),{}),i=[];t&&(i=i.concat(Object.keys(t).map(e=>{let n=t[e],r=yo(e,n),i=r===`r`,a=r===`x`;return{options:n,dposition:i?`chartArea`:a?`bottom`:`left`,dtype:i?`radialLinear`:a?`category`:`linear`}}))),V(i,t=>{let i=t.options,a=i.id,o=yo(a,i),s=z(i.type,t.dtype);(i.position===void 0||Io(i.position,o)!==Io(t.dposition))&&(i.position=t.dposition),r[a]=!0;let c=null;if(a in n&&n[a].type===s)c=n[a];else{let e=so.getScale(s);c=new e({id:a,type:s,ctx:this.ctx,chart:this}),n[c.id]=c}c.init(i,e)}),V(r,(e,t)=>{e||delete n[t]}),V(n,e=>{Z.configure(this,e,e.options),Z.addBox(this,e)})}_updateMetasets(){let e=this._metasets,t=this.data.datasets.length,n=e.length;if(e.sort((e,t)=>e.index-t.index),n>t){for(let e=t;e<n;++e)this._destroyDatasetMeta(e);e.splice(t,n-t)}this._sortedMetasets=e.slice(0).sort(Lo(`order`,`index`))}_removeUnreferencedMetasets(){let{_metasets:e,data:{datasets:t}}=this;e.length>t.length&&delete this._stacks,e.forEach((e,n)=>{t.filter(t=>t===e._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){let e=[],t=this.data.datasets,n,r;for(this._removeUnreferencedMetasets(),n=0,r=t.length;n<r;n++){let r=t[n],i=this.getDatasetMeta(n),a=r.type||this.config.type;if(i.type&&i.type!==a&&(this._destroyDatasetMeta(n),i=this.getDatasetMeta(n)),i.type=a,i.indexAxis=r.indexAxis||mo(a,this.options),i.order=r.order||0,i.index=n,i.label=``+r.label,i.visible=this.isDatasetVisible(n),i.controller)i.controller.updateIndex(n),i.controller.linkScales();else{let t=so.getController(a),{datasetElementType:r,dataElementType:o}=J.datasets[a];Object.assign(t,{dataElementType:so.getElement(o),datasetElementType:r&&so.getElement(r)}),i.controller=new t(this,n),e.push(i.controller)}}return this._updateMetasets(),e}_resetElements(){V(this.data.datasets,(e,t)=>{this.getDatasetMeta(t).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins(`reset`)}update(e){let t=this.config;t.update();let n=this._options=t.createResolver(t.chartOptionScopes(),this.getContext()),r=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins(`beforeUpdate`,{mode:e,cancelable:!0})===!1)return;let i=this.buildOrUpdateControllers();this.notifyPlugins(`beforeElementsUpdate`);let a=0;for(let e=0,t=this.data.datasets.length;e<t;e++){let{controller:t}=this.getDatasetMeta(e),n=!r&&i.indexOf(t)===-1;t.buildOrUpdateElements(n),a=Math.max(+t.getMaxOverflow(),a)}a=this._minPadding=n.layout.autoPadding?a:0,this._updateLayout(a),r||V(i,e=>{e.reset()}),this._updateDatasets(e),this.notifyPlugins(`afterUpdate`,{mode:e}),this._layers.sort(Lo(`z`,`_idx`));let{_active:o,_lastEvent:s}=this;s?this._eventHandler(s,!0):o.length&&this._updateHoverStyles(o,o,!0),this.render()}_updateScales(){V(this.scales,e=>{Z.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let e=this.options,t=new Set(Object.keys(this._listeners)),n=new Set(e.events);(!ht(t,n)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:e}=this,t=this._getUniformDataChanges()||[];for(let{method:n,start:r,count:i}of t){let t=n===`_removeElements`?-i:i;Uo(e,r,t)}}_getUniformDataChanges(){let e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];let t=this.data.datasets.length,n=t=>new Set(e.filter(e=>e[0]===t).map((e,t)=>t+`,`+e.splice(1).join(`,`))),r=n(0);for(let e=1;e<t;e++)if(!ht(r,n(e)))return;return Array.from(r).map(e=>e.split(`,`)).map(e=>({method:e[1],start:+e[2],count:+e[3]}))}_updateLayout(e){if(this.notifyPlugins(`beforeLayout`,{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,e);let t=this.chartArea,n=t.width<=0||t.height<=0;this._layers=[],V(this.boxes,e=>{n&&e.position===`chartArea`||(e.configure&&e.configure(),this._layers.push(...e._layers()))},this),this._layers.forEach((e,t)=>{e._idx=t}),this.notifyPlugins(`afterLayout`)}_updateDatasets(e){if(this.notifyPlugins(`beforeDatasetsUpdate`,{mode:e,cancelable:!0})!==!1){for(let e=0,t=this.data.datasets.length;e<t;++e)this.getDatasetMeta(e).controller.configure();for(let t=0,n=this.data.datasets.length;t<n;++t)this._updateDataset(t,mt(e)?e({datasetIndex:t}):e);this.notifyPlugins(`afterDatasetsUpdate`,{mode:e})}}_updateDataset(e,t){let n=this.getDatasetMeta(e),r={meta:n,index:e,mode:t,cancelable:!0};this.notifyPlugins(`beforeDatasetUpdate`,r)!==!1&&(n.controller._update(t),r.cancelable=!1,this.notifyPlugins(`afterDatasetUpdate`,r))}render(){this.notifyPlugins(`beforeRender`,{cancelable:!0})!==!1&&(di.has(this)?this.attached&&!di.running(this)&&di.start(this):(this.draw(),Ro({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){let{width:e,height:t}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(e,t)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins(`beforeDraw`,{cancelable:!0})===!1)return;let t=this._layers;for(e=0;e<t.length&&t[e].z<=0;++e)t[e].draw(this.chartArea);for(this._drawDatasets();e<t.length;++e)t[e].draw(this.chartArea);this.notifyPlugins(`afterDraw`)}_getSortedDatasetMetas(e){let t=this._sortedMetasets,n=[],r,i;for(r=0,i=t.length;r<i;++r){let i=t[r];(!e||i.visible)&&n.push(i)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins(`beforeDatasetsDraw`,{cancelable:!0})===!1)return;let e=this.getSortedVisibleDatasetMetas();for(let t=e.length-1;t>=0;--t)this._drawDataset(e[t]);this.notifyPlugins(`afterDatasetsDraw`)}_drawDataset(e){let t=this.ctx,n={meta:e,index:e.index,cancelable:!0},r=li(this,e);this.notifyPlugins(`beforeDatasetDraw`,n)!==!1&&(r&&jn(t,r),e.controller.draw(),r&&Mn(t),n.cancelable=!1,this.notifyPlugins(`afterDatasetDraw`,n))}isPointInArea(e){return An(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,t,n,r){let i=Yi.modes[t];return typeof i==`function`?i(this,e,n,r):[]}getDatasetMeta(e){let t=this.data.datasets[e],n=this._metasets,r=n.filter(e=>e&&e._dataset===t).pop();return r||(r={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:t&&t.order||0,index:e,_dataset:t,_parsed:[],_sorted:!1},n.push(r)),r}getContext(){return this.$context||=Yn(null,{chart:this,type:`chart`})}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){let t=this.data.datasets[e];if(!t)return!1;let n=this.getDatasetMeta(e);return typeof n.hidden==`boolean`?!n.hidden:!t.hidden}setDatasetVisibility(e,t){let n=this.getDatasetMeta(e);n.hidden=!t}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,t,n){let r=n?`show`:`hide`,i=this.getDatasetMeta(e),a=i.controller._resolveAnimations(void 0,r);pt(t)?(i.data[t].hidden=!n,this.update()):(this.setDatasetVisibility(e,n),a.update(i,{visible:n}),this.update(t=>t.datasetIndex===e?r:void 0))}hide(e,t){this._updateVisibility(e,t,!1)}show(e,t){this._updateVisibility(e,t,!0)}_destroyDatasetMeta(e){let t=this._metasets[e];t&&t.controller&&t.controller._destroy(),delete this._metasets[e]}_stop(){let e,t;for(this.stop(),di.remove(this),e=0,t=this.data.datasets.length;e<t;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins(`beforeDestroy`);let{canvas:e,ctx:t}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Dn(e,t),this.platform.releaseContext(t),this.canvas=null,this.ctx=null),delete Vo[this.id],this.notifyPlugins(`afterDestroy`)}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let e=this._listeners,t=this.platform,n=(n,r)=>{t.addEventListener(this,n,r),e[n]=r},r=(e,t,n)=>{e.offsetX=t,e.offsetY=n,this._eventHandler(e)};V(this.options.events,e=>n(e,r))}bindResponsiveEvents(){this._responsiveListeners||={};let e=this._responsiveListeners,t=this.platform,n=(n,r)=>{t.addEventListener(this,n,r),e[n]=r},r=(n,r)=>{e[n]&&(t.removeEventListener(this,n,r),delete e[n])},i=(e,t)=>{this.canvas&&this.resize(e,t)},a,o=()=>{r(`attach`,o),this.attached=!0,this.resize(),n(`resize`,i),n(`detach`,a)};a=()=>{this.attached=!1,r(`resize`,i),this._stop(),this._resize(0,0),n(`attach`,o)},t.isAttached(this.canvas)?o():a()}unbindEvents(){V(this._listeners,(e,t)=>{this.platform.removeEventListener(this,t,e)}),this._listeners={},V(this._responsiveListeners,(e,t)=>{this.platform.removeEventListener(this,t,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,t,n){let r=n?`set`:`remove`,i,a,o,s;for(t===`dataset`&&(i=this.getDatasetMeta(e[0].datasetIndex),i.controller[`_`+r+`DatasetHoverStyle`]()),o=0,s=e.length;o<s;++o){a=e[o];let t=a&&this.getDatasetMeta(a.datasetIndex).controller;t&&t[r+`HoverStyle`](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){let t=this._active||[],n=e.map(({datasetIndex:e,index:t})=>{let n=this.getDatasetMeta(e);if(!n)throw Error(`No dataset found at index `+e);return{datasetIndex:e,element:n.data[t],index:t}}),r=!tt(n,t);r&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,t))}notifyPlugins(e,t,n){return this._plugins.notify(this,e,t,n)}isPluginEnabled(e){return this._plugins._cache.filter(t=>t.plugin.id===e).length===1}_updateHoverStyles(e,t,n){let r=this.options.hover,i=(e,t)=>e.filter(e=>!t.some(t=>e.datasetIndex===t.datasetIndex&&e.index===t.index)),a=i(t,e),o=n?e:i(e,t);a.length&&this.updateHoverStyle(a,r.mode,!1),o.length&&r.mode&&this.updateHoverStyle(o,r.mode,!0)}_eventHandler(e,t){let n={event:e,replay:t,cancelable:!0,inChartArea:this.isPointInArea(e)},r=t=>(t.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins(`beforeEvent`,n,r)===!1)return;let i=this._handleEvent(e,t,n.inChartArea);return n.cancelable=!1,this.notifyPlugins(`afterEvent`,n,r),(i||n.changed)&&this.render(),this}_handleEvent(e,t,n){let{_active:r=[],options:i}=this,a=t,o=this._getActiveElements(e,r,n,a),s=gt(e),c=Wo(e,this._lastEvent,n,s);n&&(this._lastEvent=null,B(i.onHover,[e,o,this],this),s&&B(i.onClick,[e,o,this],this));let l=!tt(o,r);return(l||t)&&(this._active=o,this._updateHoverStyles(o,r,t)),this._lastEvent=c,l}_getActiveElements(e,t,n,r){if(e.type===`mouseout`)return[];if(!n)return t;let i=this.options.hover;return this.getElementsAtEventForMode(e,i.mode,i,r)}};function Ko(){return V(Go.instances,e=>e._plugins.invalidate())}function qo(e,t,n){let{startAngle:r,x:i,y:a,outerRadius:o,innerRadius:s,options:c}=t,{borderWidth:l,borderJoinStyle:u}=c,d=Math.min(l/o,G(r-n));if(e.beginPath(),e.arc(i,a,o-l/2,r+d/2,n-d/2),s>0){let t=Math.min(l/s,G(r-n));e.arc(i,a,s+l/2,n-t/2,r+t/2,!0)}else{let t=Math.min(l/2,o*G(r-n));if(u===`round`)e.arc(i,a,t,n-H/2,r+H/2,!0);else if(u===`bevel`){let o=2*t*t,s=-o*Math.cos(n+H/2)+i,c=-o*Math.sin(n+H/2)+a,l=o*Math.cos(r+H/2)+i,u=o*Math.sin(r+H/2)+a;e.lineTo(s,c),e.lineTo(l,u)}}e.closePath(),e.moveTo(0,0),e.rect(0,0,e.canvas.width,e.canvas.height),e.clip(`evenodd`)}function Jo(e,t,n){let{startAngle:r,pixelMargin:i,x:a,y:o,outerRadius:s,innerRadius:c}=t,l=i/s;e.beginPath(),e.arc(a,o,s,r-l,n+l),c>i?(l=i/c,e.arc(a,o,c,n+l,r-l,!0)):e.arc(a,o,i,n+W,r-W),e.closePath(),e.clip()}function Yo(e){return Wn(e,[`outerStart`,`outerEnd`,`innerStart`,`innerEnd`])}function Xo(e,t,n,r){let i=Yo(e.options.borderRadius),a=(n-t)/2,o=Math.min(a,r*t/2),s=e=>{let t=(n-Math.min(a,e))*r/2;return K(e,0,Math.min(a,t))};return{outerStart:s(i.outerStart),outerEnd:s(i.outerEnd),innerStart:K(i.innerStart,0,o),innerEnd:K(i.innerEnd,0,o)}}function Zo(e,t,n,r){return{x:n+e*Math.cos(t),y:r+e*Math.sin(t)}}function Qo(e,t,n,r,i,a){let{x:o,y:s,startAngle:c,pixelMargin:l,innerRadius:u}=t,d=Math.max(t.outerRadius+r+n-l,0),f=u>0?u+r+n+l:0,p=0,m=i-c;if(r){let e=u>0?u-r:0,t=d>0?d-r:0,n=(e+t)/2,i=n===0?m:m*n/(n+r);p=(m-i)/2}let h=Math.max(.001,m*d-n/H)/d,g=(m-h)/2,_=c+g+p,v=i-g-p,{outerStart:y,outerEnd:b,innerStart:x,innerEnd:S}=Xo(t,f,d,v-_),C=d-y,w=d-b,T=_+y/C,E=v-b/w,D=f+x,O=f+S,ee=_+x/D,te=v-S/O;if(e.beginPath(),a){let t=(T+E)/2;if(e.arc(o,s,d,T,t),e.arc(o,s,d,t,E),b>0){let t=Zo(w,E,o,s);e.arc(t.x,t.y,b,E,v+W)}let n=Zo(O,v,o,s);if(e.lineTo(n.x,n.y),S>0){let t=Zo(O,te,o,s);e.arc(t.x,t.y,S,v+W,te+Math.PI)}let r=(v-S/f+(_+x/f))/2;if(e.arc(o,s,f,v-S/f,r,!0),e.arc(o,s,f,r,_+x/f,!0),x>0){let t=Zo(D,ee,o,s);e.arc(t.x,t.y,x,ee+Math.PI,_-W)}let i=Zo(C,_,o,s);if(e.lineTo(i.x,i.y),y>0){let t=Zo(C,T,o,s);e.arc(t.x,t.y,y,_-W,T)}}else{e.moveTo(o,s);let t=Math.cos(T)*d+o,n=Math.sin(T)*d+s;e.lineTo(t,n);let r=Math.cos(E)*d+o,i=Math.sin(E)*d+s;e.lineTo(r,i)}e.closePath()}function $o(e,t,n,r,i){let{fullCircles:a,startAngle:o,circumference:s}=t,c=t.endAngle;if(a){Qo(e,t,n,r,c,i);for(let t=0;t<a;++t)e.fill();isNaN(s)||(c=o+(s%U||U))}return Qo(e,t,n,r,c,i),e.fill(),c}function es(e,t,n,r,i){let{fullCircles:a,startAngle:o,circumference:s,options:c}=t,{borderWidth:l,borderJoinStyle:u,borderDash:d,borderDashOffset:f,borderRadius:p}=c,m=c.borderAlign===`inner`;if(!l)return;e.setLineDash(d||[]),e.lineDashOffset=f,m?(e.lineWidth=l*2,e.lineJoin=u||`round`):(e.lineWidth=l,e.lineJoin=u||`bevel`);let h=t.endAngle;if(a){Qo(e,t,n,r,h,i);for(let t=0;t<a;++t)e.stroke();isNaN(s)||(h=o+(s%U||U))}m&&Jo(e,t,h),c.selfJoin&&h-o>=H&&p===0&&u!==`miter`&&qo(e,t,h),a||(Qo(e,t,n,r,h,i),e.stroke())}var ts=class extends Fa{static id=`arc`;static defaults={borderAlign:`center`,borderColor:`#fff`,borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:`backgroundColor`};static descriptors={_scriptable:!0,_indexable:e=>e!==`borderDash`};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(e){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,t,n){let r=this.getProps([`x`,`y`],n),{angle:i,distance:a}=Pt(r,{x:e,y:t}),{startAngle:o,endAngle:s,innerRadius:c,outerRadius:l,circumference:u}=this.getProps([`startAngle`,`endAngle`,`innerRadius`,`outerRadius`,`circumference`],n),d=(this.options.spacing+this.options.borderWidth)/2,f=z(u,s-o),p=Lt(i,o,s)&&o!==s,m=f>=U||p,h=zt(a,c+d,l+d);return m&&h}getCenterPoint(e){let{x:t,y:n,startAngle:r,endAngle:i,innerRadius:a,outerRadius:o}=this.getProps([`x`,`y`,`startAngle`,`endAngle`,`innerRadius`,`outerRadius`],e),{offset:s,spacing:c}=this.options,l=(r+i)/2,u=(a+o+c+s)/2;return{x:t+Math.cos(l)*u,y:n+Math.sin(l)*u}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){let{options:t,circumference:n}=this,r=(t.offset||0)/4,i=(t.spacing||0)/2,a=t.circular;if(this.pixelMargin=t.borderAlign===`inner`?.33:0,this.fullCircles=n>U?Math.floor(n/U):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();let o=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(o)*r,Math.sin(o)*r);let s=1-Math.sin(Math.min(H,n||0)),c=r*s;e.fillStyle=t.backgroundColor,e.strokeStyle=t.borderColor,$o(e,this,c,i,a),es(e,this,c,i,a),e.restore()}};function ns(e,t,n=t){e.lineCap=z(n.borderCapStyle,t.borderCapStyle),e.setLineDash(z(n.borderDash,t.borderDash)),e.lineDashOffset=z(n.borderDashOffset,t.borderDashOffset),e.lineJoin=z(n.borderJoinStyle,t.borderJoinStyle),e.lineWidth=z(n.borderWidth,t.borderWidth),e.strokeStyle=z(n.borderColor,t.borderColor)}function rs(e,t,n){e.lineTo(n.x,n.y)}function os(e){return e.stepped?Nn:e.tension||e.cubicInterpolationMode===`monotone`?Pn:rs}function ss(e,t,n={}){let r=e.length,{start:i=0,end:a=r-1}=n,{start:o,end:s}=t,c=Math.max(i,o),l=Math.min(a,s),u=i<o&&a<o||i>s&&a>s;return{count:r,start:c,loop:t.loop,ilen:l<c&&!u?r+l-c:l-c}}function cs(e,t,n,r){let{points:i,options:a}=t,{count:o,start:s,loop:c,ilen:l}=ss(i,n,r),u=os(a),{move:d=!0,reverse:f}=r||{},p,m,h;for(p=0;p<=l;++p)m=i[(s+(f?l-p:p))%o],!m.skip&&(d?(e.moveTo(m.x,m.y),d=!1):u(e,h,m,f,a.stepped),h=m);return c&&(m=i[(s+(f?l:0))%o],u(e,h,m,f,a.stepped)),!!c}function ls(e,t,n,r){let i=t.points,{count:a,start:o,ilen:s}=ss(i,n,r),{move:c=!0,reverse:l}=r||{},u=0,d=0,f,p,m,h,g,_,v=e=>(o+(l?s-e:e))%a,y=()=>{h!==g&&(e.lineTo(u,g),e.lineTo(u,h),e.lineTo(u,_))};for(c&&(p=i[v(0)],e.moveTo(p.x,p.y)),f=0;f<=s;++f){if(p=i[v(f)],p.skip)continue;let t=p.x,n=p.y,r=t|0;r===m?(n<h?h=n:n>g&&(g=n),u=(d*u+t)/++d):(y(),e.lineTo(t,n),m=r,d=0,h=g=n),_=n}y()}function us(e){let t=e.options,n=t.borderDash&&t.borderDash.length,r=!e._decimated&&!e._loop&&!t.tension&&t.cubicInterpolationMode!==`monotone`&&!t.stepped&&!n;return r?ls:cs}function ds(e){return e.stepped?Hr:e.tension||e.cubicInterpolationMode===`monotone`?Ur:Vr}function fs(e,t,n,r){let i=t._path;i||(i=t._path=new Path2D,t.path(i,n,r)&&i.closePath()),ns(e,t.options),e.stroke(i)}function ps(e,t,n,r){let{segments:i,options:a}=t,o=us(t);for(let s of i)ns(e,a,s.style),e.beginPath(),o(e,t,s,{start:n,end:n+r-1})&&e.closePath(),e.stroke()}const ms=typeof Path2D==`function`;function hs(e,t,n,r){ms&&!t.options.segment?fs(e,t,n,r):ps(e,t,n,r)}var gs=class extends Fa{static id=`line`;static defaults={borderCapStyle:`butt`,borderDash:[],borderDashOffset:0,borderJoinStyle:`miter`,borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:`default`,fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:`backgroundColor`,borderColor:`borderColor`};static descriptors={_scriptable:!0,_indexable:e=>e!==`borderDash`&&e!==`fill`};constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,t){let n=this.options;if((n.tension||n.cubicInterpolationMode===`monotone`)&&!n.stepped&&!this._pointsUpdated){let r=n.spanGaps?this._loop:this._fullLoop;wr(this._points,n,e,r,t),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||=ni(this,this.options.segment)}first(){let e=this.segments,t=this.points;return e.length&&t[e[0].start]}last(){let e=this.segments,t=this.points,n=e.length;return n&&t[e[n-1].end]}interpolate(e,t){let n=this.options,r=e[t],i=this.points,a=$r(this,{property:t,start:r,end:r});if(!a.length)return;let o=[],s=ds(n),c,l;for(c=0,l=a.length;c<l;++c){let{start:l,end:u}=a[c],d=i[l],f=i[u];if(d===f){o.push(d);continue}let p=Math.abs((r-d[t])/(f[t]-d[t])),m=s(d,f,p,n.stepped);m[t]=e[t],o.push(m)}return o.length===1?o[0]:o}pathSegment(e,t,n){let r=us(this);return r(e,this,t,n)}path(e,t,n){let r=this.segments,i=us(this),a=this._loop;t||=0,n||=this.points.length-t;for(let o of r)a&=i(e,this,o,{start:t,end:t+n-1});return!!a}draw(e,t,n,r){let i=this.options||{},a=this.points||[];a.length&&i.borderWidth&&(e.save(),hs(e,this,n,r),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}};function _s(e,t,n,r){let i=e.options,{[n]:a}=e.getProps([n],r);return Math.abs(t-a)<i.radius+i.hitRadius}var vs=class extends Fa{static id=`point`;parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:`circle`,radius:3,rotation:0};static defaultRoutes={backgroundColor:`backgroundColor`,borderColor:`borderColor`};constructor(e){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,t,n){let r=this.options,{x:i,y:a}=this.getProps([`x`,`y`],n);return(e-i)**2+(t-a)**2<(r.hitRadius+r.radius)**2}inXRange(e,t){return _s(this,e,`x`,t)}inYRange(e,t){return _s(this,e,`y`,t)}getCenterPoint(e){let{x:t,y:n}=this.getProps([`x`,`y`],e);return{x:t,y:n}}size(e){e=e||this.options||{};let t=e.radius||0;t=Math.max(t,t&&e.hoverRadius||0);let n=t&&e.borderWidth||0;return(t+n)*2}draw(e,t){let n=this.options;this.skip||n.radius<.1||!An(this,t,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,On(e,n,this.x,this.y))}getRange(){let e=this.options||{};return e.radius+e.hitRadius}};function ys(e,t,n){let r=e.segments,i=e.points,a=t.points,o=[];for(let e of r){let{start:r,end:s}=e;s=Ss(r,s,i);let c=bs(n,i[r],i[s],e.loop);if(!t.segments){o.push({source:e,target:c,start:i[r],end:i[s]});continue}let l=$r(t,c);for(let t of l){let r=bs(n,a[t.start],a[t.end],t.loop),s=Qr(e,i,r);for(let e of s)o.push({source:e,target:t,start:{[n]:Cs(c,r,`start`,Math.max)},end:{[n]:Cs(c,r,`end`,Math.min)}})}}return o}function bs(e,t,n,r){if(r)return;let i=t[e],a=n[e];return e===`angle`&&(i=G(i),a=G(a)),{property:e,start:i,end:a}}function xs(e,t){let{x:n=null,y:r=null}=e||{},i=t.points,a=[];return t.segments.forEach(({start:e,end:t})=>{t=Ss(e,t,i);let o=i[e],s=i[t];r===null?n!==null&&(a.push({x:n,y:o.y}),a.push({x:n,y:s.y})):(a.push({x:o.x,y:r}),a.push({x:s.x,y:r}))}),a}function Ss(e,t,n){for(;t>e;t--){let e=n[t];if(!isNaN(e.x)&&!isNaN(e.y))break}return t}function Cs(e,t,n,r){return e&&t?r(e[n],t[n]):e?e[n]:t?t[n]:0}function ws(e,t){let n=[],r=!1;return F(e)?(r=!0,n=e):n=xs(e,t),n.length?new gs({points:n,options:{tension:0},_loop:r,_fullLoop:r}):null}function Ts(e){return e&&e.fill!==!1}function Es(e,t,n){let r=e[t],i=r.fill,a=[t],o;if(!n)return i;for(;i!==!1&&a.indexOf(i)===-1;){if(!L(i))return i;if(o=e[i],!o)return!1;if(o.visible)return i;a.push(i),i=o.fill}return!1}function Ds(e,t,n){let r=js(e);if(I(r))return isNaN(r.value)?!1:r;let i=parseFloat(r);return L(i)&&Math.floor(i)===i?Os(r[0],t,i,n):[`origin`,`start`,`end`,`stack`,`shape`].indexOf(r)>=0&&r}function Os(e,t,n,r){return(e===`-`||e===`+`)&&(n=t+n),n===t||n<0||n>=r?!1:n}function ks(e,t){let n=null;return e===`start`?n=t.bottom:e===`end`?n=t.top:I(e)?n=t.getPixelForValue(e.value):t.getBasePixel&&(n=t.getBasePixel()),n}function As(e,t,n){let r;return r=e===`start`?n:e===`end`?t.options.reverse?t.min:t.max:I(e)?e.value:t.getBaseValue(),r}function js(e){let t=e.options,n=t.fill,r=z(n&&n.target,n);return r===void 0&&(r=!!t.backgroundColor),r===!1||r===null?!1:r===!0?`origin`:r}function Ms(e){let{scale:t,index:n,line:r}=e,i=[],a=r.segments,o=r.points,s=Ns(t,n);s.push(ws({x:null,y:t.bottom},r));for(let e=0;e<a.length;e++){let t=a[e];for(let e=t.start;e<=t.end;e++)Ps(i,o[e],s)}return new gs({points:i,options:{}})}function Ns(e,t){let n=[],r=e.getMatchingVisibleMetas(`line`);for(let e=0;e<r.length;e++){let i=r[e];if(i.index===t)break;i.hidden||n.unshift(i.dataset)}return n}function Ps(e,t,n){let r=[];for(let i=0;i<n.length;i++){let a=n[i],{first:o,last:s,point:c}=Fs(a,t,`x`);if(!(!c||o&&s)){if(o)r.unshift(c);else if(e.push(c),!s)break}}e.push(...r)}function Fs(e,t,n){let r=e.interpolate(t,n);if(!r)return{};let i=r[n],a=e.segments,o=e.points,s=!1,c=!1;for(let e=0;e<a.length;e++){let t=a[e],r=o[t.start][n],l=o[t.end][n];if(zt(i,r,l)){s=i===r,c=i===l;break}}return{first:s,last:c,point:r}}var Is=class{constructor(e){this.x=e.x,this.y=e.y,this.radius=e.radius}pathSegment(e,t,n){let{x:r,y:i,radius:a}=this;return t||={start:0,end:U},e.arc(r,i,a,t.end,t.start,!0),!n.bounds}interpolate(e){let{x:t,y:n,radius:r}=this,i=e.angle;return{x:t+Math.cos(i)*r,y:n+Math.sin(i)*r,angle:i}}};function Ls(e){let{chart:t,fill:n,line:r}=e;if(L(n))return Rs(t,n);if(n===`stack`)return Ms(e);if(n===`shape`)return!0;let i=zs(e);return i instanceof Is?i:ws(i,r)}function Rs(e,t){let n=e.getDatasetMeta(t),r=n&&e.isDatasetVisible(t);return r?n.dataset:null}function zs(e){let t=e.scale||{};return t.getPointPositionForValue?Vs(e):Bs(e)}function Bs(e){let{scale:t={},fill:n}=e,r=ks(n,t);if(L(r)){let e=t.isHorizontal();return{x:e?r:null,y:e?null:r}}return null}function Vs(e){let{scale:t,fill:n}=e,r=t.options,i=t.getLabels().length,a=r.reverse?t.max:t.min,o=As(n,t,a),s=[];if(r.grid.circular){let e=t.getPointPositionForValue(0,a);return new Is({x:e.x,y:e.y,radius:t.getDistanceFromCenterForValue(o)})}for(let e=0;e<i;++e)s.push(t.getPointPositionForValue(e,o));return s}function Hs(e,t,n){let r=Ls(t),{chart:i,index:a,line:o,scale:s,axis:c}=t,l=o.options,u=l.fill,d=l.backgroundColor,{above:f=d,below:p=d}=u||{},m=i.getDatasetMeta(a),h=li(i,m);r&&o.points.length&&(jn(e,n),Us(e,{line:o,target:r,above:f,below:p,area:n,scale:s,axis:c,clip:h}),Mn(e))}function Us(e,t){let{line:n,target:r,above:i,below:a,area:o,scale:s,clip:c}=t,l=n._loop?`angle`:t.axis;e.save();let u=a;a!==i&&(l===`x`?(Ws(e,r,o.top),Ks(e,{line:n,target:r,color:i,scale:s,property:l,clip:c}),e.restore(),e.save(),Ws(e,r,o.bottom)):l===`y`&&(Gs(e,r,o.left),Ks(e,{line:n,target:r,color:a,scale:s,property:l,clip:c}),e.restore(),e.save(),Gs(e,r,o.right),u=i)),Ks(e,{line:n,target:r,color:u,scale:s,property:l,clip:c}),e.restore()}function Ws(e,t,n){let{segments:r,points:i}=t,a=!0,o=!1;e.beginPath();for(let s of r){let{start:r,end:c}=s,l=i[r],u=i[Ss(r,c,i)];a?(e.moveTo(l.x,l.y),a=!1):(e.lineTo(l.x,n),e.lineTo(l.x,l.y)),o=!!t.pathSegment(e,s,{move:o}),o?e.closePath():e.lineTo(u.x,n)}e.lineTo(t.first().x,n),e.closePath(),e.clip()}function Gs(e,t,n){let{segments:r,points:i}=t,a=!0,o=!1;e.beginPath();for(let s of r){let{start:r,end:c}=s,l=i[r],u=i[Ss(r,c,i)];a?(e.moveTo(l.x,l.y),a=!1):(e.lineTo(n,l.y),e.lineTo(l.x,l.y)),o=!!t.pathSegment(e,s,{move:o}),o?e.closePath():e.lineTo(n,u.y)}e.lineTo(n,t.first().y),e.closePath(),e.clip()}function Ks(e,t){let{line:n,target:r,property:i,color:a,scale:o,clip:s}=t,c=ys(n,r,i);for(let{source:t,target:l,start:u,end:d}of c){let{style:{backgroundColor:c=a}={}}=t,f=r!==!0;e.save(),e.fillStyle=c,qs(e,o,s,f&&bs(i,u,d)),e.beginPath();let p=!!n.pathSegment(e,t),m;if(f){p?e.closePath():Js(e,r,d,i);let t=!!r.pathSegment(e,l,{move:p,reverse:!0});m=p&&t,m||Js(e,r,u,i)}e.closePath(),e.fill(m?`evenodd`:`nonzero`),e.restore()}}function qs(e,t,n,r){let i=t.chart.chartArea,{property:a,start:o,end:s}=r||{};if(a===`x`||a===`y`){let t,r,c,l;a===`x`?(t=o,r=i.top,c=s,l=i.bottom):(t=i.left,r=o,c=i.right,l=s),e.beginPath(),n&&(t=Math.max(t,n.left),c=Math.min(c,n.right),r=Math.max(r,n.top),l=Math.min(l,n.bottom)),e.rect(t,r,c-t,l-r),e.clip()}}function Js(e,t,n,r){let i=t.interpolate(n,r);i&&e.lineTo(i.x,i.y)}var Ys={id:`filler`,afterDatasetsUpdate(e,t,n){let r=(e.data.datasets||[]).length,i=[],a,o,s,c;for(o=0;o<r;++o)a=e.getDatasetMeta(o),s=a.dataset,c=null,s&&s.options&&s instanceof gs&&(c={visible:e.isDatasetVisible(o),index:o,fill:Ds(s,o,r),chart:e,axis:a.controller.options.indexAxis,scale:a.vScale,line:s}),a.$filler=c,i.push(c);for(o=0;o<r;++o)c=i[o],!(!c||c.fill===!1)&&(c.fill=Es(i,o,n.propagate))},beforeDraw(e,t,n){let r=n.drawTime===`beforeDraw`,i=e.getSortedVisibleDatasetMetas(),a=e.chartArea;for(let t=i.length-1;t>=0;--t){let n=i[t].$filler;n&&(n.line.updateControlPoints(a,n.axis),r&&n.fill&&Hs(e.ctx,n,a))}},beforeDatasetsDraw(e,t,n){if(n.drawTime!==`beforeDatasetsDraw`)return;let r=e.getSortedVisibleDatasetMetas();for(let t=r.length-1;t>=0;--t){let n=r[t].$filler;Ts(n)&&Hs(e.ctx,n,e.chartArea)}},beforeDatasetDraw(e,t,n){let r=t.meta.$filler;!Ts(r)||n.drawTime!==`beforeDatasetDraw`||Hs(e.ctx,r,e.chartArea)},defaults:{propagate:!0,drawTime:`beforeDatasetDraw`}};const Xs=(e,t)=>{let{boxHeight:n=t,boxWidth:r=t}=e;return e.usePointStyle&&(n=Math.min(n,t),r=e.pointStyleWidth||Math.min(r,t)),{boxWidth:r,boxHeight:n,itemHeight:Math.max(t,n)}},Zs=(e,t)=>e!==null&&t!==null&&e.datasetIndex===t.datasetIndex&&e.index===t.index;var Qs=class extends Fa{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t,n){this.maxWidth=e,this.maxHeight=t,this._margins=n,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let e=this.options.labels||{},t=B(e.generateLabels,[this.chart],this)||[];e.filter&&(t=t.filter(t=>e.filter(t,this.chart.data))),e.sort&&(t=t.sort((t,n)=>e.sort(t,n,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){let{options:e,ctx:t}=this;if(!e.display){this.width=this.height=0;return}let n=e.labels,r=X(n.font),i=r.size,a=this._computeTitleHeight(),{boxWidth:o,itemHeight:s}=Xs(n,i),c,l;t.font=r.string,this.isHorizontal()?(c=this.maxWidth,l=this._fitRows(a,i,o,s)+10):(l=this.maxHeight,c=this._fitCols(a,r,o,s)+10),this.width=Math.min(c,e.maxWidth||this.maxWidth),this.height=Math.min(l,e.maxHeight||this.maxHeight)}_fitRows(e,t,n,r){let{ctx:i,maxWidth:a,options:{labels:{padding:o}}}=this,s=this.legendHitBoxes=[],c=this.lineWidths=[0],l=r+o,u=e;i.textAlign=`left`,i.textBaseline=`middle`;let d=-1,f=-l;return this.legendItems.forEach((e,p)=>{let m=n+t/2+i.measureText(e.text).width;(p===0||c[c.length-1]+m+2*o>a)&&(u+=l,c[c.length-(p>0?0:1)]=0,f+=l,d++),s[p]={left:0,top:f,row:d,width:m,height:r},c[c.length-1]+=m+o}),u}_fitCols(e,t,n,r){let{ctx:i,maxHeight:a,options:{labels:{padding:o}}}=this,s=this.legendHitBoxes=[],c=this.columnSizes=[],l=a-e,u=o,d=0,f=0,p=0,m=0;return this.legendItems.forEach((e,a)=>{let{itemWidth:h,itemHeight:g}=$s(n,t,i,e,r);a>0&&f+g+2*o>l&&(u+=d+o,c.push({width:d,height:f}),p+=d+o,m++,d=f=0),s[a]={left:p,top:f,col:m,width:h,height:g},d=Math.max(d,h),f+=g+o}),u+=d,c.push({width:d,height:f}),u}adjustHitBoxes(){if(!this.options.display)return;let e=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:n,labels:{padding:r},rtl:i}}=this,a=Kr(i,this.left,this.width);if(this.isHorizontal()){let i=0,o=q(n,this.left+r,this.right-this.lineWidths[i]);for(let s of t)i!==s.row&&(i=s.row,o=q(n,this.left+r,this.right-this.lineWidths[i])),s.top+=this.top+e+r,s.left=a.leftForLtr(a.x(o),s.width),o+=s.width+r}else{let i=0,o=q(n,this.top+e+r,this.bottom-this.columnSizes[i].height);for(let s of t)s.col!==i&&(i=s.col,o=q(n,this.top+e+r,this.bottom-this.columnSizes[i].height)),s.top=o,s.left+=this.left+r,s.left=a.leftForLtr(a.x(s.left),s.width),o+=s.height+r}}isHorizontal(){return this.options.position===`top`||this.options.position===`bottom`}draw(){if(this.options.display){let e=this.ctx;jn(e,this),this._draw(),Mn(e)}}_draw(){let{options:e,columnSizes:t,lineWidths:n,ctx:r}=this,{align:i,labels:a}=e,o=J.color,s=Kr(e.rtl,this.left,this.width),c=X(a.font),{padding:l}=a,u=c.size,d=u/2,f;this.drawTitle(),r.textAlign=s.textAlign(`left`),r.textBaseline=`middle`,r.lineWidth=.5,r.font=c.string;let{boxWidth:p,boxHeight:m,itemHeight:h}=Xs(a,u),g=function(e,t,n){if(isNaN(p)||p<=0||isNaN(m)||m<0)return;r.save();let i=z(n.lineWidth,1);if(r.fillStyle=z(n.fillStyle,o),r.lineCap=z(n.lineCap,`butt`),r.lineDashOffset=z(n.lineDashOffset,0),r.lineJoin=z(n.lineJoin,`miter`),r.lineWidth=i,r.strokeStyle=z(n.strokeStyle,o),r.setLineDash(z(n.lineDash,[])),a.usePointStyle){let o={radius:m*Math.SQRT2/2,pointStyle:n.pointStyle,rotation:n.rotation,borderWidth:i},c=s.xPlus(e,p/2),l=t+d;kn(r,o,c,l,a.pointStyleWidth&&p)}else{let a=t+Math.max((u-m)/2,0),o=s.leftForLtr(e,p),c=Kn(n.borderRadius);r.beginPath(),Object.values(c).some(e=>e!==0)?zn(r,{x:o,y:a,w:p,h:m,radius:c}):r.rect(o,a,p,m),r.fill(),i!==0&&r.stroke()}r.restore()},_=function(e,t,n){Rn(r,n.text,e,t+h/2,c,{strikethrough:n.hidden,textAlign:s.textAlign(n.textAlign)})},v=this.isHorizontal(),y=this._computeTitleHeight();f=v?{x:q(i,this.left+l,this.right-n[0]),y:this.top+l+y,line:0}:{x:this.left+l,y:q(i,this.top+y+l,this.bottom-t[0].height),line:0},qr(this.ctx,e.textDirection);let b=h+l;this.legendItems.forEach((o,u)=>{r.strokeStyle=o.fontColor,r.fillStyle=o.fontColor;let m=r.measureText(o.text).width,h=s.textAlign(o.textAlign||=a.textAlign),x=p+d+m,S=f.x,C=f.y;s.setWidth(this.width),v?u>0&&S+x+l>this.right&&(C=f.y+=b,f.line++,S=f.x=q(i,this.left+l,this.right-n[f.line])):u>0&&C+b>this.bottom&&(S=f.x=S+t[f.line].width+l,f.line++,C=f.y=q(i,this.top+y+l,this.bottom-t[f.line].height));let w=s.x(S);if(g(w,C,o),S=Qt(h,S+p+d,v?S+x:this.right,e.rtl),_(s.x(S),C,o),v)f.x+=x+l;else if(typeof o.text!=`string`){let e=c.lineHeight;f.y+=nc(o,e)+l}else f.y+=b}),Jr(this.ctx,e.textDirection)}drawTitle(){let e=this.options,t=e.title,n=X(t.font),r=Y(t.padding);if(!t.display)return;let i=Kr(e.rtl,this.left,this.width),a=this.ctx,o=t.position,s=n.size/2,c=r.top+s,l,u=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),l=this.top+c,u=q(e.align,u,this.right-d);else{let t=this.columnSizes.reduce((e,t)=>Math.max(e,t.height),0);l=c+q(e.align,this.top,this.bottom-t-e.labels.padding-this._computeTitleHeight())}let f=q(o,u,u+d);a.textAlign=i.textAlign(Zt(o)),a.textBaseline=`middle`,a.strokeStyle=t.color,a.fillStyle=t.color,a.font=n.string,Rn(a,t.text,f,l,n)}_computeTitleHeight(){let e=this.options.title,t=X(e.font),n=Y(e.padding);return e.display?t.lineHeight+n.height:0}_getLegendItemAt(e,t){let n,r,i;if(zt(e,this.left,this.right)&&zt(t,this.top,this.bottom)){for(i=this.legendHitBoxes,n=0;n<i.length;++n)if(r=i[n],zt(e,r.left,r.left+r.width)&&zt(t,r.top,r.top+r.height))return this.legendItems[n]}return null}handleEvent(e){let t=this.options;if(!rc(e.type,t))return;let n=this._getLegendItemAt(e.x,e.y);if(e.type===`mousemove`||e.type===`mouseout`){let r=this._hoveredItem,i=Zs(r,n);r&&!i&&B(t.onLeave,[e,r,this],this),this._hoveredItem=n,n&&!i&&B(t.onHover,[e,n,this],this)}else n&&B(t.onClick,[e,n,this],this)}};function $s(e,t,n,r,i){let a=ec(r,e,t,n),o=tc(i,r,t.lineHeight);return{itemWidth:a,itemHeight:o}}function ec(e,t,n,r){let i=e.text;return i&&typeof i!=`string`&&(i=i.reduce((e,t)=>e.length>t.length?e:t)),t+n.size/2+r.measureText(i).width}function tc(e,t,n){let r=e;return typeof t.text!=`string`&&(r=nc(t,n)),r}function nc(e,t){let n=e.text?e.text.length:0;return t*n}function rc(e,t){return!!((e===`mousemove`||e===`mouseout`)&&(t.onHover||t.onLeave)||t.onClick&&(e===`click`||e===`mouseup`))}var ic={id:`legend`,_element:Qs,start(e,t,n){let r=e.legend=new Qs({ctx:e.ctx,options:n,chart:e});Z.configure(e,r,n),Z.addBox(e,r)},stop(e){Z.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,t,n){let r=e.legend;Z.configure(e,r,n),r.options=n},afterUpdate(e){let t=e.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(e,t){t.replay||e.legend.handleEvent(t.event)},defaults:{display:!0,position:`top`,align:`center`,fullSize:!0,reverse:!1,weight:1e3,onClick(e,t,n){let r=t.datasetIndex,i=n.chart;i.isDatasetVisible(r)?(i.hide(r),t.hidden=!0):(i.show(r),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){let t=e.data.datasets,{labels:{usePointStyle:n,pointStyle:r,textAlign:i,color:a,useBorderRadius:o,borderRadius:s}}=e.legend.options;return e._getSortedDatasetMetas().map(e=>{let c=e.controller.getStyle(n?0:void 0),l=Y(c.borderWidth);return{text:t[e.index].label,fillStyle:c.backgroundColor,fontColor:a,hidden:!e.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(l.width+l.height)/4,strokeStyle:c.borderColor,pointStyle:r||c.pointStyle,rotation:c.rotation,textAlign:i||c.textAlign,borderRadius:o&&(s||c.borderRadius),datasetIndex:e.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:`center`,text:``}},descriptors:{_scriptable:e=>!e.startsWith(`on`),labels:{_scriptable:e=>![`generateLabels`,`filter`,`sort`].includes(e)}}},ac=class extends Fa{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t){let n=this.options;if(this.left=0,this.top=0,!n.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=t;let r=F(n.text)?n.text.length:1;this._padding=Y(n.padding);let i=r*X(n.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=i:this.width=i}isHorizontal(){let e=this.options.position;return e===`top`||e===`bottom`}_drawArgs(e){let{top:t,left:n,bottom:r,right:i,options:a}=this,o=a.align,s=0,c,l,u;return this.isHorizontal()?(l=q(o,n,i),u=t+e,c=i-n):(a.position===`left`?(l=n+e,u=q(o,r,t),s=H*-.5):(l=i-e,u=q(o,t,r),s=H*.5),c=r-t),{titleX:l,titleY:u,maxWidth:c,rotation:s}}draw(){let e=this.ctx,t=this.options;if(!t.display)return;let n=X(t.font),r=n.lineHeight,i=r/2+this._padding.top,{titleX:a,titleY:o,maxWidth:s,rotation:c}=this._drawArgs(i);Rn(e,t.text,0,0,n,{color:t.color,maxWidth:s,rotation:c,textAlign:Zt(t.align),textBaseline:`middle`,translation:[a,o]})}};function oc(e,t){let n=new ac({ctx:e.ctx,options:t,chart:e});Z.configure(e,n,t),Z.addBox(e,n),e.titleBlock=n}var sc={id:`title`,_element:ac,start(e,t,n){oc(e,n)},stop(e){let t=e.titleBlock;Z.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){let r=e.titleBlock;Z.configure(e,r,n),r.options=n},defaults:{align:`center`,display:!1,font:{weight:`bold`},fullSize:!0,padding:10,position:`top`,text:``,weight:2e3},defaultRoutes:{color:`color`},descriptors:{_scriptable:!0,_indexable:!1}};const cc={average(e){if(!e.length)return!1;let t,n,r=new Set,i=0,a=0;for(t=0,n=e.length;t<n;++t){let n=e[t].element;if(n&&n.hasValue()){let e=n.tooltipPosition();r.add(e.x),i+=e.y,++a}}if(a===0||r.size===0)return!1;let o=[...r].reduce((e,t)=>e+t)/r.size;return{x:o,y:i/a}},nearest(e,t){if(!e.length)return!1;let n=t.x,r=t.y,i=1/0,a,o,s;for(a=0,o=e.length;a<o;++a){let n=e[a].element;if(n&&n.hasValue()){let e=n.getCenterPoint(),r=Ft(t,e);r<i&&(i=r,s=n)}}if(s){let e=s.tooltipPosition();n=e.x,r=e.y}return{x:n,y:r}}};function lc(e,t){return t&&(F(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function uc(e){return(typeof e==`string`||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function dc(e,t){let{element:n,datasetIndex:r,index:i}=t,a=e.getDatasetMeta(r).controller,{label:o,value:s}=a.getLabelAndValue(i);return{chart:e,label:o,parsed:a.getParsed(i),raw:e.data.datasets[r].data[i],formattedValue:s,dataset:a.getDataset(),dataIndex:i,datasetIndex:r,element:n}}function fc(e,t){let n=e.chart.ctx,{body:r,footer:i,title:a}=e,{boxWidth:o,boxHeight:s}=t,c=X(t.bodyFont),l=X(t.titleFont),u=X(t.footerFont),d=a.length,f=i.length,p=r.length,m=Y(t.padding),h=m.height,g=0,_=r.reduce((e,t)=>e+t.before.length+t.lines.length+t.after.length,0);if(_+=e.beforeBody.length+e.afterBody.length,d&&(h+=d*l.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),_){let e=t.displayColors?Math.max(s,c.lineHeight):c.lineHeight;h+=p*e+(_-p)*c.lineHeight+(_-1)*t.bodySpacing}f&&(h+=t.footerMarginTop+f*u.lineHeight+(f-1)*t.footerSpacing);let v=0,y=function(e){g=Math.max(g,n.measureText(e).width+v)};return n.save(),n.font=l.string,V(e.title,y),n.font=c.string,V(e.beforeBody.concat(e.afterBody),y),v=t.displayColors?o+2+t.boxPadding:0,V(r,e=>{V(e.before,y),V(e.lines,y),V(e.after,y)}),v=0,n.font=u.string,V(e.footer,y),n.restore(),g+=m.width,{width:g,height:h}}function pc(e,t){let{y:n,height:r}=t;return n<r/2?`top`:n>e.height-r/2?`bottom`:`center`}function mc(e,t,n,r){let{x:i,width:a}=r,o=n.caretSize+n.caretPadding;if(e===`left`&&i+a+o>t.width||e===`right`&&i-a-o<0)return!0}function hc(e,t,n,r){let{x:i,width:a}=n,{width:o,chartArea:{left:s,right:c}}=e,l=`center`;return r===`center`?l=i<=(s+c)/2?`left`:`right`:i<=a/2?l=`left`:i>=o-a/2&&(l=`right`),mc(l,e,t,n)&&(l=`center`),l}function gc(e,t,n){let r=n.yAlign||t.yAlign||pc(e,n);return{xAlign:n.xAlign||t.xAlign||hc(e,t,n,r),yAlign:r}}function _c(e,t){let{x:n,width:r}=e;return t===`right`?n-=r:t===`center`&&(n-=r/2),n}function vc(e,t,n){let{y:r,height:i}=e;return t===`top`?r+=n:t===`bottom`?r-=i+n:r-=i/2,r}function yc(e,t,n,r){let{caretSize:i,caretPadding:a,cornerRadius:o}=e,{xAlign:s,yAlign:c}=n,l=i+a,{topLeft:u,topRight:d,bottomLeft:f,bottomRight:p}=Kn(o),m=_c(t,s),h=vc(t,c,l);return c===`center`?s===`left`?m+=l:s===`right`&&(m-=l):s===`left`?m-=Math.max(u,f)+i:s===`right`&&(m+=Math.max(d,p)+i),{x:K(m,0,r.width-t.width),y:K(h,0,r.height-t.height)}}function bc(e,t,n){let r=Y(n.padding);return t===`center`?e.x+e.width/2:t===`right`?e.x+e.width-r.right:e.x+r.left}function xc(e){return lc([],uc(e))}function Sc(e,t,n){return Yn(e,{tooltip:t,tooltipItems:n,type:`tooltip`})}function Cc(e,t){let n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const wc={beforeTitle:Qe,title(e){if(e.length>0){let t=e[0],n=t.chart.data.labels,r=n?n.length:0;if(this&&this.options&&this.options.mode===`dataset`)return t.dataset.label||``;if(t.label)return t.label;if(r>0&&t.dataIndex<r)return n[t.dataIndex]}return``},afterTitle:Qe,beforeBody:Qe,beforeLabel:Qe,label(e){if(this&&this.options&&this.options.mode===`dataset`)return e.label+`: `+e.formattedValue||e.formattedValue;let t=e.dataset.label||``;t&&(t+=`: `);let n=e.formattedValue;return P(n)||(t+=n),t},labelColor(e){let t=e.chart.getDatasetMeta(e.datasetIndex),n=t.controller.getStyle(e.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){let t=e.chart.getDatasetMeta(e.datasetIndex),n=t.controller.getStyle(e.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Qe,afterBody:Qe,beforeFooter:Qe,footer:Qe,afterFooter:Qe};function Q(e,t,n,r){let i=e[t].call(n,r);return i===void 0?wc[t].call(n,r):i}var Tc=class extends Fa{static positioners=cc;constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let e=this._cachedAnimations;if(e)return e;let t=this.chart,n=this.options.setContext(this.getContext()),r=n.enabled&&t.options.animation&&n.animations,i=new hi(this.chart,r);return r._cacheable&&(this._cachedAnimations=Object.freeze(i)),i}getContext(){return this.$context||=Sc(this.chart.getContext(),this,this._tooltipItems)}getTitle(e,t){let{callbacks:n}=t,r=Q(n,`beforeTitle`,this,e),i=Q(n,`title`,this,e),a=Q(n,`afterTitle`,this,e),o=[];return o=lc(o,uc(r)),o=lc(o,uc(i)),o=lc(o,uc(a)),o}getBeforeBody(e,t){return xc(Q(t.callbacks,`beforeBody`,this,e))}getBody(e,t){let{callbacks:n}=t,r=[];return V(e,e=>{let t={before:[],lines:[],after:[]},i=Cc(n,e);lc(t.before,uc(Q(i,`beforeLabel`,this,e))),lc(t.lines,Q(i,`label`,this,e)),lc(t.after,uc(Q(i,`afterLabel`,this,e))),r.push(t)}),r}getAfterBody(e,t){return xc(Q(t.callbacks,`afterBody`,this,e))}getFooter(e,t){let{callbacks:n}=t,r=Q(n,`beforeFooter`,this,e),i=Q(n,`footer`,this,e),a=Q(n,`afterFooter`,this,e),o=[];return o=lc(o,uc(r)),o=lc(o,uc(i)),o=lc(o,uc(a)),o}_createItems(e){let t=this._active,n=this.chart.data,r=[],i=[],a=[],o=[],s,c;for(s=0,c=t.length;s<c;++s)o.push(dc(this.chart,t[s]));return e.filter&&(o=o.filter((t,r,i)=>e.filter(t,r,i,n))),e.itemSort&&(o=o.sort((t,r)=>e.itemSort(t,r,n))),V(o,t=>{let n=Cc(e.callbacks,t);r.push(Q(n,`labelColor`,this,t)),i.push(Q(n,`labelPointStyle`,this,t)),a.push(Q(n,`labelTextColor`,this,t))}),this.labelColors=r,this.labelPointStyles=i,this.labelTextColors=a,this.dataPoints=o,o}update(e,t){let n=this.options.setContext(this.getContext()),r=this._active,i,a=[];if(!r.length)this.opacity!==0&&(i={opacity:0});else{let e=cc[n.position].call(this,r,this._eventPosition);a=this._createItems(n),this.title=this.getTitle(a,n),this.beforeBody=this.getBeforeBody(a,n),this.body=this.getBody(a,n),this.afterBody=this.getAfterBody(a,n),this.footer=this.getFooter(a,n);let t=this._size=fc(this,n),o=Object.assign({},e,t),s=gc(this.chart,n,o),c=yc(n,o,s,this.chart);this.xAlign=s.xAlign,this.yAlign=s.yAlign,i={opacity:1,x:c.x,y:c.y,width:t.width,height:t.height,caretX:e.x,caretY:e.y}}this._tooltipItems=a,this.$context=void 0,i&&this._resolveAnimations().update(this,i),e&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:t})}drawCaret(e,t,n,r){let i=this.getCaretPosition(e,n,r);t.lineTo(i.x1,i.y1),t.lineTo(i.x2,i.y2),t.lineTo(i.x3,i.y3)}getCaretPosition(e,t,n){let{xAlign:r,yAlign:i}=this,{caretSize:a,cornerRadius:o}=n,{topLeft:s,topRight:c,bottomLeft:l,bottomRight:u}=Kn(o),{x:d,y:f}=e,{width:p,height:m}=t,h,g,_,v,y,b;return i===`center`?(y=f+m/2,r===`left`?(h=d,g=h-a,v=y+a,b=y-a):(h=d+p,g=h+a,v=y-a,b=y+a),_=h):(g=r===`left`?d+Math.max(s,l)+a:r===`right`?d+p-Math.max(c,u)-a:this.caretX,i===`top`?(v=f,y=v-a,h=g-a,_=g+a):(v=f+m,y=v+a,h=g+a,_=g-a),b=v),{x1:h,x2:g,x3:_,y1:v,y2:y,y3:b}}drawTitle(e,t,n){let r=this.title,i=r.length,a,o,s;if(i){let c=Kr(n.rtl,this.x,this.width);for(e.x=bc(this,n.titleAlign,n),t.textAlign=c.textAlign(n.titleAlign),t.textBaseline=`middle`,a=X(n.titleFont),o=n.titleSpacing,t.fillStyle=n.titleColor,t.font=a.string,s=0;s<i;++s)t.fillText(r[s],c.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+o,s+1===i&&(e.y+=n.titleMarginBottom-o)}}_drawColorBox(e,t,n,r,i){let a=this.labelColors[n],o=this.labelPointStyles[n],{boxHeight:s,boxWidth:c}=i,l=X(i.bodyFont),u=bc(this,`left`,i),d=r.x(u),f=s<l.lineHeight?(l.lineHeight-s)/2:0,p=t.y+f;if(i.usePointStyle){let t={radius:Math.min(c,s)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},n=r.leftForLtr(d,c)+c/2,l=p+s/2;e.strokeStyle=i.multiKeyBackground,e.fillStyle=i.multiKeyBackground,On(e,t,n,l),e.strokeStyle=a.borderColor,e.fillStyle=a.backgroundColor,On(e,t,n,l)}else{e.lineWidth=I(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,e.strokeStyle=a.borderColor,e.setLineDash(a.borderDash||[]),e.lineDashOffset=a.borderDashOffset||0;let t=r.leftForLtr(d,c),n=r.leftForLtr(r.xPlus(d,1),c-2),o=Kn(a.borderRadius);Object.values(o).some(e=>e!==0)?(e.beginPath(),e.fillStyle=i.multiKeyBackground,zn(e,{x:t,y:p,w:c,h:s,radius:o}),e.fill(),e.stroke(),e.fillStyle=a.backgroundColor,e.beginPath(),zn(e,{x:n,y:p+1,w:c-2,h:s-2,radius:o}),e.fill()):(e.fillStyle=i.multiKeyBackground,e.fillRect(t,p,c,s),e.strokeRect(t,p,c,s),e.fillStyle=a.backgroundColor,e.fillRect(n,p+1,c-2,s-2))}e.fillStyle=this.labelTextColors[n]}drawBody(e,t,n){let{body:r}=this,{bodySpacing:i,bodyAlign:a,displayColors:o,boxHeight:s,boxWidth:c,boxPadding:l}=n,u=X(n.bodyFont),d=u.lineHeight,f=0,p=Kr(n.rtl,this.x,this.width),m=function(n){t.fillText(n,p.x(e.x+f),e.y+d/2),e.y+=d+i},h=p.textAlign(a),g,_,v,y,b,x,S;for(t.textAlign=a,t.textBaseline=`middle`,t.font=u.string,e.x=bc(this,h,n),t.fillStyle=n.bodyColor,V(this.beforeBody,m),f=o&&h!==`right`?a===`center`?c/2+l:c+2+l:0,y=0,x=r.length;y<x;++y){for(g=r[y],_=this.labelTextColors[y],t.fillStyle=_,V(g.before,m),v=g.lines,o&&v.length&&(this._drawColorBox(t,e,y,p,n),d=Math.max(u.lineHeight,s)),b=0,S=v.length;b<S;++b)m(v[b]),d=u.lineHeight;V(g.after,m)}f=0,d=u.lineHeight,V(this.afterBody,m),e.y-=i}drawFooter(e,t,n){let r=this.footer,i=r.length,a,o;if(i){let s=Kr(n.rtl,this.x,this.width);for(e.x=bc(this,n.footerAlign,n),e.y+=n.footerMarginTop,t.textAlign=s.textAlign(n.footerAlign),t.textBaseline=`middle`,a=X(n.footerFont),t.fillStyle=n.footerColor,t.font=a.string,o=0;o<i;++o)t.fillText(r[o],s.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+n.footerSpacing}}drawBackground(e,t,n,r){let{xAlign:i,yAlign:a}=this,{x:o,y:s}=e,{width:c,height:l}=n,{topLeft:u,topRight:d,bottomLeft:f,bottomRight:p}=Kn(r.cornerRadius);t.fillStyle=r.backgroundColor,t.strokeStyle=r.borderColor,t.lineWidth=r.borderWidth,t.beginPath(),t.moveTo(o+u,s),a===`top`&&this.drawCaret(e,t,n,r),t.lineTo(o+c-d,s),t.quadraticCurveTo(o+c,s,o+c,s+d),a===`center`&&i===`right`&&this.drawCaret(e,t,n,r),t.lineTo(o+c,s+l-p),t.quadraticCurveTo(o+c,s+l,o+c-p,s+l),a===`bottom`&&this.drawCaret(e,t,n,r),t.lineTo(o+f,s+l),t.quadraticCurveTo(o,s+l,o,s+l-f),a===`center`&&i===`left`&&this.drawCaret(e,t,n,r),t.lineTo(o,s+u),t.quadraticCurveTo(o,s,o+u,s),t.closePath(),t.fill(),r.borderWidth>0&&t.stroke()}_updateAnimationTarget(e){let t=this.chart,n=this.$animations,r=n&&n.x,i=n&&n.y;if(r||i){let n=cc[e.position].call(this,this._active,this._eventPosition);if(!n)return;let a=this._size=fc(this,e),o=Object.assign({},n,this._size),s=gc(t,e,o),c=yc(e,o,s,t);(r._to!==c.x||i._to!==c.y)&&(this.xAlign=s.xAlign,this.yAlign=s.yAlign,this.width=a.width,this.height=a.height,this.caretX=n.x,this.caretY=n.y,this._resolveAnimations().update(this,c))}}_willRender(){return!!this.opacity}draw(e){let t=this.options.setContext(this.getContext()),n=this.opacity;if(!n)return;this._updateAnimationTarget(t);let r={width:this.width,height:this.height},i={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;let a=Y(t.padding),o=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;t.enabled&&o&&(e.save(),e.globalAlpha=n,this.drawBackground(i,e,r,t),qr(e,t.textDirection),i.y+=a.top,this.drawTitle(i,e,t),this.drawBody(i,e,t),this.drawFooter(i,e,t),Jr(e,t.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,t){let n=this._active,r=e.map(({datasetIndex:e,index:t})=>{let n=this.chart.getDatasetMeta(e);if(!n)throw Error(`Cannot find a dataset at index `+e);return{datasetIndex:e,element:n.data[t],index:t}}),i=!tt(n,r),a=this._positionChanged(r,t);(i||a)&&(this._active=r,this._eventPosition=t,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,t,n=!0){if(t&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let r=this.options,i=this._active||[],a=this._getActiveElements(e,i,t,n),o=this._positionChanged(a,e),s=t||!tt(a,i)||o;return s&&(this._active=a,(r.enabled||r.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,t))),s}_getActiveElements(e,t,n,r){let i=this.options;if(e.type===`mouseout`)return[];if(!r)return t.filter(e=>this.chart.data.datasets[e.datasetIndex]&&this.chart.getDatasetMeta(e.datasetIndex).controller.getParsed(e.index)!==void 0);let a=this.chart.getElementsAtEventForMode(e,i.mode,i,n);return i.reverse&&a.reverse(),a}_positionChanged(e,t){let{caretX:n,caretY:r,options:i}=this,a=cc[i.position].call(this,e,t);return a!==!1&&(n!==a.x||r!==a.y)}},Ec={id:`tooltip`,_element:Tc,positioners:cc,afterInit(e,t,n){n&&(e.tooltip=new Tc({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){let t=e.tooltip;if(t&&t._willRender()){let n={tooltip:t};if(e.notifyPlugins(`beforeTooltipDraw`,{...n,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins(`afterTooltipDraw`,n)}},afterEvent(e,t){if(e.tooltip){let n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:`average`,backgroundColor:`rgba(0,0,0,0.8)`,titleColor:`#fff`,titleFont:{weight:`bold`},titleSpacing:2,titleMarginBottom:6,titleAlign:`left`,bodyColor:`#fff`,bodySpacing:2,bodyFont:{},bodyAlign:`left`,footerColor:`#fff`,footerSpacing:2,footerMarginTop:6,footerFont:{weight:`bold`},footerAlign:`left`,padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:`#fff`,displayColors:!0,boxPadding:0,borderColor:`rgba(0,0,0,0)`,borderWidth:0,animation:{duration:400,easing:`easeOutQuart`},animations:{numbers:{type:`number`,properties:[`x`,`y`,`width`,`height`,`caretX`,`caretY`]},opacity:{easing:`linear`,duration:200}},callbacks:wc},defaultRoutes:{bodyFont:`font`,footerFont:`font`,titleFont:`font`},descriptors:{_scriptable:e=>e!==`filter`&&e!==`itemSort`&&e!==`external`,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:`animation`}},additionalOptionScopes:[`interaction`]};const Dc=(e,t,n,r)=>(typeof t==`string`?(n=e.push(t)-1,r.unshift({index:n,label:t})):isNaN(t)&&(n=null),n);function Oc(e,t,n,r){let i=e.indexOf(t);if(i===-1)return Dc(e,t,n,r);let a=e.lastIndexOf(t);return i===a?i:n}const kc=(e,t)=>e===null?null:K(Math.round(e),0,t);function Ac(e){let t=this.getLabels();return e>=0&&e<t.length?t[e]:e}var jc=class extends to{static id=`category`;static defaults={ticks:{callback:Ac}};constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){let t=this._addedLabels;if(t.length){let e=this.getLabels();for(let{index:n,label:r}of t)e[n]===r&&e.splice(n,1);this._addedLabels=[]}super.init(e)}parse(e,t){if(P(e))return null;let n=this.getLabels();return t=isFinite(t)&&n[t]===e?t:Oc(n,e,z(t,e),this._addedLabels),kc(t,n.length-1)}determineDataLimits(){let{minDefined:e,maxDefined:t}=this.getUserBounds(),{min:n,max:r}=this.getMinMax(!0);this.options.bounds===`ticks`&&(e||(n=0),t||(r=this.getLabels().length-1)),this.min=n,this.max=r}buildTicks(){let e=this.min,t=this.max,n=this.options.offset,r=[],i=this.getLabels();i=e===0&&t===i.length-1?i:i.slice(e,t+1),this._valueRange=Math.max(i.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let n=e;n<=t;n++)r.push({value:n});return r}getLabelForValue(e){return Ac.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!=`number`&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){let t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}};function Mc(e,t){let n=[],r=1e-14,{bounds:i,step:a,min:o,max:s,precision:c,count:l,maxTicks:u,maxDigits:d,includeBounds:f}=e,p=a||1,m=u-1,{min:h,max:g}=t,_=!P(o),v=!P(s),y=!P(l),b=(g-h)/(d+1),x=Tt((g-h)/m/p)*p,S,C,w,T;if(x<r&&!_&&!v)return[{value:h},{value:g}];T=Math.ceil(g/x)-Math.floor(h/x),T>m&&(x=Tt(T*x/m/p)*p),P(c)||(S=10**c,x=Math.ceil(x*S)/S),i===`ticks`?(C=Math.floor(h/x)*x,w=Math.ceil(g/x)*x):(C=h,w=g),_&&v&&a&&kt((s-o)/a,x/1e3)?(T=Math.round(Math.min((s-o)/x,u)),x=(s-o)/T,C=o,w=s):y?(C=_?o:C,w=v?s:w,T=l-1,x=(w-C)/T):(T=(w-C)/x,T=wt(T,Math.round(T),x/1e3)?Math.round(T):Math.ceil(T));let E=Math.max(Nt(x),Nt(C));S=10**(P(c)?E:c),C=Math.round(C*S)/S,w=Math.round(w*S)/S;let D=0;for(_&&(f&&C!==o?(n.push({value:o}),C<o&&D++,wt(Math.round((C+D*x)*S)/S,o,Nc(o,b,e))&&D++):C<o&&D++);D<T;++D){let e=Math.round((C+D*x)*S)/S;if(v&&e>s)break;n.push({value:e})}return v&&f&&w!==s?n.length&&wt(n[n.length-1].value,s,Nc(s,b,e))?n[n.length-1].value=s:n.push({value:s}):(!v||w===s)&&n.push({value:w}),n}function Nc(e,t,{horizontal:n,minRotation:r}){let i=jt(r),a=(n?Math.sin(i):Math.cos(i))||.001,o=.75*t*(``+e).length;return Math.min(t/a,o)}var Pc=class extends to{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,t){return P(e)||(typeof e==`number`||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){let{beginAtZero:e}=this.options,{minDefined:t,maxDefined:n}=this.getUserBounds(),{min:r,max:i}=this,a=e=>r=t?r:e,o=e=>i=n?i:e;if(e){let e=Ct(r),t=Ct(i);e<0&&t<0?o(0):e>0&&t>0&&a(0)}if(r===i){let t=i===0?1:Math.abs(i*.05);o(i+t),e||a(r-t)}this.min=r,this.max=i}getTickLimit(){let e=this.options.ticks,{maxTicksLimit:t,stepSize:n}=e,r;return n?(r=Math.ceil(this.max/n)-Math.floor(this.min/n)+1,r>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${n} would result generating up to ${r} ticks. Limiting to 1000.`),r=1e3)):(r=this.computeTickLimit(),t||=11),t&&(r=Math.min(t,r)),r}computeTickLimit(){return 1/0}buildTicks(){let e=this.options,t=e.ticks,n=this.getTickLimit();n=Math.max(2,n);let r={maxTicks:n,bounds:e.bounds,min:e.min,max:e.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:t.includeBounds!==!1},i=this._range||this,a=Mc(r,i);return e.bounds===`ticks`&&At(a,this,`value`),e.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){let e=this.ticks,t=this.min,n=this.max;if(super.configure(),this.options.offset&&e.length){let r=(n-t)/Math.max(e.length-1,1)/2;t-=r,n+=r}this._startValue=t,this._endValue=n,this._valueRange=n-t}getLabelForValue(e){return pn(e,this.chart.options.locale,this.options.ticks.format)}},Fc=class extends Pc{static id=`linear`;static defaults={ticks:{callback:gn.formatters.numeric}};determineDataLimits(){let{min:e,max:t}=this.getMinMax(!0);this.min=L(e)?e:0,this.max=L(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){let e=this.isHorizontal(),t=e?this.width:this.height,n=jt(this.options.ticks.minRotation),r=(e?Math.sin(n):Math.cos(n))||.001,i=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,i.lineHeight/r))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}};const Ic=e=>Math.floor(St(e)),Lc=(e,t)=>10**(Ic(e)+t);function Rc(e){let t=e/10**Ic(e);return t===1}function zc(e,t,n){let r=10**n,i=Math.floor(e/r),a=Math.ceil(t/r);return a-i}function Bc(e,t){let n=t-e,r=Ic(n);for(;zc(e,t,r)>10;)r++;for(;zc(e,t,r)<10;)r--;return Math.min(r,Ic(e))}function Vc(e,{min:t,max:n}){t=R(e.min,t);let r=[],i=Ic(t),a=Bc(t,n),o=a<0?10**Math.abs(a):1,s=10**a,c=i>a?10**i:0,l=Math.round((t-c)*o)/o,u=Math.floor((t-c)/s/10)*s*10,d=Math.floor((l-u)/10**a),f=R(e.min,Math.round((c+u+d*10**a)*o)/o);for(;f<n;)r.push({value:f,major:Rc(f),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(a++,d=2,o=a>=0?1:o),f=Math.round((c+u+d*10**a)*o)/o;let p=R(e.max,f);return r.push({value:p,major:Rc(p),significand:d}),r}(class extends to{static id=`logarithmic`;static defaults={ticks:{callback:gn.formatters.logarithmic,major:{enabled:!0}}};constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(e,t){let n=Pc.prototype.parse.apply(this,[e,t]);if(n===0){this._zero=!0;return}return L(n)&&n>0?n:null}determineDataLimits(){let{min:e,max:t}=this.getMinMax(!0);this.min=L(e)?Math.max(0,e):null,this.max=L(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!L(this._userMin)&&(this.min=e===Lc(this.min,0)?Lc(this.min,-1):Lc(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:e,maxDefined:t}=this.getUserBounds(),n=this.min,r=this.max,i=t=>n=e?n:t,a=e=>r=t?r:e;n===r&&(n<=0?(i(1),a(10)):(i(Lc(n,-1)),a(Lc(r,1)))),n<=0&&i(Lc(r,-1)),r<=0&&a(Lc(n,1)),this.min=n,this.max=r}buildTicks(){let e=this.options,t={min:this._userMin,max:this._userMax},n=Vc(t,this);return e.bounds===`ticks`&&At(n,this,`value`),e.reverse?(n.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),n}getLabelForValue(e){return e===void 0?`0`:pn(e,this.chart.options.locale,this.options.ticks.format)}configure(){let e=this.min;super.configure(),this._startValue=St(e),this._valueRange=St(this.max)-St(e)}getPixelForValue(e){return(e===void 0||e===0)&&(e=this.min),e===null||isNaN(e)?NaN:this.getPixelForDecimal(e===this.min?0:(St(e)-this._startValue)/this._valueRange)}getValueForPixel(e){let t=this.getDecimalForPixel(e);return 10**(this._startValue+t*this._valueRange)}});function Hc(e){let t=e.ticks;if(t.display&&e.display){let e=Y(t.backdropPadding);return z(t.font&&t.font.size,J.font.size)+e.height}return 0}function Uc(e,t,n){return n=F(n)?n:[n],{w:Tn(e,t.string,n),h:n.length*t.lineHeight}}function Wc(e,t,n,r,i){return e===r||e===i?{start:t-n/2,end:t+n/2}:e<r||e>i?{start:t-n,end:t}:{start:t,end:t+n}}function Gc(e){let t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),r=[],i=[],a=e._pointLabels.length,o=e.options.pointLabels,s=o.centerPointLabels?H/a:0;for(let c=0;c<a;c++){let a=o.setContext(e.getPointLabelContext(c));i[c]=a.padding;let l=e.getPointPosition(c,e.drawingArea+i[c],s),u=X(a.font),d=Uc(e.ctx,u,e._pointLabels[c]);r[c]=d;let f=G(e.getIndexAngle(c)+s),p=Math.round(Mt(f)),m=Wc(p,l.x,d.w,0,180),h=Wc(p,l.y,d.h,90,270);Kc(n,t,f,m,h)}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=Yc(e,r,i)}function Kc(e,t,n,r,i){let a=Math.abs(Math.sin(n)),o=Math.abs(Math.cos(n)),s=0,c=0;r.start<t.l?(s=(t.l-r.start)/a,e.l=Math.min(e.l,t.l-s)):r.end>t.r&&(s=(r.end-t.r)/a,e.r=Math.max(e.r,t.r+s)),i.start<t.t?(c=(t.t-i.start)/o,e.t=Math.min(e.t,t.t-c)):i.end>t.b&&(c=(i.end-t.b)/o,e.b=Math.max(e.b,t.b+c))}function qc(e,t,n){let r=e.drawingArea,{extra:i,additionalAngle:a,padding:o,size:s}=n,c=e.getPointPosition(t,r+i+o,a),l=Math.round(Mt(G(c.angle+W))),u=Qc(c.y,s.h,l),d=Xc(l),f=Zc(c.x,s.w,d);return{visible:!0,x:c.x,y:u,textAlign:d,left:f,top:u,right:f+s.w,bottom:u+s.h}}function Jc(e,t){if(!t)return!0;let{left:n,top:r,right:i,bottom:a}=e,o=An({x:n,y:r},t)||An({x:n,y:a},t)||An({x:i,y:r},t)||An({x:i,y:a},t);return!o}function Yc(e,t,n){let r=[],i=e._pointLabels.length,a=e.options,{centerPointLabels:o,display:s}=a.pointLabels,c={extra:Hc(a)/2,additionalAngle:o?H/i:0},l;for(let a=0;a<i;a++){c.padding=n[a],c.size=t[a];let i=qc(e,a,c);r.push(i),s===`auto`&&(i.visible=Jc(i,l),i.visible&&(l=i))}return r}function Xc(e){return e===0||e===180?`center`:e<180?`left`:`right`}function Zc(e,t,n){return n===`right`?e-=t:n===`center`&&(e-=t/2),e}function Qc(e,t,n){return n===90||n===270?e-=t/2:(n>270||n<90)&&(e-=t),e}function $c(e,t,n){let{left:r,top:i,right:a,bottom:o}=n,{backdropColor:s}=t;if(!P(s)){let n=Kn(t.borderRadius),c=Y(t.backdropPadding);e.fillStyle=s;let l=r-c.left,u=i-c.top,d=a-r+c.width,f=o-i+c.height;Object.values(n).some(e=>e!==0)?(e.beginPath(),zn(e,{x:l,y:u,w:d,h:f,radius:n}),e.fill()):e.fillRect(l,u,d,f)}}function el(e,t){let{ctx:n,options:{pointLabels:r}}=e;for(let i=t-1;i>=0;i--){let t=e._pointLabelItems[i];if(!t.visible)continue;let a=r.setContext(e.getPointLabelContext(i));$c(n,a,t);let o=X(a.font),{x:s,y:c,textAlign:l}=t;Rn(n,e._pointLabels[i],s,c+o.lineHeight/2,o,{color:a.color,textAlign:l,textBaseline:`middle`})}}function tl(e,t,n,r){let{ctx:i}=e;if(n)i.arc(e.xCenter,e.yCenter,t,0,U);else{let n=e.getPointPosition(0,t);i.moveTo(n.x,n.y);for(let a=1;a<r;a++)n=e.getPointPosition(a,t),i.lineTo(n.x,n.y)}}function nl(e,t,n,r,i){let a=e.ctx,o=t.circular,{color:s,lineWidth:c}=t;!o&&!r||!s||!c||n<0||(a.save(),a.strokeStyle=s,a.lineWidth=c,a.setLineDash(i.dash||[]),a.lineDashOffset=i.dashOffset,a.beginPath(),tl(e,n,o,r),a.closePath(),a.stroke(),a.restore())}function rl(e,t,n){return Yn(e,{label:n,index:t,type:`pointLabel`})}(class extends Pc{static id=`radialLinear`;static defaults={display:!0,animate:!0,position:`chartArea`,angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:gn.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(e){return e},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":`borderColor`,"pointLabels.color":`color`,"ticks.color":`color`};static descriptors={angleLines:{_fallback:`grid`}};constructor(e){super(e),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let e=this._padding=Y(Hc(this.options)/2),t=this.width=this.maxWidth-e.width,n=this.height=this.maxHeight-e.height;this.xCenter=Math.floor(this.left+t/2+e.left),this.yCenter=Math.floor(this.top+n/2+e.top),this.drawingArea=Math.floor(Math.min(t,n)/2)}determineDataLimits(){let{min:e,max:t}=this.getMinMax(!1);this.min=L(e)&&!isNaN(e)?e:0,this.max=L(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Hc(this.options))}generateTickLabels(e){Pc.prototype.generateTickLabels.call(this,e),this._pointLabels=this.getLabels().map((e,t)=>{let n=B(this.options.pointLabels.callback,[e,t],this);return n||n===0?n:``}).filter((e,t)=>this.chart.getDataVisibility(t))}fit(){let e=this.options;e.display&&e.pointLabels.display?Gc(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(e,t,n,r){this.xCenter+=Math.floor((e-t)/2),this.yCenter+=Math.floor((n-r)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(e,t,n,r))}getIndexAngle(e){let t=U/(this._pointLabels.length||1),n=this.options.startAngle||0;return G(e*t+jt(n))}getDistanceFromCenterForValue(e){if(P(e))return NaN;let t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-e)*t:(e-this.min)*t}getValueForDistanceFromCenter(e){if(P(e))return NaN;let t=e/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(e){let t=this._pointLabels||[];if(e>=0&&e<t.length){let n=t[e];return rl(this.getContext(),e,n)}}getPointPosition(e,t,n=0){let r=this.getIndexAngle(e)-W+n;return{x:Math.cos(r)*t+this.xCenter,y:Math.sin(r)*t+this.yCenter,angle:r}}getPointPositionForValue(e,t){return this.getPointPosition(e,this.getDistanceFromCenterForValue(t))}getBasePosition(e){return this.getPointPositionForValue(e||0,this.getBaseValue())}getPointLabelPosition(e){let{left:t,top:n,right:r,bottom:i}=this._pointLabelItems[e];return{left:t,top:n,right:r,bottom:i}}drawBackground(){let{backgroundColor:e,grid:{circular:t}}=this.options;if(e){let n=this.ctx;n.save(),n.beginPath(),tl(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),n.closePath(),n.fillStyle=e,n.fill(),n.restore()}}drawGrid(){let e=this.ctx,t=this.options,{angleLines:n,grid:r,border:i}=t,a=this._pointLabels.length,o,s,c;if(t.pointLabels.display&&el(this,a),r.display&&this.ticks.forEach((e,t)=>{if(t!==0||t===0&&this.min<0){s=this.getDistanceFromCenterForValue(e.value);let n=this.getContext(t),o=r.setContext(n),c=i.setContext(n);nl(this,o,s,a,c)}}),n.display){for(e.save(),o=a-1;o>=0;o--){let r=n.setContext(this.getPointLabelContext(o)),{color:i,lineWidth:a}=r;!a||!i||(e.lineWidth=a,e.strokeStyle=i,e.setLineDash(r.borderDash),e.lineDashOffset=r.borderDashOffset,s=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),c=this.getPointPosition(o,s),e.beginPath(),e.moveTo(this.xCenter,this.yCenter),e.lineTo(c.x,c.y),e.stroke())}e.restore()}}drawBorder(){}drawLabels(){let e=this.ctx,t=this.options,n=t.ticks;if(!n.display)return;let r=this.getIndexAngle(0),i,a;e.save(),e.translate(this.xCenter,this.yCenter),e.rotate(r),e.textAlign=`center`,e.textBaseline=`middle`,this.ticks.forEach((r,o)=>{if(o===0&&this.min>=0&&!t.reverse)return;let s=n.setContext(this.getContext(o)),c=X(s.font);if(i=this.getDistanceFromCenterForValue(this.ticks[o].value),s.showLabelBackdrop){e.font=c.string,a=e.measureText(r.label).width,e.fillStyle=s.backdropColor;let t=Y(s.backdropPadding);e.fillRect(-a/2-t.left,-i-c.size/2-t.top,a+t.width,c.size+t.height)}Rn(e,r.label,0,-i,c,{color:s.color,strokeColor:s.textStrokeColor,strokeWidth:s.textStrokeWidth})}),e.restore()}drawTitle(){}});const il={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},$=Object.keys(il);function al(e,t){return e-t}function ol(e,t){if(P(t))return null;let n=e._adapter,{parser:r,round:i,isoWeekday:a}=e._parseOpts,o=t;return typeof r==`function`&&(o=r(o)),L(o)||(o=typeof r==`string`?n.parse(o,r):n.parse(o)),o===null?null:(i&&(o=i===`week`&&(Ot(a)||a===!0)?n.startOf(o,`isoWeek`,a):n.startOf(o,i)),+o)}function sl(e,t,n,r){let i=$.length;for(let a=$.indexOf(e);a<i-1;++a){let e=il[$[a]],i=e.steps?e.steps:2**53-1;if(e.common&&Math.ceil((n-t)/(i*e.size))<=r)return $[a]}return $[i-1]}function cl(e,t,n,r,i){for(let a=$.length-1;a>=$.indexOf(n);a--){let n=$[a];if(il[n].common&&e._adapter.diff(i,r,n)>=t-1)return n}return $[n?$.indexOf(n):0]}function ll(e){for(let t=$.indexOf(e)+1,n=$.length;t<n;++t)if(il[$[t]].common)return $[t]}function ul(e,t,n){if(!n)e[t]=!0;else if(n.length){let{lo:r,hi:i}=Bt(n,t),a=n[r]>=t?n[r]:n[i];e[a]=!0}}function dl(e,t,n,r){let i=e._adapter,a=+i.startOf(t[0].value,r),o=t[t.length-1].value,s,c;for(s=a;s<=o;s=+i.add(s,1,r))c=n[s],c>=0&&(t[c].major=!0);return t}function fl(e,t,n){let r=[],i={},a=t.length,o,s;for(o=0;o<a;++o)s=t[o],i[s]=o,r.push({value:s,major:!1});return a===0||!n?r:dl(e,r,i,n)}var pl=class extends to{static id=`time`;static defaults={bounds:`data`,adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:`millisecond`,displayFormats:{}},ticks:{source:`auto`,callback:!1,major:{enabled:!1}}};constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit=`day`,this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,t={}){let n=e.time||={},r=this._adapter=new Bi._date(e.adapters.date);r.init(t),ot(n.displayFormats,r.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(e),this._normalized=t.normalized}parse(e,t){return e===void 0?null:ol(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let e=this.options,t=this._adapter,n=e.time.unit||`day`,{min:r,max:i,minDefined:a,maxDefined:o}=this.getUserBounds();function s(e){!a&&!isNaN(e.min)&&(r=Math.min(r,e.min)),!o&&!isNaN(e.max)&&(i=Math.max(i,e.max))}(!a||!o)&&(s(this._getLabelBounds()),(e.bounds!==`ticks`||e.ticks.source!==`labels`)&&s(this.getMinMax(!1))),r=L(r)&&!isNaN(r)?r:+t.startOf(Date.now(),n),i=L(i)&&!isNaN(i)?i:+t.endOf(Date.now(),n)+1,this.min=Math.min(r,i-1),this.max=Math.max(r+1,i)}_getLabelBounds(){let e=this.getLabelTimestamps(),t=1/0,n=-1/0;return e.length&&(t=e[0],n=e[e.length-1]),{min:t,max:n}}buildTicks(){let e=this.options,t=e.time,n=e.ticks,r=n.source===`labels`?this.getLabelTimestamps():this._generate();e.bounds===`ticks`&&r.length&&(this.min=this._userMin||r[0],this.max=this._userMax||r[r.length-1]);let i=this.min,a=this.max,o=Ut(r,i,a);return this._unit=t.unit||(n.autoSkip?sl(t.minUnit,this.min,this.max,this._getLabelCapacity(i)):cl(this,o.length,t.minUnit,this.min,this.max)),this._majorUnit=!n.major.enabled||this._unit===`year`?void 0:ll(this._unit),this.initOffsets(r),e.reverse&&o.reverse(),fl(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let t=0,n=0,r,i;this.options.offset&&e.length&&(r=this.getDecimalForValue(e[0]),t=e.length===1?1-r:(this.getDecimalForValue(e[1])-r)/2,i=this.getDecimalForValue(e[e.length-1]),n=e.length===1?i:(i-this.getDecimalForValue(e[e.length-2]))/2);let a=e.length<3?.5:.25;t=K(t,0,a),n=K(n,0,a),this._offsets={start:t,end:n,factor:1/(t+1+n)}}_generate(){let e=this._adapter,t=this.min,n=this.max,r=this.options,i=r.time,a=i.unit||sl(i.minUnit,t,n,this._getLabelCapacity(t)),o=z(r.ticks.stepSize,1),s=a===`week`?i.isoWeekday:!1,c=Ot(s)||s===!0,l={},u=t,d,f;if(c&&(u=+e.startOf(u,`isoWeek`,s)),u=+e.startOf(u,c?`day`:a),e.diff(n,t,a)>1e5*o)throw Error(t+` and `+n+` are too far apart with stepSize of `+o+` `+a);let p=r.ticks.source===`data`&&this.getDataTimestamps();for(d=u,f=0;d<n;d=+e.add(d,o,a),f++)ul(l,d,p);return(d===n||r.bounds===`ticks`||f===1)&&ul(l,d,p),Object.keys(l).sort(al).map(e=>+e)}getLabelForValue(e){let t=this._adapter,n=this.options.time;return n.tooltipFormat?t.format(e,n.tooltipFormat):t.format(e,n.displayFormats.datetime)}format(e,t){let n=this.options,r=n.time.displayFormats,i=this._unit,a=t||r[i];return this._adapter.format(e,a)}_tickFormatFunction(e,t,n,r){let i=this.options,a=i.ticks.callback;if(a)return B(a,[e,t,n],this);let o=i.time.displayFormats,s=this._unit,c=this._majorUnit,l=s&&o[s],u=c&&o[c],d=n[t],f=c&&u&&d&&d.major;return this._adapter.format(e,r||(f?u:l))}generateTickLabels(e){let t,n,r;for(t=0,n=e.length;t<n;++t)r=e[t],r.label=this._tickFormatFunction(r.value,t,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){let t=this._offsets,n=this.getDecimalForValue(e);return this.getPixelForDecimal((t.start+n)*t.factor)}getValueForPixel(e){let t=this._offsets,n=this.getDecimalForPixel(e)/t.factor-t.end;return this.min+n*(this.max-this.min)}_getLabelSize(e){let t=this.options.ticks,n=this.ctx.measureText(e).width,r=jt(this.isHorizontal()?t.maxRotation:t.minRotation),i=Math.cos(r),a=Math.sin(r),o=this._resolveTickFontOptions(0).size;return{w:n*i+o*a,h:n*a+o*i}}_getLabelCapacity(e){let t=this.options.time,n=t.displayFormats,r=n[t.unit]||n.millisecond,i=this._tickFormatFunction(e,0,fl(this,[e],this._majorUnit),r),a=this._getLabelSize(i),o=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return o>0?o:1}getDataTimestamps(){let e=this._cache.data||[],t,n;if(e.length)return e;let r=this.getMatchingVisibleMetas();if(this._normalized&&r.length)return this._cache.data=r[0].controller.getAllParsedValues(this);for(t=0,n=r.length;t<n;++t)e=e.concat(r[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){let e=this._cache.labels||[],t,n;if(e.length)return e;let r=this.getLabels();for(t=0,n=r.length;t<n;++t)e.push(ol(this,r[t]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return qt(e.sort(al))}};function ml(e,t,n){let r=0,i=e.length-1,a,o,s,c;n?(t>=e[r].pos&&t<=e[i].pos&&({lo:r,hi:i}=Vt(e,`pos`,t)),{pos:a,time:s}=e[r],{pos:o,time:c}=e[i]):(t>=e[r].time&&t<=e[i].time&&({lo:r,hi:i}=Vt(e,`time`,t)),{time:a,pos:s}=e[r],{time:o,pos:c}=e[i]);let l=o-a;return l?s+(c-s)*(t-a)/l:s}(class extends pl{static id=`timeseries`;static defaults=pl.defaults;constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let e=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(e);this._minPos=ml(t,this.min),this._tableRange=ml(t,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){let{min:t,max:n}=this,r=[],i=[],a,o,s,c,l;for(a=0,o=e.length;a<o;++a)c=e[a],c>=t&&c<=n&&r.push(c);if(r.length<2)return[{time:t,pos:0},{time:n,pos:1}];for(a=0,o=r.length;a<o;++a)l=r[a+1],s=r[a-1],c=r[a],Math.round((l+s)/2)!==c&&i.push({time:c,pos:a/(o-1)});return i}_generate(){let e=this.min,t=this.max,n=super.getDataTimestamps();return(!n.includes(e)||!n.length)&&n.splice(0,0,e),(!n.includes(t)||n.length===1)&&n.push(t),n.sort((e,t)=>e-t)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;let t=this.getDataTimestamps(),n=this.getLabelTimestamps();return e=t.length&&n.length?this.normalize(t.concat(n)):t.length?t:n,e=this._cache.all=e,e}getDecimalForValue(e){return(ml(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){let t=this._offsets,n=this.getDecimalForPixel(e)/t.factor-t.end;return ml(this._table,n*this._tableRange+this._minPos,!0)}});const hl={class:`chart-container`};var gl=C({__name:`LineChart`,props:{data:{},options:{},height:{default:300}},setup(e){Go.register(jc,Fc,vs,gs,sc,Ec,ic,Ys);let t=e,n=d(),r=null,i={responsive:!0,maintainAspectRatio:!1,interaction:{mode:`index`,intersect:!1},plugins:{legend:{position:`top`,labels:{usePointStyle:!0,padding:20,font:{size:12,family:`Roboto, sans-serif`}}},tooltip:{backgroundColor:`rgba(0, 0, 0, 0.8)`,titleColor:`white`,bodyColor:`white`,borderColor:`rgba(255, 255, 255, 0.1)`,borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(e){return`${e.dataset.label}: ${e.parsed.y}%`}}}},scales:{x:{display:!0,grid:{display:!0,color:`rgba(0, 0, 0, 0.05)`},ticks:{font:{size:11,family:`Roboto, sans-serif`}}},y:{display:!0,beginAtZero:!0,max:100,grid:{display:!0,color:`rgba(0, 0, 0, 0.05)`},ticks:{font:{size:11,family:`Roboto, sans-serif`},callback:function(e){return e+`%`}}}},elements:{line:{tension:.4,borderWidth:3},point:{radius:4,hoverRadius:6,borderWidth:2,hoverBorderWidth:3}}},a=()=>{if(!n.value)return;let e={...i,...t.options};r=new Go(n.value,{type:`line`,data:t.data,options:e})},o=()=>{r&&(r.data=t.data,r.update(`active`))},s=()=>{r&&(r.destroy(),r=null)};return l(()=>t.data,o,{deep:!0}),w(()=>{a()}),T(()=>{s()}),(e,t)=>(E(),b(`div`,hl,[_(`canvas`,{ref_key:`chartCanvas`,ref:n},null,512)]))}}),_l=s(gl,[[`__scopeId`,`data-v-32788512`]]);const vl={class:`chart-container`};var yl=C({__name:`DoughnutChart`,props:{data:{},options:{},height:{default:250}},setup(e){Go.register(ts,Ec,ic);let t=e,n=d(),r=null,i={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:`bottom`,labels:{usePointStyle:!0,padding:20,font:{size:12,family:`Roboto, sans-serif`},generateLabels:function(e){let t=e.data;return t.labels&&t.datasets.length?t.labels.map((e,n)=>{let r=t.datasets[0],i=Array.isArray(r.backgroundColor)?r.backgroundColor[n]:r.backgroundColor;return{text:e,fillStyle:i,strokeStyle:i,lineWidth:0,pointStyle:`circle`,hidden:!1,index:n}}):[]}}},tooltip:{backgroundColor:`rgba(0, 0, 0, 0.8)`,titleColor:`white`,bodyColor:`white`,borderColor:`rgba(255, 255, 255, 0.1)`,borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(e){let t=e.label||``,n=e.parsed,r=e.dataset.data.reduce((e,t)=>e+t,0),i=(n/r*100).toFixed(1);return`${t}: ${n} (${i}%)`}}}},cutout:`60%`,elements:{arc:{borderWidth:2,borderColor:`#ffffff`,hoverBorderWidth:3}}},a=()=>{if(!n.value)return;let e={...i,...t.options};r=new Go(n.value,{type:`doughnut`,data:t.data,options:e})},o=()=>{r&&(r.data=t.data,r.update(`active`))},s=()=>{r&&(r.destroy(),r=null)};return l(()=>t.data,o,{deep:!0}),w(()=>{a()}),T(()=>{s()}),(e,t)=>(E(),b(`div`,vl,[_(`canvas`,{ref_key:`chartCanvas`,ref:n},null,512)]))}}),bl=s(yl,[[`__scopeId`,`data-v-e77cdb8d`]]);const xl={class:`text-h4 font-weight-bold text-primary`},Sl={class:`d-flex align-center justify-space-between`},Cl={class:`text-subtitle-2 text-medium-emphasis mb-1`},wl={class:`text-h4 font-weight-bold`},Tl={class:`d-flex align-center mt-2`},El={class:`d-flex align-center`},Dl={class:`chart-container`},Ol={key:1,class:`d-flex align-center justify-center`,style:{height:`300px`}},kl={class:`chart-container`},Al={key:1,class:`d-flex align-center justify-center`,style:{height:`250px`}},jl={class:`d-flex align-center`},Ml={class:`d-flex align-center justify-space-between`},Nl={class:`text-body-2 font-weight-medium mb-1`},Pl={class:`text-caption text-medium-emphasis`},Fl={class:`text-caption text-medium-emphasis`},Il={key:0,class:`text-center py-4`},Ll={class:`d-flex align-center justify-space-between mb-2`},Rl={class:`text-caption`},zl={class:`text-subtitle-1 font-weight-bold mb-1`},Bl={class:`text-body-2 text-medium-emphasis mb-2`},Vl={class:`text-caption mt-1`},Hl={key:0,class:`text-center py-8`};var Ul=C({__name:`DashboardView`,setup(s){let l=o(),C=d(!1),T=d(`month`),k=d(null),A=d(null),fe=d(null),j=d([]),M=g(()=>[{title:`Total Courses`,value:k.value?.total_courses||24,trend:12,icon:`mdi-book-open-page-variant`,color:`primary`},{title:`Active CPMK`,value:k.value?.total_cpmk||156,trend:8,icon:`mdi-bullseye-arrow`,color:`success`},{title:`CPL Mapped`,value:k.value?.total_cpl||32,trend:-3,icon:`mdi-target`,color:`warning`},{title:`Assessments`,value:k.value?.active_assessments||89,trend:15,icon:`mdi-clipboard-check`,color:`info`}]),pe=g(()=>{let e=[{title:`Create New Course`,subtitle:`Add a new course to curriculum`,icon:`mdi-plus`,color:`primary`,to:`/courses?action=create`},{title:`Manage CPMK`,subtitle:`Define learning outcomes`,icon:`mdi-bullseye-arrow`,color:`success`,to:`/cpmk`},{title:`Assessment Planning`,subtitle:`Plan course assessments`,icon:`mdi-calendar-plus`,color:`warning`,to:`/assessments`},{title:`Generate Reports`,subtitle:`View analytics and reports`,icon:`mdi-chart-line`,color:`info`,to:`/reports`}];return e.filter(e=>!0)}),N=d([{id:1,title:`Midterm Exam`,course_name:`Database Systems`,type:`UTS`,due_date:`2025-02-15`,completion_rate:75},{id:2,title:`Project Presentation`,course_name:`Software Engineering`,type:`Project`,due_date:`2025-02-20`,completion_rate:45},{id:3,title:`Lab Assignment`,course_name:`Computer Networks`,type:`Lab`,due_date:`2025-02-25`,completion_rate:90}]),me={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:`top`},title:{display:!1}},scales:{y:{beginAtZero:!0,max:100}}},he={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:`bottom`}}},ge=async()=>{C.value=!0;try{let e=await ae.getStats();k.value=e.data.data;let t=await ae.getChartData(`cpmk-achievement`);A.value=t.data.data;let n=await ae.getChartData(`cpl-distribution`);fe.value=n.data.data;let r=await ae.getRecentActivities();j.value=r.data.data}catch(e){console.error(`Failed to load dashboard data:`,e),_e()}finally{C.value=!1}},_e=()=>{A.value={labels:[`Jan`,`Feb`,`Mar`,`Apr`,`May`,`Jun`],datasets:[{label:`CPMK Achievement (%)`,data:[65,72,68,75,82,78],borderColor:`rgb(25, 118, 210)`,backgroundColor:`rgba(25, 118, 210, 0.1)`,borderWidth:2}]},fe.value={labels:[`Attitude`,`Knowledge`,`General Skills`,`Specific Skills`],datasets:[{label:`CPL Distribution`,data:[25,35,20,20],backgroundColor:[`rgba(76, 175, 80, 0.8)`,`rgba(33, 150, 243, 0.8)`,`rgba(255, 193, 7, 0.8)`,`rgba(156, 39, 176, 0.8)`]}]},j.value=[{id:1,user_id:1,user_name:`Dr. Ahmad Rahman`,action:`create`,resource_type:`course`,resource_id:1,resource_name:`Database Systems`,description:`Created new course "Database Systems"`,created_at:`2025-01-25T10:30:00Z`},{id:2,user_id:2,user_name:`Dr. Maya Sari`,action:`update`,resource_type:`cpmk`,resource_id:5,resource_name:`CPMK-01`,description:`Updated CPMK learning outcomes`,created_at:`2025-01-25T09:15:00Z`}]},ve=async()=>{try{let e=await ae.getRecentActivities();j.value=e.data.data}catch(e){console.error(`Failed to refresh activities:`,e)}},ye=e=>{let t={create:`success`,update:`warning`,delete:`error`,view:`info`};return t[e]||`primary`},be=e=>{let t={UTS:`warning`,UAS:`error`,Project:`success`,Lab:`info`,Quiz:`primary`};return t[e]||`primary`},xe=e=>e>=80?`success`:e>=60?`warning`:`error`,Se=e=>new Date(e).toLocaleDateString(`id-ID`,{day:`numeric`,month:`short`}),Ce=e=>{let t=new Date,n=new Date(e),r=Math.floor((t.getTime()-n.getTime())/(1e3*60*60));return r<1?`Just now`:r<24?`${r}h ago`:`${Math.floor(r/24)}d ago`};return w(()=>{ge()}),(o,s)=>{let d=c(`v-list-item-prepend`);return E(),b(`div`,null,[S(e,{class:`mb-6`},{default:u(()=>[S(t,null,{default:u(()=>[_(`h1`,xl,` Welcome back, `+m(f(l).user?.full_name)+`! `,1),s[1]||=_(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Here's what's happening with your RPS management system today. `,-1)]),_:1,__:[1]})]),_:1}),S(e,{class:`mb-6`},{default:u(()=>[(E(!0),b(h,null,D(M.value,e=>(E(),v(t,{key:e.title,cols:`12`,sm:`6`,md:`3`},{default:u(()=>[S(oe,{class:`stat-card`,color:e.color,variant:`tonal`,hover:``},{default:u(()=>[S(se,null,{default:u(()=>[_(`div`,Sl,[_(`div`,null,[_(`p`,Cl,m(e.title),1),_(`h2`,wl,m(e.value),1),_(`div`,Tl,[S(i,{color:e.trend>0?`success`:`error`,size:`small`,class:`mr-1`},{default:u(()=>[x(m(e.trend>0?`mdi-trending-up`:`mdi-trending-down`),1)]),_:2},1032,[`color`]),_(`span`,{class:p([e.trend>0?`text-success`:`text-error`,`text-caption`])},m(Math.abs(e.trend))+`% from last month `,3)])]),S(i,{color:e.color,size:`48`,class:`stat-icon`},{default:u(()=>[x(m(e.icon),1)]),_:2},1032,[`color`])])]),_:2},1024)]),_:2},1032,[`color`])]),_:2},1024))),128))]),_:1}),S(e,null,{default:u(()=>[S(t,{cols:`12`,md:`8`},{default:u(()=>[S(oe,{class:`mb-6`},{default:u(()=>[S(ce,{class:`d-flex align-center justify-space-between`},{default:u(()=>[_(`div`,El,[S(i,{class:`mr-2`},{default:u(()=>s[2]||=[x(`mdi-chart-line`,-1)]),_:1,__:[2]}),s[3]||=_(`span`,{class:`text-h6 font-weight-bold`},`CPMK Achievement Trends`,-1)]),S(a,{modelValue:T.value,"onUpdate:modelValue":s[0]||=e=>T.value=e,variant:`outlined`,size:`small`,mandatory:``},{default:u(()=>[S(ie,{value:`week`},{default:u(()=>s[4]||=[x(`Week`,-1)]),_:1,__:[4]}),S(ie,{value:`month`},{default:u(()=>s[5]||=[x(`Month`,-1)]),_:1,__:[5]}),S(ie,{value:`semester`},{default:u(()=>s[6]||=[x(`Semester`,-1)]),_:1,__:[6]})]),_:1},8,[`modelValue`])]),_:1}),S(se,null,{default:u(()=>[_(`div`,Dl,[A.value?(E(),v(_l,{key:0,data:A.value,options:me},null,8,[`data`])):(E(),b(`div`,Ol,[S(r,{indeterminate:``,color:`primary`})]))])]),_:1})]),_:1})]),_:1}),S(t,{cols:`12`,md:`4`},{default:u(()=>[S(oe,{class:`mb-6`},{default:u(()=>[S(ce,{class:`text-h6 font-weight-bold`},{default:u(()=>[S(i,{class:`mr-2`},{default:u(()=>s[7]||=[x(`mdi-lightning-bolt`,-1)]),_:1,__:[7]}),s[8]||=x(` Quick Actions `,-1)]),_:1,__:[8]}),S(se,null,{default:u(()=>[S(O,null,{default:u(()=>[(E(!0),b(h,null,D(pe.value,e=>(E(),v(ee,{key:e.title,to:e.to,class:`px-0`},{default:u(()=>[S(d,null,{default:u(()=>[S(re,{color:e.color,size:`40`},{default:u(()=>[S(i,{color:`white`},{default:u(()=>[x(m(e.icon),1)]),_:2},1024)]),_:2},1032,[`color`])]),_:2},1024),S(te,{class:`font-weight-medium`},{default:u(()=>[x(m(e.title),1)]),_:2},1024),S(ne,null,{default:u(()=>[x(m(e.subtitle),1)]),_:2},1024)]),_:2},1032,[`to`]))),128))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),S(e,null,{default:u(()=>[S(t,{cols:`12`,md:`6`},{default:u(()=>[S(oe,{class:`mb-6`},{default:u(()=>[S(ce,{class:`text-h6 font-weight-bold`},{default:u(()=>[S(i,{class:`mr-2`},{default:u(()=>s[9]||=[x(`mdi-chart-donut`,-1)]),_:1,__:[9]}),s[10]||=x(` CPL Distribution by Category `,-1)]),_:1,__:[10]}),S(se,null,{default:u(()=>[_(`div`,kl,[fe.value?(E(),v(bl,{key:0,data:fe.value,options:he},null,8,[`data`])):(E(),b(`div`,Al,[S(r,{indeterminate:``,color:`primary`})]))])]),_:1})]),_:1})]),_:1}),S(t,{cols:`12`,md:`6`},{default:u(()=>[S(oe,{class:`mb-6`},{default:u(()=>[S(ce,{class:`d-flex align-center justify-space-between`},{default:u(()=>[_(`div`,jl,[S(i,{class:`mr-2`},{default:u(()=>s[11]||=[x(`mdi-history`,-1)]),_:1,__:[11]}),s[12]||=_(`span`,{class:`text-h6 font-weight-bold`},`Recent Activities`,-1)]),S(ie,{variant:`text`,size:`small`,onClick:ve},{default:u(()=>[S(i,null,{default:u(()=>s[13]||=[x(`mdi-refresh`,-1)]),_:1,__:[13]})]),_:1})]),_:1}),S(se,null,{default:u(()=>[S(ue,{density:`compact`,align:`start`,class:`activity-timeline`},{default:u(()=>[(E(!0),b(h,null,D(j.value,e=>(E(),v(de,{key:e.id,"dot-color":ye(e.action),size:`small`},{default:u(()=>[_(`div`,Ml,[_(`div`,null,[_(`p`,Nl,m(e.description),1),_(`p`,Pl,` by `+m(e.user_name),1)]),_(`span`,Fl,m(Ce(e.created_at)),1)])]),_:2},1032,[`dot-color`]))),128))]),_:1}),j.value.length===0?(E(),b(`div`,Il,[S(i,{size:`48`,color:`grey-lighten-2`},{default:u(()=>s[14]||=[x(`mdi-history`,-1)]),_:1,__:[14]}),s[15]||=_(`p`,{class:`text-body-2 text-medium-emphasis mt-2`},` No recent activities `,-1)])):y(``,!0)]),_:1})]),_:1})]),_:1})]),_:1}),S(e,null,{default:u(()=>[S(t,{cols:`12`},{default:u(()=>[S(oe,null,{default:u(()=>[S(ce,{class:`text-h6 font-weight-bold`},{default:u(()=>[S(i,{class:`mr-2`},{default:u(()=>s[16]||=[x(`mdi-calendar`,-1)]),_:1,__:[16]}),s[17]||=x(` Upcoming Assessments `,-1)]),_:1,__:[17]}),S(se,null,{default:u(()=>[S(e,null,{default:u(()=>[(E(!0),b(h,null,D(N.value,e=>(E(),v(t,{key:e.id,cols:`12`,sm:`6`,md:`4`,lg:`3`},{default:u(()=>[S(oe,{variant:`outlined`,class:`assessment-card`,hover:``},{default:u(()=>[S(se,null,{default:u(()=>[_(`div`,Ll,[S(le,{color:be(e.type),size:`small`,variant:`tonal`},{default:u(()=>[x(m(e.type),1)]),_:2},1032,[`color`]),_(`span`,Rl,m(Se(e.due_date)),1)]),_(`h4`,zl,m(e.title),1),_(`p`,Bl,m(e.course_name),1),S(n,{"model-value":e.completion_rate,color:xe(e.completion_rate),height:`6`,rounded:``},null,8,[`model-value`,`color`]),_(`p`,Vl,m(e.completion_rate)+`% completed `,1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),N.value.length===0?(E(),b(`div`,Hl,[S(i,{size:`64`,color:`grey-lighten-2`},{default:u(()=>s[18]||=[x(`mdi-calendar-check`,-1)]),_:1,__:[18]}),s[19]||=_(`p`,{class:`text-h6 text-medium-emphasis mt-4`},` No upcoming assessments `,-1),s[20]||=_(`p`,{class:`text-body-2 text-medium-emphasis`},` All caught up! Great work. `,-1)])):y(``,!0)]),_:1})]),_:1})]),_:1})]),_:1})])}}}),Wl=s(Ul,[[`__scopeId`,`data-v-49bd1090`]]);export{Wl as default};