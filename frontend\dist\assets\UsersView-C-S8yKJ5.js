import{b as e,c as t}from"./VSwitch-Bh_Rc-In.js";import{b as n,c as r}from"./VRow-Cvqvybmt.js";import{I as ee,_ as te,bD as i,bF as a,bJ as o,bK as s,bS as c,bd as ne,bf as l,bg as u,bh as d,bi as re,bj as f,bl as p,bm as m,bn as ie,bv as h,bx as g,bz as ae,q as oe,t as se,u as _}from"./index-BSnscBhv.js";import{h as v,j as y,k as b}from"./api-BWRuf0Vj.js";import{b as x,c as S,d as C,e as w}from"./VCard-DVRc-Pxh.js";import{b as T}from"./VChip-CBN0Kf2u.js";import{b as E}from"./VSelect-DqM1bu6y.js";import{b as ce}from"./VDialog-VHlGBbps.js";import{b as D}from"./VTextField-BU8lnKH2.js";import{b as le}from"./FormModal-CAo97PhI.js";import"./VForm-CDHrkI-n.js";import{b as O}from"./VSnackbar-KpoxlJmd.js";const ue={class:`d-flex flex-wrap gap-1`},de={key:0},fe={key:1,class:`text-medium-emphasis`},pe={key:0},me={key:1,class:`text-medium-emphasis`};var k=ie({__name:`UsersView`,setup(ie){let k=s(!1),A=s(!1),j=s(!1),M=s(!1),N=s(!1),P=s(!1),F=s(!1),I=s(!1),L=s(``),R=s(``),z=s([]),B=s([]),V=s([]),H=s(0),U=s(null),W=s(`create`),G=o({full_name:``,username:``,email:``,phone:``,roles:[],faculty_id:null,study_program_id:null,is_active:!0,password:``}),K=s(``),q=s({}),J=s({page:1,per_page:20,sort_by:`created_at`,sort_order:`desc`}),he=[{key:`avatar`,title:`Avatar`,sortable:!1,width:`60px`,type:`text`},{key:`full_name`,title:`Full Name`,sortable:!0,type:`text`},{key:`username`,title:`Username`,sortable:!0,type:`text`},{key:`email`,title:`Email`,sortable:!0,type:`text`},{key:`roles`,title:`Roles`,sortable:!1,type:`text`},{key:`faculty`,title:`Faculty`,sortable:!1,type:`text`},{key:`study_program`,title:`Study Program`,sortable:!1,type:`text`},{key:`is_active`,title:`Status`,sortable:!0,type:`boolean`},{key:`created_at`,title:`Created`,sortable:!0,type:`date`}],ge=[{key:`role`,label:`Role`,options:[{title:`Admin`,value:`admin`},{title:`Dekan`,value:`dekan`},{title:`Wakil Dekan`,value:`wakil_dekan`},{title:`Kepala Prodi`,value:`kepala_prodi`},{title:`Sekretaris Prodi`,value:`sekretaris_prodi`},{title:`Dosen`,value:`dosen`}]},{key:`is_active`,label:`Status`,options:[{title:`Active`,value:!0},{title:`Inactive`,value:!1}]}],_e=l(()=>{switch(W.value){case`create`:return`Create New User`;case`edit`:return`Edit User`;case`view`:return`View User Details`;default:return`User Form`}}),ve=l(()=>{switch(W.value){case`create`:return`mdi-account-plus`;case`edit`:return`mdi-account-edit`;case`view`:return`mdi-account`;default:return`mdi-account`}}),Y=[{title:`Administrator`,value:`admin`},{title:`Dekan`,value:`dekan`},{title:`Wakil Dekan`,value:`wakil_dekan`},{title:`Kepala Program Studi`,value:`kepala_prodi`},{title:`Sekretaris Program Studi`,value:`sekretaris_prodi`},{title:`Dosen`,value:`dosen`}],ye=l(()=>B.value.map(e=>({title:e.name,value:e.id}))),be=l(()=>G.faculty_id?V.value.filter(e=>e.faculty_id===G.faculty_id).map(e=>({title:e.name,value:e.id})):[]),xe=[e=>!!e||`Full name is required`,e=>e.length>=2||`Name must be at least 2 characters`],Se=[e=>!!e||`Username is required`,e=>e.length>=3||`Username must be at least 3 characters`,e=>/^[a-zA-Z0-9_]+$/.test(e)||`Username can only contain letters, numbers, and underscores`],Ce=[e=>!!e||`Email is required`,e=>/.+@.+\..+/.test(e)||`Email must be valid`],we=[e=>e.length>0||`At least one role is required`],Te=[e=>!!e||`Password is required`,e=>e.length>=6||`Password must be at least 6 characters`],X=async()=>{k.value=!0;try{let e={...J.value,search:K.value,...q.value},t=await b.getAll(e);z.value=t.data.data,H.value=t.data.meta?.total||0}catch(e){R.value=`Failed to load users`,I.value=!0,console.error(`Load users error:`,e)}finally{k.value=!1}},Ee=async()=>{try{let e=await v.getAll({per_page:100});B.value=e.data.data}catch(e){console.error(`Load faculties error:`,e)}},De=async()=>{try{let e=await y.getAll({per_page:1e3});V.value=e.data.data}catch(e){console.error(`Load study programs error:`,e)}},Oe=()=>{W.value=`create`,Z(),M.value=!0},ke=e=>{W.value=`edit`,U.value=e,Q(e),M.value=!0},Ae=e=>{W.value=`view`,U.value=e,Q(e),M.value=!0},je=e=>{U.value=e,N.value=!0},Z=()=>{Object.assign(G,{full_name:``,username:``,email:``,phone:``,roles:[],faculty_id:null,study_program_id:null,is_active:!0,password:``})},Q=e=>{Object.assign(G,{full_name:e.full_name,username:e.username,email:e.email,phone:e.phone||``,roles:e.roles,faculty_id:e.faculty_id,study_program_id:e.study_program_id,is_active:e.is_active,password:``})},Me=async()=>{A.value=!0;try{if(W.value===`create`)await b.create(G),L.value=`User created successfully!`;else if(W.value===`edit`&&U.value){let{password:e,...t}=G;await b.update(U.value.id,t),L.value=`User updated successfully!`}F.value=!0,$(),await X()}catch(e){R.value=e.response?.data?.message||`Operation failed`,I.value=!0}finally{A.value=!1}},Ne=async()=>{if(U.value){j.value=!0;try{await b.delete(U.value.id),L.value=`User deleted successfully!`,F.value=!0,N.value=!1,await X()}catch(e){R.value=e.response?.data?.message||`Delete failed`,I.value=!0}finally{j.value=!1}}},$=()=>{M.value=!1,U.value=null,Z()},Pe=e=>{K.value=e,J.value.page=1,X()},Fe=e=>{q.value=e,J.value.page=1,X()},Ie=e=>{J.value={...J.value,page:e.page,per_page:e.itemsPerPage,sort_by:e.sortBy?.[0]?.key||`created_at`,sort_order:e.sortBy?.[0]?.order||`desc`},X()},Le=e=>{let t={admin:`Administrator`,dekan:`Dekan`,wakil_dekan:`Wakil Dekan`,kepala_prodi:`Kepala Program Studi`,sekretaris_prodi:`Sekretaris Program Studi`,dosen:`Dosen`};return t[e]||e};return i(()=>G.faculty_id,()=>{G.study_program_id=null}),h(async()=>{await Promise.all([X(),Ee(),De()])}),(i,o)=>(g(),f(`div`,null,[m(n,{class:`mb-6`},{default:a(()=>[m(r,null,{default:a(()=>o[17]||=[u(`h1`,{class:`text-h4 font-weight-bold text-primary`},`User Management`,-1),u(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Manage system users, roles, and permissions `,-1)]),_:1,__:[17]})]),_:1}),m(t,{title:`System Users`,icon:`mdi-account-group`,"item-name":`User`,headers:he,items:z.value,loading:k.value,"total-items":H.value,filters:ge,onAdd:Oe,onEdit:ke,onDelete:je,onView:Ae,onRefresh:X,onSearch:Pe,onFilter:Fe,"onUpdate:options":Ie},{"item.avatar":a(({item:e})=>[m(se,{size:`32`},{default:a(()=>[e.avatar?(g(),d(te,{key:0,src:e.avatar,alt:e.full_name},null,8,[`src`,`alt`])):(g(),d(ee,{key:1},{default:a(()=>o[18]||=[p(`mdi-account`,-1)]),_:1,__:[18]}))]),_:2},1024)]),"item.roles":a(({item:e})=>[u(`div`,ue,[(g(!0),f(ne,null,ae(e.roles,e=>(g(),d(T,{key:e,size:`small`,color:`primary`,variant:`tonal`},{default:a(()=>[p(c(Le(e)),1)]),_:2},1024))),128))])]),"item.faculty":a(({item:e})=>[e.faculty_name?(g(),f(`span`,de,c(e.faculty_name),1)):(g(),f(`span`,fe,`-`))]),"item.study_program":a(({item:e})=>[e.study_program_name?(g(),f(`span`,pe,c(e.study_program_name),1)):(g(),f(`span`,me,`-`))]),_:1},8,[`items`,`loading`,`total-items`]),m(le,{modelValue:M.value,"onUpdate:modelValue":o[10]||=e=>M.value=e,title:_e.value,icon:ve.value,mode:W.value,loading:A.value,onSubmit:Me,onClose:$},{default:a(()=>[m(n,null,{default:a(()=>[m(r,{cols:`12`,md:`6`},{default:a(()=>[m(D,{modelValue:G.full_name,"onUpdate:modelValue":o[0]||=e=>G.full_name=e,rules:xe,label:`Full Name *`,variant:`outlined`,"prepend-inner-icon":`mdi-account`,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(D,{modelValue:G.username,"onUpdate:modelValue":o[1]||=e=>G.username=e,rules:Se,label:`Username *`,variant:`outlined`,"prepend-inner-icon":`mdi-at`,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(D,{modelValue:G.email,"onUpdate:modelValue":o[2]||=e=>G.email=e,rules:Ce,label:`Email *`,type:`email`,variant:`outlined`,"prepend-inner-icon":`mdi-email`,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(D,{modelValue:G.phone,"onUpdate:modelValue":o[3]||=e=>G.phone=e,label:`Phone Number`,variant:`outlined`,"prepend-inner-icon":`mdi-phone`,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(E,{modelValue:G.roles,"onUpdate:modelValue":o[4]||=e=>G.roles=e,items:Y,rules:we,label:`Roles *`,variant:`outlined`,"prepend-inner-icon":`mdi-shield-account`,multiple:``,chips:``,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(E,{modelValue:G.faculty_id,"onUpdate:modelValue":o[5]||=e=>G.faculty_id=e,items:ye.value,label:`Faculty`,variant:`outlined`,"prepend-inner-icon":`mdi-domain`,clearable:``,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(E,{modelValue:G.study_program_id,"onUpdate:modelValue":o[6]||=e=>G.study_program_id=e,items:be.value,label:`Study Program`,variant:`outlined`,"prepend-inner-icon":`mdi-school`,clearable:``,disabled:A.value||W.value===`view`||!G.faculty_id},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(e,{modelValue:G.is_active,"onUpdate:modelValue":o[7]||=e=>G.is_active=e,label:`Active`,color:`primary`,disabled:A.value||W.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),W.value===`create`?(g(),d(r,{key:0,cols:`12`},{default:a(()=>[m(D,{modelValue:G.password,"onUpdate:modelValue":o[8]||=e=>G.password=e,rules:Te,type:P.value?`text`:`password`,label:`Password *`,variant:`outlined`,"prepend-inner-icon":`mdi-lock`,"append-inner-icon":P.value?`mdi-eye`:`mdi-eye-off`,"onClick:appendInner":o[9]||=e=>P.value=!P.value,disabled:A.value},null,8,[`modelValue`,`type`,`append-inner-icon`,`disabled`])]),_:1})):re(``,!0)]),_:1})]),_:1},8,[`modelValue`,`title`,`icon`,`mode`,`loading`]),m(ce,{modelValue:N.value,"onUpdate:modelValue":o[12]||=e=>N.value=e,"max-width":`400`},{default:a(()=>[m(x,null,{default:a(()=>[m(C,{class:`text-h6`},{default:a(()=>o[19]||=[p(`Confirm Delete`,-1)]),_:1,__:[19]}),m(S,null,{default:a(()=>[p(` Are you sure you want to delete user "`+c(U.value?.full_name)+`"? This action cannot be undone. `,1)]),_:1}),m(w,null,{default:a(()=>[m(oe),m(_,{onClick:o[11]||=e=>N.value=!1},{default:a(()=>o[20]||=[p(`Cancel`,-1)]),_:1,__:[20]}),m(_,{color:`error`,loading:j.value,onClick:Ne},{default:a(()=>o[21]||=[p(` Delete `,-1)]),_:1,__:[21]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(O,{modelValue:F.value,"onUpdate:modelValue":o[14]||=e=>F.value=e,color:`success`,timeout:`3000`},{actions:a(()=>[m(_,{onClick:o[13]||=e=>F.value=!1},{default:a(()=>o[22]||=[p(`Close`,-1)]),_:1,__:[22]})]),default:a(()=>[p(c(L.value)+` `,1)]),_:1},8,[`modelValue`]),m(O,{modelValue:I.value,"onUpdate:modelValue":o[16]||=e=>I.value=e,color:`error`,timeout:`5000`},{actions:a(()=>[m(_,{onClick:o[15]||=e=>I.value=!1},{default:a(()=>o[23]||=[p(`Close`,-1)]),_:1,__:[23]})]),default:a(()=>[p(c(R.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),A=k;export{A as default};