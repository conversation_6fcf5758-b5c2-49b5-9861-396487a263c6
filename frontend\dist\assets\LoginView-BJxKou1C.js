import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{A as n,F as r,G as i,I as a,Q as o,R as s,S as c,T as l,U as u,W as d,X as f,a3 as p,a4 as m,a6 as h,a7 as g,a8 as _,a9 as v,aA as y,aC as b,aG as x,aH as S,aW as C,af as w,ah as T,ai as E,an as D,ao as ee,ap as O,au as te,aw as k,ax as A,b as j,b4 as M,bC as ne,bF as N,bK as P,bM as F,bQ as I,bR as L,bS as R,bc as re,bf as z,bg as B,bh as V,bi as ie,bl as H,bm as U,bn as ae,bq as W,bv as oe,bx as G,d as se,q as K,r as ce,t as le,u as q,z as ue}from"./index-BSnscBhv.js";import{b as J,c as Y,d as de,e as fe}from"./VCard-DVRc-Pxh.js";import{b as pe,c as X,d as me}from"./VDialog-VHlGBbps.js";import{b as Z,h as Q,i as he,m as ge}from"./VTextField-BU8lnKH2.js";import{b as _e}from"./VForm-CDHrkI-n.js";const ve=S({...he(),...M(me(),[`inline`])},`VCheckbox`),ye=b()({name:`VCheckbox`,inheritAttrs:!1,props:ve(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,t){let{attrs:n,slots:r}=t,i=k(e,`modelValue`),{isFocused:a,focus:o,blur:s}=ge(e),c=P(),l=ne();return A(()=>{let[t,u]=C(n),d=Q.filterProps(e),f=X.filterProps(e);return U(Q,W({ref:c,class:[`v-checkbox`,e.class]},t,d,{modelValue:i.value,"onUpdate:modelValue":e=>i.value=e,id:e.id||`checkbox-${l}`,focused:a.value,style:e.style}),{...r,default:e=>{let{id:t,messagesId:n,isDisabled:a,isReadonly:c,isValid:l}=e;return U(X,W(f,{id:t.value,"aria-describedby":n.value,disabled:a.value,readonly:c.value},u,{error:l.value===!1,modelValue:i.value,"onUpdate:modelValue":e=>i.value=e,onFocus:o,onBlur:s}),r)}})}),se({},c)}}),be=y(`v-alert-title`),$=S({iconSize:[Number,String],iconSizes:{type:Array,default:()=>[[`x-small`,10],[`small`,16],[`default`,24],[`large`,28],[`x-large`,32]]}},`iconSize`);function xe(e,t){let n=z(()=>{let n=new Map(e.iconSizes),r=e.iconSize??t()??`default`;return n.has(r)?n.get(r):r});return{iconSize:n}}const Se=[`success`,`info`,`warning`,`error`],Ce=S({border:{type:[Boolean,String],validator:e=>typeof e==`boolean`||[`top`,`end`,`bottom`,`start`].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:O,default:`$close`},closeLabel:{type:String,default:`$vuetify.close`},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>Se.includes(e)},...x(),...l(),...g(),...d(),...$(),...r(),...ue(),...p(),...w(),...D(),...s({variant:`flat`})},`VAlert`),we=b()({name:`VAlert`,props:Ce(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{emit:r,slots:s}=t,l=k(e,`modelValue`),d=F(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),{iconSize:p}=xe(e,()=>e.prominent?44:28),{themeClasses:g}=ee(e),{colorClasses:y,colorStyles:b,variantClasses:x}=c(()=>({color:e.color??e.type,variant:e.variant})),{densityClasses:S}=u(e),{dimensionStyles:C}=_(e),{elevationClasses:w}=f(e),{locationStyles:T}=i(e),{positionClasses:E}=n(e),{roundedClasses:D}=m(e),{textColorClasses:O,textColorStyles:A}=h(()=>e.borderColor),{t:j}=te(),M=F(()=>({"aria-label":j(e.closeLabel),onClick(e){l.value=!1,r(`click:close`,e)}}));return()=>{let t=!!(s.prepend||d.value),n=!!(s.title||e.title),r=!!(s.close||e.closable),i={density:e.density,icon:d.value,size:p.value};return l.value&&U(e.tag,{class:I([`v-alert`,e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?`start`:e.border}`]:!0},{"v-alert--prominent":e.prominent},g.value,y.value,S.value,w.value,E.value,D.value,x.value,e.class]),style:L([b.value,C.value,T.value,e.style]),role:`alert`},{default:()=>[o(!1,`v-alert`),e.border&&B(`div`,{key:`border`,class:I([`v-alert__border`,O.value]),style:L(A.value)},null),t&&B(`div`,{key:`prepend`,class:`v-alert__prepend`},[s.prepend?U(v,{key:`prepend-defaults`,disabled:!d.value,defaults:{VIcon:{...i}}},s.prepend):U(a,W({key:`prepend-icon`},i),null)]),B(`div`,{class:`v-alert__content`},[n&&U(be,{key:`title`},{default:()=>[s.title?.()??e.title]}),s.text?.()??e.text,s.default?.()]),s.append&&B(`div`,{key:`append`,class:`v-alert__append`},[s.append()]),r&&B(`div`,{key:`close`,class:`v-alert__close`},[s.close?U(v,{key:`close-defaults`,defaults:{VBtn:{icon:e.closeIcon,size:`x-small`,variant:`text`}}},{default:()=>[s.close?.({props:M.value})]}):U(q,W({key:`close-btn`,icon:e.closeIcon,size:`x-small`,variant:`text`},M.value),null)])]})}}}),Te={class:`text-center mb-6`},Ee={class:`text-center`},De={class:`text-center mt-4`},Oe={class:`text-caption text-medium-emphasis mb-1`},ke={class:`text-caption text-medium-emphasis`};var Ae=ae({__name:`LoginView`,setup(n){let r=T(),i=E(),o=P(),s=P(!1),c=P(!1),l=P(null),u=P(!1),d=P(!1),f=P(``),p=P({username:``,password:``,remember_me:!1}),m=[e=>!!e||`Username atau email harus diisi`,e=>e.length>=3||`Username minimal 3 karakter`],h=[e=>!!e||`Password harus diisi`,e=>e.length>=6||`Password minimal 6 karakter`],g=z(()=>`RPS Management System`),_=z(()=>`1.0.0`),v=z(()=>`Universitas Example`),y=z(()=>new Date().getFullYear()),b=async()=>{if(s.value){c.value=!0,l.value=null;try{await i.login(p.value),r.push(`/dashboard`)}catch(e){l.value=e.response?.data?.message||`Login gagal. Silakan coba lagi.`}finally{c.value=!1}}},x=async()=>{if(f.value)try{console.log(`Reset password for:`,f.value),d.value=!1,f.value=``}catch(e){console.error(`Forgot password error:`,e)}};return oe(()=>{l.value=null,setTimeout(()=>{let e=document.querySelector(`input[type="text"]`);e&&e.focus()},100)}),(n,r)=>(G(),V(ce,{fluid:``,class:`login-container fill-height`},{default:N(()=>[U(e,{align:`center`,justify:`center`,class:`fill-height no-gutters`},{default:N(()=>[U(t,{cols:`12`,sm:`10`,md:`8`,lg:`6`,xl:`4`,class:`d-flex justify-center`},{default:N(()=>[U(J,{class:`login-card elevation-12 rounded-lg`,"max-width":`400`,width:`100%`},{default:N(()=>[U(Y,{class:`pa-6 pa-sm-8`},{default:N(()=>[B(`div`,Te,[U(le,{size:`60`,class:`mx-auto mb-3 elevation-2`,color:`primary`},{default:N(()=>[U(a,{size:`30`,color:`white`},{default:N(()=>r[10]||=[H(`mdi-school`,-1)]),_:1,__:[10]})]),_:1}),r[11]||=B(`h1`,{class:`text-h5 font-weight-bold text-primary mb-1`},` RPS Management `,-1),r[12]||=B(`p`,{class:`text-body-2 text-medium-emphasis`},` Sistem Manajemen Rencana Pembelajaran Semester `,-1)]),U(_e,{ref_key:`form`,ref:o,modelValue:s.value,"onUpdate:modelValue":r[5]||=e=>s.value=e,onSubmit:re(b,[`prevent`])},{default:N(()=>[U(Z,{modelValue:p.value.username,"onUpdate:modelValue":r[0]||=e=>p.value.username=e,rules:m,label:`Username atau Email`,"prepend-inner-icon":`mdi-account`,variant:`outlined`,class:`mb-3`,disabled:c.value,autofocus:``},null,8,[`modelValue`,`disabled`]),U(Z,{modelValue:p.value.password,"onUpdate:modelValue":r[1]||=e=>p.value.password=e,rules:h,type:u.value?`text`:`password`,label:`Password`,"prepend-inner-icon":`mdi-lock`,"append-inner-icon":u.value?`mdi-eye`:`mdi-eye-off`,"onClick:appendInner":r[2]||=e=>u.value=!u.value,variant:`outlined`,class:`mb-3`,disabled:c.value},null,8,[`modelValue`,`type`,`append-inner-icon`,`disabled`]),U(ye,{modelValue:p.value.remember_me,"onUpdate:modelValue":r[3]||=e=>p.value.remember_me=e,label:`Remember me`,color:`primary`,class:`mb-3`,disabled:c.value},null,8,[`modelValue`,`disabled`]),l.value?(G(),V(we,{key:0,type:`error`,variant:`tonal`,class:`mb-4`,closable:``,"onClick:close":r[4]||=e=>l.value=null},{default:N(()=>[H(R(l.value),1)]),_:1})):ie(``,!0),U(q,{type:`submit`,color:`primary`,size:`large`,block:``,loading:c.value,disabled:!s.value,class:`mb-4`},{default:N(()=>[U(a,{start:``},{default:N(()=>r[13]||=[H(`mdi-login`,-1)]),_:1,__:[13]}),r[14]||=H(` Login `,-1)]),_:1,__:[14]},8,[`loading`,`disabled`])]),_:1},8,[`modelValue`]),B(`div`,Ee,[U(q,{variant:`text`,color:`primary`,size:`small`,onClick:r[6]||=e=>d.value=!0},{default:N(()=>r[15]||=[H(` Forgot Password? `,-1)]),_:1,__:[15]})])]),_:1})]),_:1}),B(`div`,De,[B(`p`,Oe,R(g.value)+` v`+R(_.value),1),B(`p`,ke,` © `+R(y.value)+` `+R(v.value),1)])]),_:1})]),_:1}),U(pe,{modelValue:d.value,"onUpdate:modelValue":r[9]||=e=>d.value=e,"max-width":`400`},{default:N(()=>[U(J,null,{default:N(()=>[U(de,{class:`text-h6`},{default:N(()=>r[16]||=[H(`Reset Password`,-1)]),_:1,__:[16]}),U(Y,null,{default:N(()=>[U(Z,{modelValue:f.value,"onUpdate:modelValue":r[7]||=e=>f.value=e,label:`Email Address`,type:`email`,variant:`outlined`,"prepend-inner-icon":`mdi-email`},null,8,[`modelValue`])]),_:1}),U(fe,null,{default:N(()=>[U(K),U(q,{onClick:r[8]||=e=>d.value=!1},{default:N(()=>r[17]||=[H(`Cancel`,-1)]),_:1,__:[17]}),U(q,{color:`primary`,onClick:x},{default:N(()=>r[18]||=[H(` Send Reset Link `,-1)]),_:1,__:[18]})]),_:1})]),_:1})]),_:1},8,[`modelValue`])]),_:1}))}}),je=j(Ae,[[`__scopeId`,`data-v-79f8a217`]]);export{je as default};