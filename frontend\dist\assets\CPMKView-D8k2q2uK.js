import{b as e,c as t}from"./VSwitch-Bh_Rc-In.js";import{b as n,c as r}from"./VRow-Cvqvybmt.js";import{E as i,I as a,b as ee,bF as o,bJ as s,bK as c,bO as l,bS as u,bd as d,bf as f,bg as p,bh as m,bi as h,bj as g,bl as _,bm as v,bn as y,bv as te,bx as b,bz as x,q as ne,s as S,u as C}from"./index-BSnscBhv.js";import{d as w,f as T}from"./api-BWRuf0Vj.js";import{b as E,c as D,d as O,e as re}from"./VCard-DVRc-Pxh.js";import{b as k}from"./VChip-CBN0Kf2u.js";import{b as ie}from"./VSelect-DqM1bu6y.js";import{b as A}from"./VDialog-VHlGBbps.js";import{b as ae}from"./VTextField-BU8lnKH2.js";import{b as oe,c as j}from"./FormModal-CAo97PhI.js";import"./VForm-CDHrkI-n.js";import{b as se}from"./VSnackbar-KpoxlJmd.js";import{b as ce}from"./VTextarea-BciMMY-M.js";const M={class:`pa-6`},N={class:`d-flex align-center justify-space-between mb-4`},P={class:`text-h6 font-weight-bold`},F={class:`d-flex align-start justify-space-between`},I={class:`flex-grow-1`},L={class:`d-flex align-center mb-2`},R={class:`text-subtitle-1 font-weight-bold mb-1`},z={key:0,class:`text-body-2 text-medium-emphasis mb-2`},B={key:1,class:`mb-2`},V={class:`d-flex flex-column gap-1 ml-4`},H={key:0,class:`text-center py-8`};var U=y({__name:`SubCPMKManagement`,props:{cpmk:{}},emits:[`close`],setup(e,{emit:t}){let i=e,ee=c(!1),y=c(!1),S=c(!1),w=c(!1),U=c(!1),W=c(!1),G=c(!1),K=c(``),q=c(``),J=c([]),Y=c(null),X=c(`create`),Z=s({code:``,description:``,learning_indicator:``,week_coverage:[],weight_percentage:null}),Q=f(()=>X.value===`create`?`Add Sub-CPMK`:`Edit Sub-CPMK`),le=Array.from({length:16},(e,t)=>({title:`Week ${t+1}`,value:t+1})),ue=[e=>!!e||`Sub-CPMK code is required`],de=[e=>!!e||`Description is required`],fe=[e=>!!e||`Learning indicator is required`],pe=async()=>{ee.value=!0;try{let e=await T.getSubCPMK(i.cpmk.id);J.value=e.data.data}catch{q.value=`Failed to load sub-CPMKs`,G.value=!0}finally{ee.value=!1}},me=()=>{X.value=`create`,_e(),w.value=!0},he=e=>{X.value=`edit`,Y.value=e,ve(e),w.value=!0},ge=e=>{Y.value=e,U.value=!0},_e=()=>{Object.assign(Z,{code:``,description:``,learning_indicator:``,week_coverage:[],weight_percentage:null})},ve=e=>{Object.assign(Z,{code:e.code,description:e.description,learning_indicator:e.learning_indicator,week_coverage:e.week_coverage||[],weight_percentage:e.weight_percentage})},ye=async()=>{y.value=!0;try{let e={...Z,cpmk_id:i.cpmk.id};X.value===`create`?(await T.createSubCPMK(i.cpmk.id,e),K.value=`Sub-CPMK added successfully!`):Y.value&&(await T.updateSubCPMK(i.cpmk.id,Y.value.id,e),K.value=`Sub-CPMK updated successfully!`),W.value=!0,xe(),await pe()}catch(e){q.value=e.response?.data?.message||`Operation failed`,G.value=!0}finally{y.value=!1}},be=async()=>{if(Y.value){S.value=!0;try{await T.deleteSubCPMK(i.cpmk.id,Y.value.id),K.value=`Sub-CPMK deleted successfully!`,W.value=!0,U.value=!1,await pe()}catch(e){q.value=e.response?.data?.message||`Delete failed`,G.value=!0}finally{S.value=!1}}},xe=()=>{w.value=!1,Y.value=null,_e()};return te(()=>{pe()}),(e,t)=>(b(),g(`div`,M,[p(`div`,N,[p(`div`,null,[p(`h3`,P,u(e.cpmk.code)+` - Sub-CPMK`,1),t[10]||=p(`p`,{class:`text-subtitle-2 text-medium-emphasis`},` Manage detailed learning indicators for this CPMK `,-1)]),v(C,{color:`primary`,onClick:me},{default:o(()=>[v(a,{start:``},{default:o(()=>t[11]||=[_(`mdi-plus`,-1)]),_:1,__:[11]}),t[12]||=_(` Add Sub-CPMK `,-1)]),_:1,__:[12]})]),v(n,null,{default:o(()=>[(b(!0),g(d,null,x(J.value,e=>(b(),m(r,{key:e.id,cols:`12`},{default:o(()=>[v(E,{variant:`outlined`,class:`mb-3`},{default:o(()=>[v(D,null,{default:o(()=>[p(`div`,F,[p(`div`,I,[p(`div`,L,[v(k,{color:`primary`,size:`small`,variant:`tonal`,class:`mr-2`},{default:o(()=>[_(u(e.code),1)]),_:2},1024),e.weight_percentage?(b(),m(k,{key:0,size:`small`,variant:`outlined`},{default:o(()=>[_(u(e.weight_percentage)+`% `,1)]),_:2},1024)):h(``,!0)]),p(`h4`,R,u(e.description),1),e.learning_indicator?(b(),g(`p`,z,[v(a,{size:`small`,class:`mr-1`},{default:o(()=>t[13]||=[_(`mdi-target`,-1)]),_:1,__:[13]}),_(` `+u(e.learning_indicator),1)])):h(``,!0),e.week_coverage&&e.week_coverage.length>0?(b(),g(`div`,B,[t[14]||=p(`span`,{class:`text-caption text-medium-emphasis mr-2`},`Week Coverage:`,-1),(b(!0),g(d,null,x(e.week_coverage,e=>(b(),m(k,{key:e,size:`x-small`,variant:`outlined`,class:`ma-1`},{default:o(()=>[_(` Week `+u(e),1)]),_:2},1024))),128))])):h(``,!0)]),p(`div`,V,[v(C,{icon:``,size:`small`,variant:`text`,onClick:t=>he(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>t[15]||=[_(`mdi-pencil`,-1)]),_:1,__:[15]}),v(j,{activator:`parent`},{default:o(()=>t[16]||=[_(`Edit`,-1)]),_:1,__:[16]})]),_:2},1032,[`onClick`]),v(C,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>ge(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>t[17]||=[_(`mdi-delete`,-1)]),_:1,__:[17]}),v(j,{activator:`parent`},{default:o(()=>t[18]||=[_(`Delete`,-1)]),_:1,__:[18]})]),_:2},1032,[`onClick`])])])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),J.value.length===0?(b(),g(`div`,H,[v(a,{size:`64`,color:`grey-lighten-2`},{default:o(()=>t[19]||=[_(`mdi-format-list-bulleted`,-1)]),_:1,__:[19]}),t[22]||=p(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`No sub-CPMK defined yet`,-1),v(C,{color:`primary`,onClick:me,class:`mt-2`},{default:o(()=>[v(a,{start:``},{default:o(()=>t[20]||=[_(`mdi-plus`,-1)]),_:1,__:[20]}),t[21]||=_(` Add First Sub-CPMK `,-1)]),_:1,__:[21]})])):h(``,!0),v(oe,{modelValue:w.value,"onUpdate:modelValue":t[5]||=e=>w.value=e,title:Q.value,icon:`mdi-format-list-bulleted`,mode:X.value,loading:y.value,"max-width":`700`,onSubmit:ye,onClose:xe},{default:o(()=>[v(n,null,{default:o(()=>[v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ae,{modelValue:Z.code,"onUpdate:modelValue":t[0]||=e=>Z.code=e,rules:ue,label:`Sub-CPMK Code *`,variant:`outlined`,"prepend-inner-icon":`mdi-identifier`,disabled:y.value,placeholder:`e.g., CPMK-01.1`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ae,{modelValue:Z.weight_percentage,"onUpdate:modelValue":t[1]||=e=>Z.weight_percentage=e,label:`Weight Percentage`,type:`number`,variant:`outlined`,"prepend-inner-icon":`mdi-scale-balance`,suffix:`%`,disabled:y.value,min:`0`,max:`100`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`},{default:o(()=>[v(ce,{modelValue:Z.description,"onUpdate:modelValue":t[2]||=e=>Z.description=e,rules:de,label:`Description *`,variant:`outlined`,"prepend-inner-icon":`mdi-text`,rows:`3`,disabled:y.value},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`},{default:o(()=>[v(ce,{modelValue:Z.learning_indicator,"onUpdate:modelValue":t[3]||=e=>Z.learning_indicator=e,rules:fe,label:`Learning Indicator *`,variant:`outlined`,"prepend-inner-icon":`mdi-target`,rows:`3`,disabled:y.value},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`},{default:o(()=>[v(ie,{modelValue:Z.week_coverage,"onUpdate:modelValue":t[4]||=e=>Z.week_coverage=e,items:l(le),label:`Week Coverage`,variant:`outlined`,"prepend-inner-icon":`mdi-calendar-week`,multiple:``,chips:``,disabled:y.value},null,8,[`modelValue`,`items`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`mode`,`loading`]),v(A,{modelValue:U.value,"onUpdate:modelValue":t[7]||=e=>U.value=e,"max-width":`400`},{default:o(()=>[v(E,null,{default:o(()=>[v(O,{class:`text-h6`},{default:o(()=>t[23]||=[_(`Confirm Delete`,-1)]),_:1,__:[23]}),v(D,null,{default:o(()=>t[24]||=[_(` Are you sure you want to delete this sub-CPMK? This action cannot be undone. `,-1)]),_:1,__:[24]}),v(re,null,{default:o(()=>[v(ne),v(C,{onClick:t[6]||=e=>U.value=!1},{default:o(()=>t[25]||=[_(`Cancel`,-1)]),_:1,__:[25]}),v(C,{color:`error`,loading:S.value,onClick:be},{default:o(()=>t[26]||=[_(` Delete `,-1)]),_:1,__:[26]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),v(se,{modelValue:W.value,"onUpdate:modelValue":t[8]||=e=>W.value=e,color:`success`,timeout:`3000`},{default:o(()=>[_(u(K.value),1)]),_:1},8,[`modelValue`]),v(se,{modelValue:G.value,"onUpdate:modelValue":t[9]||=e=>G.value=e,color:`error`,timeout:`5000`},{default:o(()=>[_(u(q.value),1)]),_:1},8,[`modelValue`])]))}}),W=ee(U,[[`__scopeId`,`data-v-dfac0e2b`]]);const G={class:`pa-6`},K={class:`text-center py-8`},q={class:`text-body-2 text-medium-emphasis`};var J=y({__name:`CPMKCPLRelations`,props:{cpmk:{}},emits:[`close`],setup(e){return(e,t)=>(b(),g(`div`,G,[p(`div`,K,[v(a,{size:`64`,color:`grey-lighten-2`},{default:o(()=>t[0]||=[_(`mdi-vector-link`,-1)]),_:1,__:[0]}),t[1]||=p(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`CPMK-CPL Relations`,-1),p(`p`,q,` Manage relationships between CPMK and CPL for `+u(e.cpmk.code),1),t[2]||=p(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),Y=J;const X={class:`pa-6`},Z={class:`text-center py-8`};var Q=y({__name:`CPMKWeightValidation`,props:{courseId:{}},emits:[`close`],setup(e){return(e,t)=>(b(),g(`div`,X,[p(`div`,Z,[v(a,{size:`64`,color:`grey-lighten-2`},{default:o(()=>t[0]||=[_(`mdi-scale-balance`,-1)]),_:1,__:[0]}),t[1]||=p(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Weight Validation`,-1),t[2]||=p(`p`,{class:`text-body-2 text-medium-emphasis`},` Validate CPMK weight distribution for the selected course `,-1),t[3]||=p(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),le=Q;const ue={class:`pa-6`},de={class:`text-center py-8`};var fe=y({__name:`CPMKCPLMapping`,props:{courseId:{}},emits:[`close`],setup(e){return(e,t)=>(b(),g(`div`,ue,[p(`div`,de,[v(a,{size:`64`,color:`grey-lighten-2`},{default:o(()=>t[0]||=[_(`mdi-vector-link`,-1)]),_:1,__:[0]}),t[1]||=p(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`CPMK-CPL Mapping`,-1),t[2]||=p(`p`,{class:`text-body-2 text-medium-emphasis`},` Visual mapping between CPMK and CPL for the selected course `,-1),t[3]||=p(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),pe=fe;const me={class:`d-flex align-center justify-space-between`},he={class:`text-h4 font-weight-bold`},ge={class:`d-flex align-center justify-space-between`},_e={class:`text-h4 font-weight-bold`},ve={class:`d-flex align-center justify-space-between`},ye={class:`text-h4 font-weight-bold`},be={class:`d-flex align-center justify-space-between`},xe={class:`text-h4 font-weight-bold`},Se={class:`d-flex align-center`},Ce={class:`text-caption font-weight-medium`},we={key:0},Te={class:`font-weight-medium`},Ee={class:`text-caption text-medium-emphasis`},De={key:1,class:`text-medium-emphasis`},Oe={class:`text-truncate`,style:{"max-width":`300px`}},ke={class:`d-flex align-center gap-1`},Ae={class:`d-flex align-center`},je={class:`text-h6 font-weight-bold`},Me={class:`d-flex align-center`},Ne={class:`text-h6 font-weight-bold`},Pe={class:`d-flex align-center`},Fe={class:`d-flex align-center`};var Ie=y({__name:`CPMKView`,setup(ee){let l=c(!1),d=c(!1),y=c(!1),x=c(!1),M=c(!1),N=c(!1),P=c(!1),F=c(!1),I=c(!1),L=c(!1),R=c(!1),z=c(``),B=c(``),V=c([]),H=c([]),U=c(0),G=c(null),K=c(null),q=c(`create`),J=s({code:``,course_id:null,description:``,learning_outcome:``,cognitive_level:`C1`,weight_percentage:null,is_active:!0}),X=c(``),Z=c({}),Q=c({page:1,per_page:20,sort_by:`created_at`,sort_order:`desc`}),ue=f(()=>V.value.length),de=f(()=>V.value.filter(e=>e.is_active).length),fe=f(()=>{if(V.value.length===0)return 0;let e=V.value.reduce((e,t)=>e+t.weight_percentage,0);return Math.round(e/V.value.length)}),Ie=f(()=>V.value.filter(e=>e.cpl_relations&&e.cpl_relations.length>0).length),Le=[{key:`code`,title:`Code`,sortable:!0,type:`text`},{key:`course`,title:`Course`,sortable:!1,type:`text`},{key:`description`,title:`Description`,sortable:!1,type:`text`},{key:`cognitive_level`,title:`Cognitive Level`,sortable:!0,type:`text`},{key:`weight_percentage`,title:`Weight`,sortable:!0,type:`text`},{key:`is_active`,title:`Status`,sortable:!0,type:`boolean`},{key:`created_at`,title:`Created`,sortable:!0,type:`date`}],Re=[{key:`cognitive_level`,label:`Cognitive Level`,options:[{title:`C1 - Remember`,value:`C1`},{title:`C2 - Understand`,value:`C2`},{title:`C3 - Apply`,value:`C3`},{title:`C4 - Analyze`,value:`C4`},{title:`C5 - Evaluate`,value:`C5`},{title:`C6 - Create`,value:`C6`}]},{key:`course_id`,label:`Course`,options:[]},{key:`is_active`,label:`Status`,options:[{title:`Active`,value:!0},{title:`Inactive`,value:!1}]}],ze=f(()=>{switch(q.value){case`create`:return`Create New CPMK`;case`edit`:return`Edit CPMK`;case`view`:return`View CPMK Details`;default:return`CPMK Form`}}),Be=f(()=>{switch(q.value){case`create`:return`mdi-bullseye-plus`;case`edit`:return`mdi-bullseye-edit`;case`view`:return`mdi-bullseye-arrow`;default:return`mdi-bullseye-arrow`}}),Ve=f(()=>H.value.map(e=>({title:`${e.code} - ${e.name}`,value:e.id}))),He=[{title:`C1 - Remember (Mengingat)`,value:`C1`},{title:`C2 - Understand (Memahami)`,value:`C2`},{title:`C3 - Apply (Menerapkan)`,value:`C3`},{title:`C4 - Analyze (Menganalisis)`,value:`C4`},{title:`C5 - Evaluate (Mengevaluasi)`,value:`C5`},{title:`C6 - Create (Mencipta)`,value:`C6`}],Ue=[e=>!!e||`CPMK code is required`,e=>e.length>=3||`CPMK code must be at least 3 characters`,e=>/^CPMK-\d{2}$/.test(e)||`CPMK code format: CPMK-01, CPMK-02, etc.`],We=[e=>!!e||`Course is required`],Ge=[e=>!!e||`Cognitive level is required`],Ke=[e=>e!=null||`Weight percentage is required`,e=>e>=0&&e<=100||`Weight must be between 0 and 100`],qe=[e=>!!e||`Description is required`,e=>e.length>=10||`Description must be at least 10 characters`],Je=[e=>!!e||`Learning outcome statement is required`,e=>e.length>=20||`Learning outcome must be at least 20 characters`],$=async()=>{l.value=!0;try{let e={...Q.value,search:X.value,...Z.value},t=await T.getAll(e);V.value=t.data.data,U.value=t.data.meta?.total||0}catch(e){B.value=`Failed to load CPMKs`,R.value=!0,console.error(`Load CPMKs error:`,e)}finally{l.value=!1}},Ye=async()=>{try{let e=await w.getAll({per_page:1e3});H.value=e.data.data,Re[1].options=H.value.map(e=>({title:`${e.code} - ${e.name}`,value:e.id}))}catch(e){console.error(`Load courses error:`,e)}},Xe=()=>{K.value?(Z.value.course_id=K.value,$()):(delete Z.value.course_id,$())},Ze=()=>{q.value=`create`,rt(),x.value=!0},Qe=e=>{q.value=`edit`,G.value=e,it(e),x.value=!0},$e=e=>{q.value=`view`,G.value=e,it(e),x.value=!0},et=e=>{G.value=e,M.value=!0},tt=e=>{G.value=e,N.value=!0},nt=e=>{G.value=e,I.value=!0},rt=()=>{Object.assign(J,{code:``,course_id:K.value,description:``,learning_outcome:``,cognitive_level:`C1`,weight_percentage:null,is_active:!0})},it=e=>{Object.assign(J,{code:e.code,course_id:e.course_id,description:e.description,learning_outcome:e.learning_outcome,cognitive_level:e.cognitive_level,weight_percentage:e.weight_percentage,is_active:e.is_active})},at=async()=>{d.value=!0;try{q.value===`create`?(await T.create(J),z.value=`CPMK created successfully!`):q.value===`edit`&&G.value&&(await T.update(G.value.id,J),z.value=`CPMK updated successfully!`),L.value=!0,st(),await $()}catch(e){B.value=e.response?.data?.message||`Operation failed`,R.value=!0}finally{d.value=!1}},ot=async()=>{if(G.value){y.value=!0;try{await T.delete(G.value.id),z.value=`CPMK deleted successfully!`,L.value=!0,I.value=!1,await $()}catch(e){B.value=e.response?.data?.message||`Delete failed`,R.value=!0}finally{y.value=!1}}},st=()=>{x.value=!1,G.value=null,rt()},ct=e=>{X.value=e,Q.value.page=1,$()},lt=e=>{Z.value=e,Q.value.page=1,$()},ut=e=>{Q.value={...Q.value,page:e.page,per_page:e.itemsPerPage,sort_by:e.sortBy?.[0]?.key||`created_at`,sort_order:e.sortBy?.[0]?.order||`desc`},$()},dt=e=>{let t={C1:`blue-grey`,C2:`blue`,C3:`green`,C4:`orange`,C5:`deep-orange`,C6:`red`};return t[e]||`primary`},ft=e=>e>=80?`success`:e>=60?`warning`:e>=40?`orange`:`error`;return te(async()=>{await Promise.all([Ye(),$()])}),(ee,s)=>(b(),g(`div`,null,[v(n,{class:`mb-6`},{default:o(()=>[v(r,null,{default:o(()=>s[29]||=[p(`h1`,{class:`text-h4 font-weight-bold text-primary`},`CPMK Management`,-1),p(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Manage Course Learning Outcomes (Capaian Pembelajaran Mata Kuliah) `,-1)]),_:1,__:[29]})]),_:1}),v(n,{class:`mb-6`},{default:o(()=>[v(r,{cols:`12`,sm:`6`,md:`3`},{default:o(()=>[v(E,{color:`primary`,variant:`tonal`},{default:o(()=>[v(D,null,{default:o(()=>[p(`div`,me,[p(`div`,null,[s[30]||=p(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total CPMK`,-1),p(`h2`,he,u(ue.value),1)]),v(a,{size:`48`,color:`primary`},{default:o(()=>s[31]||=[_(`mdi-bullseye-arrow`,-1)]),_:1,__:[31]})])]),_:1})]),_:1})]),_:1}),v(r,{cols:`12`,sm:`6`,md:`3`},{default:o(()=>[v(E,{color:`success`,variant:`tonal`},{default:o(()=>[v(D,null,{default:o(()=>[p(`div`,ge,[p(`div`,null,[s[32]||=p(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Active CPMK`,-1),p(`h2`,_e,u(de.value),1)]),v(a,{size:`48`,color:`success`},{default:o(()=>s[33]||=[_(`mdi-check-circle`,-1)]),_:1,__:[33]})])]),_:1})]),_:1})]),_:1}),v(r,{cols:`12`,sm:`6`,md:`3`},{default:o(()=>[v(E,{color:`warning`,variant:`tonal`},{default:o(()=>[v(D,null,{default:o(()=>[p(`div`,ve,[p(`div`,null,[s[34]||=p(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Avg Weight`,-1),p(`h2`,ye,u(fe.value)+`%`,1)]),v(a,{size:`48`,color:`warning`},{default:o(()=>s[35]||=[_(`mdi-scale-balance`,-1)]),_:1,__:[35]})])]),_:1})]),_:1})]),_:1}),v(r,{cols:`12`,sm:`6`,md:`3`},{default:o(()=>[v(E,{color:`info`,variant:`tonal`},{default:o(()=>[v(D,null,{default:o(()=>[p(`div`,be,[p(`div`,null,[s[36]||=p(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`CPL Mapped`,-1),p(`h2`,xe,u(Ie.value),1)]),v(a,{size:`48`,color:`info`},{default:o(()=>s[37]||=[_(`mdi-vector-link`,-1)]),_:1,__:[37]})])]),_:1})]),_:1})]),_:1})]),_:1}),v(n,{class:`mb-4`},{default:o(()=>[v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ie,{modelValue:K.value,"onUpdate:modelValue":[s[0]||=e=>K.value=e,Xe],items:Ve.value,label:`Select Course`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open-page-variant`,clearable:``},null,8,[`modelValue`,`items`])]),_:1}),v(r,{cols:`12`,md:`6`,class:`d-flex align-center gap-2`},{default:o(()=>[v(C,{color:`secondary`,variant:`outlined`,onClick:s[1]||=e=>P.value=!0,disabled:!K.value},{default:o(()=>[v(a,{start:``},{default:o(()=>s[38]||=[_(`mdi-scale-balance`,-1)]),_:1,__:[38]}),s[39]||=_(` Validate Weights `,-1)]),_:1,__:[39]},8,[`disabled`]),v(C,{color:`info`,variant:`outlined`,onClick:s[2]||=e=>F.value=!0,disabled:!K.value},{default:o(()=>[v(a,{start:``},{default:o(()=>s[40]||=[_(`mdi-vector-link`,-1)]),_:1,__:[40]}),s[41]||=_(` CPL Mapping `,-1)]),_:1,__:[41]},8,[`disabled`])]),_:1})]),_:1}),v(t,{title:`Course Learning Outcomes (CPMK)`,icon:`mdi-bullseye-arrow`,"item-name":`CPMK`,headers:Le,items:V.value,loading:l.value,"total-items":U.value,filters:Re,onAdd:Ze,onEdit:Qe,onDelete:nt,onView:$e,onRefresh:$,onSearch:ct,onFilter:lt,"onUpdate:options":ut},{"item.code":o(({item:e})=>[v(k,{color:`primary`,variant:`tonal`,size:`small`,class:`font-weight-bold`},{default:o(()=>[_(u(e.code),1)]),_:2},1024)]),"item.cognitive_level":o(({item:e})=>[v(k,{color:dt(e.cognitive_level),variant:`tonal`,size:`small`},{default:o(()=>[_(u(e.cognitive_level),1)]),_:2},1032,[`color`])]),"item.weight_percentage":o(({item:e})=>[p(`div`,Se,[v(i,{"model-value":e.weight_percentage,color:ft(e.weight_percentage),height:`6`,rounded:``,class:`mr-2`,style:{"min-width":`60px`}},null,8,[`model-value`,`color`]),p(`span`,Ce,u(e.weight_percentage)+`%`,1)])]),"item.course":o(({item:e})=>[e.course_name?(b(),g(`div`,we,[p(`div`,Te,u(e.course_name),1),p(`div`,Ee,u(e.course_code),1)])):(b(),g(`span`,De,`-`))]),"item.description":o(({item:e})=>[p(`div`,Oe,u(e.description),1)]),"item.actions":o(({item:e})=>[p(`div`,ke,[v(C,{icon:``,size:`small`,variant:`text`,onClick:t=>$e(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>s[42]||=[_(`mdi-eye`,-1)]),_:1,__:[42]}),v(j,{activator:`parent`},{default:o(()=>s[43]||=[_(`View Details`,-1)]),_:1,__:[43]})]),_:2},1032,[`onClick`]),v(C,{icon:``,size:`small`,variant:`text`,onClick:t=>Qe(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>s[44]||=[_(`mdi-pencil`,-1)]),_:1,__:[44]}),v(j,{activator:`parent`},{default:o(()=>s[45]||=[_(`Edit CPMK`,-1)]),_:1,__:[45]})]),_:2},1032,[`onClick`]),v(C,{icon:``,size:`small`,variant:`text`,onClick:t=>et(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>s[46]||=[_(`mdi-format-list-bulleted`,-1)]),_:1,__:[46]}),v(j,{activator:`parent`},{default:o(()=>s[47]||=[_(`Manage Sub-CPMK`,-1)]),_:1,__:[47]})]),_:2},1032,[`onClick`]),v(C,{icon:``,size:`small`,variant:`text`,onClick:t=>tt(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>s[48]||=[_(`mdi-vector-link`,-1)]),_:1,__:[48]}),v(j,{activator:`parent`},{default:o(()=>s[49]||=[_(`CPL Relations`,-1)]),_:1,__:[49]})]),_:2},1032,[`onClick`]),v(C,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>nt(e)},{default:o(()=>[v(a,{size:`small`},{default:o(()=>s[50]||=[_(`mdi-delete`,-1)]),_:1,__:[50]}),v(j,{activator:`parent`},{default:o(()=>s[51]||=[_(`Delete CPMK`,-1)]),_:1,__:[51]})]),_:2},1032,[`onClick`])])]),_:1},8,[`items`,`loading`,`total-items`]),v(oe,{modelValue:x.value,"onUpdate:modelValue":s[10]||=e=>x.value=e,title:ze.value,icon:Be.value,mode:q.value,loading:d.value,"max-width":`900`,onSubmit:at,onClose:st},{default:o(()=>[v(n,null,{default:o(()=>[v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ae,{modelValue:J.code,"onUpdate:modelValue":s[3]||=e=>J.code=e,rules:Ue,label:`CPMK Code *`,variant:`outlined`,"prepend-inner-icon":`mdi-identifier`,disabled:d.value||q.value===`view`,placeholder:`e.g., CPMK-01`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ie,{modelValue:J.course_id,"onUpdate:modelValue":s[4]||=e=>J.course_id=e,items:Ve.value,rules:We,label:`Course *`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open-page-variant`,disabled:d.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ie,{modelValue:J.cognitive_level,"onUpdate:modelValue":s[5]||=e=>J.cognitive_level=e,items:He,rules:Ge,label:`Cognitive Level *`,variant:`outlined`,"prepend-inner-icon":`mdi-brain`,disabled:d.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`,md:`6`},{default:o(()=>[v(ae,{modelValue:J.weight_percentage,"onUpdate:modelValue":s[6]||=e=>J.weight_percentage=e,rules:Ke,label:`Weight Percentage *`,type:`number`,variant:`outlined`,"prepend-inner-icon":`mdi-scale-balance`,suffix:`%`,disabled:d.value||q.value===`view`,min:`0`,max:`100`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`},{default:o(()=>[v(ce,{modelValue:J.description,"onUpdate:modelValue":s[7]||=e=>J.description=e,rules:qe,label:`Description *`,variant:`outlined`,"prepend-inner-icon":`mdi-text`,rows:`3`,disabled:d.value||q.value===`view`,hint:`Brief description of the learning outcome`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`},{default:o(()=>[v(ce,{modelValue:J.learning_outcome,"onUpdate:modelValue":s[8]||=e=>J.learning_outcome=e,rules:Je,label:`Learning Outcome Statement *`,variant:`outlined`,"prepend-inner-icon":`mdi-target`,rows:`4`,disabled:d.value||q.value===`view`,hint:`Detailed, measurable learning outcome statement`},null,8,[`modelValue`,`disabled`])]),_:1}),v(r,{cols:`12`,md:`6`},{default:o(()=>[v(e,{modelValue:J.is_active,"onUpdate:modelValue":s[9]||=e=>J.is_active=e,label:`Active`,color:`primary`,disabled:d.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`icon`,`mode`,`loading`]),v(A,{modelValue:M.value,"onUpdate:modelValue":s[13]||=e=>M.value=e,"max-width":`1200`,scrollable:``},{default:o(()=>[v(E,null,{default:o(()=>[v(O,{class:`d-flex align-center justify-space-between`},{default:o(()=>[p(`div`,Ae,[v(a,{class:`mr-2`},{default:o(()=>s[52]||=[_(`mdi-format-list-bulleted`,-1)]),_:1,__:[52]}),p(`span`,je,`Sub-CPMK for `+u(G.value?.code),1)]),v(C,{icon:``,variant:`text`,onClick:s[11]||=e=>M.value=!1},{default:o(()=>[v(a,null,{default:o(()=>s[53]||=[_(`mdi-close`,-1)]),_:1,__:[53]})]),_:1})]),_:1}),v(S),v(D,null,{default:o(()=>[G.value?(b(),m(W,{key:0,cpmk:G.value,onClose:s[12]||=e=>M.value=!1},null,8,[`cpmk`])):h(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),v(A,{modelValue:N.value,"onUpdate:modelValue":s[16]||=e=>N.value=e,"max-width":`1000`,scrollable:``},{default:o(()=>[v(E,null,{default:o(()=>[v(O,{class:`d-flex align-center justify-space-between`},{default:o(()=>[p(`div`,Me,[v(a,{class:`mr-2`},{default:o(()=>s[54]||=[_(`mdi-vector-link`,-1)]),_:1,__:[54]}),p(`span`,Ne,`CPL Relations for `+u(G.value?.code),1)]),v(C,{icon:``,variant:`text`,onClick:s[14]||=e=>N.value=!1},{default:o(()=>[v(a,null,{default:o(()=>s[55]||=[_(`mdi-close`,-1)]),_:1,__:[55]})]),_:1})]),_:1}),v(S),v(D,null,{default:o(()=>[G.value?(b(),m(Y,{key:0,cpmk:G.value,onClose:s[15]||=e=>N.value=!1},null,8,[`cpmk`])):h(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),v(A,{modelValue:P.value,"onUpdate:modelValue":s[19]||=e=>P.value=e,"max-width":`800`},{default:o(()=>[v(E,null,{default:o(()=>[v(O,{class:`d-flex align-center justify-space-between`},{default:o(()=>[p(`div`,Pe,[v(a,{class:`mr-2`},{default:o(()=>s[56]||=[_(`mdi-scale-balance`,-1)]),_:1,__:[56]}),s[57]||=p(`span`,{class:`text-h6 font-weight-bold`},`Weight Validation`,-1)]),v(C,{icon:``,variant:`text`,onClick:s[17]||=e=>P.value=!1},{default:o(()=>[v(a,null,{default:o(()=>s[58]||=[_(`mdi-close`,-1)]),_:1,__:[58]})]),_:1})]),_:1}),v(S),v(D,null,{default:o(()=>[K.value?(b(),m(le,{key:0,"course-id":K.value,onClose:s[18]||=e=>P.value=!1},null,8,[`course-id`])):h(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),v(A,{modelValue:F.value,"onUpdate:modelValue":s[22]||=e=>F.value=e,"max-width":`1400`,scrollable:``},{default:o(()=>[v(E,null,{default:o(()=>[v(O,{class:`d-flex align-center justify-space-between`},{default:o(()=>[p(`div`,Fe,[v(a,{class:`mr-2`},{default:o(()=>s[59]||=[_(`mdi-vector-link`,-1)]),_:1,__:[59]}),s[60]||=p(`span`,{class:`text-h6 font-weight-bold`},`CPMK-CPL Mapping`,-1)]),v(C,{icon:``,variant:`text`,onClick:s[20]||=e=>F.value=!1},{default:o(()=>[v(a,null,{default:o(()=>s[61]||=[_(`mdi-close`,-1)]),_:1,__:[61]})]),_:1})]),_:1}),v(S),v(D,null,{default:o(()=>[K.value?(b(),m(pe,{key:0,"course-id":K.value,onClose:s[21]||=e=>F.value=!1},null,8,[`course-id`])):h(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),v(A,{modelValue:I.value,"onUpdate:modelValue":s[24]||=e=>I.value=e,"max-width":`400`},{default:o(()=>[v(E,null,{default:o(()=>[v(O,{class:`text-h6`},{default:o(()=>s[62]||=[_(`Confirm Delete`,-1)]),_:1,__:[62]}),v(D,null,{default:o(()=>[_(` Are you sure you want to delete CPMK "`+u(G.value?.code)+`"? This action cannot be undone and will also remove all sub-CPMK and CPL relations. `,1)]),_:1}),v(re,null,{default:o(()=>[v(ne),v(C,{onClick:s[23]||=e=>I.value=!1},{default:o(()=>s[63]||=[_(`Cancel`,-1)]),_:1,__:[63]}),v(C,{color:`error`,loading:y.value,onClick:ot},{default:o(()=>s[64]||=[_(` Delete `,-1)]),_:1,__:[64]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),v(se,{modelValue:L.value,"onUpdate:modelValue":s[26]||=e=>L.value=e,color:`success`,timeout:`3000`},{actions:o(()=>[v(C,{onClick:s[25]||=e=>L.value=!1},{default:o(()=>s[65]||=[_(`Close`,-1)]),_:1,__:[65]})]),default:o(()=>[_(u(z.value)+` `,1)]),_:1},8,[`modelValue`]),v(se,{modelValue:R.value,"onUpdate:modelValue":s[28]||=e=>R.value=e,color:`error`,timeout:`5000`},{actions:o(()=>[v(C,{onClick:s[27]||=e=>R.value=!1},{default:o(()=>s[66]||=[_(`Close`,-1)]),_:1,__:[66]})]),default:o(()=>[_(u(B.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),Le=Ie;export{Le as default};