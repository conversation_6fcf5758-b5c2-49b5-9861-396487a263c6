import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{I as n,b as r,bF as i,bg as a,bh as o,bl as s,bm as c,bn as l,bx as u,r as d,u as f}from"./index-BSnscBhv.js";const p={class:`error-container`},m={class:`mt-8`};var h=l({__name:`NotFoundView`,setup(r){return(r,l)=>(u(),o(d,{fluid:``,class:`fill-height`},{default:i(()=>[c(e,{align:`center`,justify:`center`,class:`fill-height`},{default:i(()=>[c(t,{cols:`12`,sm:`8`,md:`6`,class:`text-center`},{default:i(()=>[a(`div`,p,[l[6]||=a(`h1`,{class:`error-code`},`404`,-1),l[7]||=a(`h2`,{class:`error-title`},`Page Not Found`,-1),l[8]||=a(`p`,{class:`error-description`},` The page you're looking for doesn't exist or has been moved. `,-1),a(`div`,m,[c(f,{color:`primary`,size:`large`,onClick:l[0]||=e=>r.$router.push(`/dashboard`),class:`mr-4`},{default:i(()=>[c(n,{start:``},{default:i(()=>l[2]||=[s(`mdi-home`,-1)]),_:1,__:[2]}),l[3]||=s(` Go to Dashboard `,-1)]),_:1,__:[3]}),c(f,{variant:`outlined`,size:`large`,onClick:l[1]||=e=>r.$router.back()},{default:i(()=>[c(n,{start:``},{default:i(()=>l[4]||=[s(`mdi-arrow-left`,-1)]),_:1,__:[4]}),l[5]||=s(` Go Back `,-1)]),_:1,__:[5]})])])]),_:1})]),_:1})]),_:1}))}}),g=r(h,[[`__scopeId`,`data-v-9636b079`]]);export{g as default};