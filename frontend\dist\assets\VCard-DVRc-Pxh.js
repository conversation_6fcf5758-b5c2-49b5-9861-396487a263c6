import{A as e,B as t,C as n,D as r,F as i,G as a,I as o,Q as s,R as c,S as l,T as u,U as d,W as f,X as p,Y as m,Z as h,_ as g,a3 as _,a4 as v,a7 as y,a8 as b,a9 as x,aA as S,aC as C,aD as w,aG as T,aH as E,af as D,an as O,ao as k,ap as A,ax as j,bG as M,bQ as N,bR as P,bS as F,bd as I,bg as L,bm as R,bq as z,t as B,w as V,x as H,y as U,z as W}from"./index-BSnscBhv.js";const G=C()({name:`VCardActions`,props:T(),setup(e,t){let{slots:n}=t;return w({VBtn:{slim:!0,variant:`text`}}),j(()=>L(`div`,{class:N([`v-card-actions`,e.class]),style:P(e.style)},[n.default?.()])),{}}}),K=E({opacity:[Number,String],...T(),...D()},`VCardSubtitle`),q=C()({name:`VCardSubtitle`,props:K(),setup(e,t){let{slots:n}=t;return j(()=>R(e.tag,{class:N([`v-card-subtitle`,e.class]),style:P([{"--v-card-subtitle-opacity":e.opacity},e.style])},n)),{}}}),J=S(`v-card-title`),Y=E({appendAvatar:String,appendIcon:A,prependAvatar:String,prependIcon:A,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...T(),...u()},`VCardItem`),X=C()({name:`VCardItem`,props:Y(),setup(e,t){let{slots:n}=t;return j(()=>{let t=!!(e.prependAvatar||e.prependIcon),r=!!(t||n.prepend),i=!!(e.appendAvatar||e.appendIcon),a=!!(i||n.append),s=!!(e.title!=null||n.title),c=!!(e.subtitle!=null||n.subtitle);return L(`div`,{class:N([`v-card-item`,e.class]),style:P(e.style)},[r&&L(`div`,{key:`prepend`,class:`v-card-item__prepend`},[n.prepend?R(x,{key:`prepend-defaults`,disabled:!t,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon}}},n.prepend):L(I,null,[e.prependAvatar&&R(B,{key:`prepend-avatar`,density:e.density,image:e.prependAvatar},null),e.prependIcon&&R(o,{key:`prepend-icon`,density:e.density,icon:e.prependIcon},null)])]),L(`div`,{class:`v-card-item__content`},[s&&R(J,{key:`title`},{default:()=>[n.title?.()??F(e.title)]}),c&&R(q,{key:`subtitle`},{default:()=>[n.subtitle?.()??F(e.subtitle)]}),n.default?.()]),a&&L(`div`,{key:`append`,class:`v-card-item__append`},[n.append?R(x,{key:`append-defaults`,disabled:!i,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon}}},n.append):L(I,null,[e.appendIcon&&R(o,{key:`append-icon`,density:e.density,icon:e.appendIcon},null),e.appendAvatar&&R(B,{key:`append-avatar`,density:e.density,image:e.appendAvatar},null)])])])}),{}}}),Z=E({opacity:[Number,String],...T(),...D()},`VCardText`),Q=C()({name:`VCardText`,props:Z(),setup(e,t){let{slots:n}=t;return j(()=>R(e.tag,{class:N([`v-card-text`,e.class]),style:P([{"--v-card-text-opacity":e.opacity},e.style])},n)),{}}}),$=E({appendAvatar:String,appendIcon:A,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:A,ripple:{type:[Boolean,Object],default:!0},subtitle:{type:[String,Number,Boolean],default:void 0},text:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...m(),...T(),...u(),...y(),...f(),...n(),...i(),...W(),..._(),...H(),...D(),...O(),...c({variant:`elevated`})},`VCard`),ee=C()({name:`VCard`,directives:{vRipple:V},props:$(),setup(n,i){let{attrs:o,slots:c}=i,{themeClasses:u}=k(n),{borderClasses:f}=h(n),{colorClasses:m,colorStyles:_,variantClasses:y}=l(n),{densityClasses:S}=d(n),{dimensionStyles:C}=b(n),{elevationClasses:w}=p(n),{loaderClasses:T}=r(n),{locationStyles:E}=a(n),{positionClasses:D}=e(n),{roundedClasses:O}=v(n),A=U(n,o);return j(()=>{let e=n.link!==!1&&A.isLink.value,r=!n.disabled&&n.link!==!1&&(n.link||A.isClickable.value),i=e?`a`:n.tag,a=!!(c.title||n.title!=null),o=!!(c.subtitle||n.subtitle!=null),l=a||o,d=!!(c.append||n.appendAvatar||n.appendIcon),p=!!(c.prepend||n.prependAvatar||n.prependIcon),h=!!(c.image||n.image),v=l||p||d,b=!!(c.text||n.text!=null);return M(R(i,z({class:[`v-card`,{"v-card--disabled":n.disabled,"v-card--flat":n.flat,"v-card--hover":n.hover&&!(n.disabled||n.flat),"v-card--link":r},u.value,f.value,m.value,S.value,w.value,T.value,D.value,O.value,y.value,n.class],style:[_.value,C.value,E.value,n.style],onClick:r&&A.navigate,tabindex:n.disabled?-1:void 0},A.linkProps),{default:()=>[h&&L(`div`,{key:`image`,class:`v-card__image`},[c.image?R(x,{key:`image-defaults`,disabled:!n.image,defaults:{VImg:{cover:!0,src:n.image}}},c.image):R(g,{key:`image-img`,cover:!0,src:n.image},null)]),R(t,{name:`v-card`,active:!!n.loading,color:typeof n.loading==`boolean`?void 0:n.loading},{default:c.loader}),v&&R(X,{key:`item`,prependAvatar:n.prependAvatar,prependIcon:n.prependIcon,title:n.title,subtitle:n.subtitle,appendAvatar:n.appendAvatar,appendIcon:n.appendIcon},{default:c.item,prepend:c.prepend,title:c.title,subtitle:c.subtitle,append:c.append}),b&&R(Q,{key:`text`},{default:()=>[c.text?.()??n.text]}),c.default?.(),c.actions&&R(G,null,{default:c.actions}),s(r,`v-card`)]}),[[V,r&&n.ripple]])}),{}}});export{ee as b,Q as c,J as d,G as e};