# 🎓 RPS Management System

## 📖 Overview

RPS (Rencana Pembelajaran Semester) Management System adalah aplikasi web komprehensif yang dirancang untuk mengelola kurikulum, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan penilaian di perguruan tinggi. Sistem ini memungkinkan pengelolaan CPMK (Capaian Pembelajaran Mata Kuliah), CPL (Capaian Pembelajaran Lulusan), dan sistem penilaian yang terintegrasi.

## ✨ Key Features

### 🏛️ Academic Management
- **Faculty & Study Program Management**: Pengelolaan fakultas dan program studi
- **User Management**: Sistem manajemen pengguna dengan role-based access control
- **Academic Year Management**: Pengelolaan tahun akademik dan semester

### 📚 Course Management
- **Course CRUD Operations**: Pengelolaan mata kuliah lengkap
- **Course References**: Manajemen pustaka dan referensi mata kuliah
- **Course Topics**: Perencanaan pokok bahasan mingguan
- **Prerequisites Management**: Pengelolaan mata kuliah prasyarat

### 🎯 Learning Outcomes Management
- **CPL (Graduate Learning Outcomes)**: Pengelolaan capaian pembelajaran lulusan
- **CPMK (Course Learning Outcomes)**: Pengelolaan capaian pembelajaran mata kuliah
- **Sub-CPMK**: Pengelolaan sub-capaian pembelajaran
- **CPMK-CPL Mapping**: Pemetaan hubungan CPMK dengan CPL

### 📊 Assessment System
- **Assessment Methods**: Berbagai metode penilaian (tugas, kuis, UTS, UAS, dll)
- **Assessment Planning**: Perencanaan penilaian per CPMK
- **Rubric Management**: Pengelolaan rubrik penilaian
- **Achievement Tracking**: Pelacakan pencapaian CPMK dan CPL

### 📈 Analytics & Reporting
- **Dashboard Analytics**: Dashboard dengan visualisasi data
- **CPMK Achievement Reports**: Laporan pencapaian CPMK
- **CPL Mapping Reports**: Laporan pemetaan CPL
- **Performance Analytics**: Analisis kinerja pembelajaran

## 🏗️ System Architecture

### Backend Stack
- **Framework**: CodeIgniter 4 (PHP 8.1+)
- **Database**: PostgreSQL 14+
- **Authentication**: JWT-based authentication
- **API**: RESTful API with JSON responses
- **Caching**: Redis for session and query caching

### Frontend Stack
- **Framework**: Vue.js 3 with Composition API
- **UI Library**: Vuetify 3 (Material Design)
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **HTTP Client**: Axios
- **Build Tool**: Vite

### Development Environment
- **Containerization**: Docker & Docker Compose
- **Web Server**: Nginx (production), Apache (development)
- **Package Managers**: Composer (PHP), npm (Node.js)

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- PHP 8.1+
- Node.js 18+
- PostgreSQL 14+
- Redis

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/rps-management-system.git
   cd rps-management-system
   ```

2. **Setup Backend**
   ```bash
   cd backend
   composer install
   cp .env.example .env
   # Configure database credentials in .env
   php spark migrate
   php spark db:seed
   ```

3. **Setup Frontend**
   ```bash
   cd frontend
   npm install
   cp .env.example .env
   # Configure API endpoints in .env
   npm run dev
   ```

4. **Docker Setup (Alternative)**
   ```bash
   docker-compose up -d
   ```

### Default Login Credentials
- **Super Admin**: <EMAIL> / password123
- **Dosen**: <EMAIL> / password123
- **Mahasiswa**: <EMAIL> / password123

## 📁 Project Structure

```
rps-management-system/
├── backend/                    # CodeIgniter 4 Backend
│   ├── app/
│   │   ├── Controllers/        # API Controllers
│   │   ├── Models/            # Database Models
│   │   ├── Filters/           # Middleware/Filters
│   │   └── Config/            # Configuration files
│   ├── public/                # Public assets
│   └── tests/                 # Backend tests
├── frontend/                  # Vue.js Frontend
│   ├── src/
│   │   ├── components/        # Vue components
│   │   ├── views/            # Page views
│   │   ├── stores/           # Pinia stores
│   │   ├── router/           # Vue Router
│   │   └── services/         # API services
│   ├── public/               # Static assets
│   └── tests/                # Frontend tests
├── database/
│   ├── database_design.sql   # Complete database schema
│   └── migrations/           # Database migrations
├── docs/
│   ├── development_plan.md   # Development plan
│   ├── step_by_step_action_plan.md  # Action plan
│   └── api/                  # API documentation
├── docker-compose.yml        # Docker configuration
└── README.md                 # This file
```

## 🔧 Development

### Backend Development
```bash
cd backend
php spark serve  # Development server
php spark migrate  # Run migrations
php spark db:seed  # Seed database
vendor/bin/phpunit  # Run tests
```

### Frontend Development
```bash
cd frontend
npm run dev      # Development server
npm run build    # Production build
npm run test     # Run tests
npm run lint     # Code linting
```

### Database Management
```bash
# Create new migration
php spark make:migration CreateNewTable

# Create new seeder
php spark make:seeder NewTableSeeder

# Run specific migration
php spark migrate -g migration_name
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
vendor/bin/phpunit                    # Run all tests
vendor/bin/phpunit --coverage-html    # Generate coverage report
```

### Frontend Testing
```bash
cd frontend
npm run test:unit     # Unit tests
npm run test:e2e      # E2E tests
npm run test:coverage # Coverage report
```

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/v1/auth/login     # User login
POST /api/v1/auth/logout    # User logout
POST /api/v1/auth/refresh   # Refresh token
GET  /api/v1/auth/profile   # Get user profile
```

### Course Management Endpoints
```
GET    /api/v1/courses              # List courses
POST   /api/v1/courses              # Create course
GET    /api/v1/courses/{id}         # Get course details
PUT    /api/v1/courses/{id}         # Update course
DELETE /api/v1/courses/{id}         # Delete course
GET    /api/v1/courses/{id}/cpmk    # Get course CPMK
```

### CPMK Management Endpoints
```
GET    /api/v1/cpmk                 # List CPMK
POST   /api/v1/cpmk                 # Create CPMK
GET    /api/v1/cpmk/{id}            # Get CPMK details
PUT    /api/v1/cpmk/{id}            # Update CPMK
DELETE /api/v1/cpmk/{id}            # Delete CPMK
```

For complete API documentation, visit `/api/docs` when the application is running.

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Granular permission system
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Output encoding and CSP headers
- **CSRF Protection**: CSRF token validation
- **Rate Limiting**: API rate limiting
- **Audit Trail**: Complete system activity logging

## 🌐 Deployment

### Production Deployment
1. **Server Requirements**
   - Ubuntu 20.04+ or CentOS 8+
   - PHP 8.1+ with required extensions
   - PostgreSQL 14+
   - Nginx or Apache
   - Redis
   - SSL certificate

2. **Deployment Steps**
   ```bash
   # Clone and setup
   git clone https://github.com/your-org/rps-management-system.git
   cd rps-management-system
   
   # Backend setup
   cd backend
   composer install --no-dev --optimize-autoloader
   php spark migrate --env=production
   
   # Frontend setup
   cd ../frontend
   npm install
   npm run build
   
   # Configure web server
   # Setup SSL certificate
   # Configure database backup
   ```

### Docker Deployment
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PSR-12 coding standards for PHP
- Use ESLint and Prettier for JavaScript
- Write unit tests for new features
- Update documentation for API changes
- Follow semantic versioning

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Project Manager**: [Name]
- **Backend Developer**: [Name]
- **Frontend Developer**: [Name]
- **Database Administrator**: [Name]
- **UI/UX Designer**: [Name]
- **Quality Assurance**: [Name]

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [Wiki](https://github.com/your-org/rps-management-system/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/rps-management-system/issues)

## 🔄 Changelog

### Version 1.0.0 (2025-01-25)
- Initial release
- Complete RPS management functionality
- CPMK and CPL management
- Assessment system
- Analytics and reporting

---

**Built with ❤️ by Syahroni Wahyu Iriananda, S.Kom, MT**
