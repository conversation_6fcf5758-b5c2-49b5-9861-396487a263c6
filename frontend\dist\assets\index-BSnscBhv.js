const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/LoginView-BJxKou1C.js","assets/VCard-DVRc-Pxh.js","assets/VCard-DaK-lFqK.css","assets/VDialog-VHlGBbps.js","assets/VTextField-BU8lnKH2.js","assets/VTextField-CwHEo_Fa.css","assets/VDialog-p_rsguF8.css","assets/VForm-CDHrkI-n.js","assets/VRow-Cvqvybmt.js","assets/LoginView-CnL-y_dV.css","assets/DashboardView-D5UIV0lu.js","assets/VChip-CBN0Kf2u.js","assets/VChip-Chl2HXOf.css","assets/VTimeline-uQEqPSC6.js","assets/VTimeline-BsjaHvCP.css","assets/api-BWRuf0Vj.js","assets/DashboardView-B6sssODb.css","assets/UsersView-C-S8yKJ5.js","assets/VSwitch-Bh_Rc-In.js","assets/VSelect-DqM1bu6y.js","assets/VSelect-Bl27WBms.css","assets/FormModal-CAo97PhI.js","assets/FormModal-DwxgyMSp.css","assets/VSwitch-XVxpNmnu.css","assets/VSnackbar-KpoxlJmd.js","assets/VSnackbar-BHR6p3mn.css","assets/FacultiesView-BAvwq3Li.js","assets/StudyProgramsView-C36Uyo4w.js","assets/CoursesView-C7heoylY.js","assets/VTextarea-BciMMY-M.js","assets/VTextarea-9-wS2r-T.css","assets/CourseTopics-0mf0fzpJ.js","assets/CourseTopics-7x3FMJhO.css","assets/CourseDetailView-BuBillj2.js","assets/VTabs-Dp1ayKKb.js","assets/VTabs-Doua30Mb.css","assets/CourseDetailView-B7MfkJS8.css","assets/CPLView-45YpSa3z.js","assets/CPMKView-D8k2q2uK.js","assets/CPMKView-BCIxdvIa.css","assets/AssessmentsView-CA6HoVnu.js","assets/ReportsView-BdVqN3KQ.js","assets/ProfileView-oWoJGH0s.js","assets/ProfileView-CHpTn_LE.css","assets/NotFoundView-BT7fsKGJ.js","assets/NotFoundView-C_kMftAR.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,n)=>{for(var r in n)e(t,r,{get:n[r],enumerable:!0})};(function(){let e=document.createElement(`link`).relList;if(e&&e.supports&&e.supports(`modulepreload`))return;for(let e of document.querySelectorAll(`link[rel="modulepreload"]`))n(e);new MutationObserver(e=>{for(let t of e){if(t.type!==`childList`)continue;for(let e of t.addedNodes)e.tagName===`LINK`&&e.rel===`modulepreload`&&n(e)}}).observe(document,{childList:!0,subtree:!0});function t(e){let t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin===`use-credentials`?t.credentials=`include`:e.crossOrigin===`anonymous`?t.credentials=`omit`:t.credentials=`same-origin`,t}function n(e){if(e.ep)return;e.ep=!0;let n=t(e);fetch(e.href,n)}})();
/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function n(e){let t=Object.create(null);for(let n of e.split(`,`))t[n]=1;return e=>e in t}const r={},i=[],a=()=>{},o=()=>!1,s=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),c=e=>e.startsWith(`onUpdate:`),l=Object.assign,u=(e,t)=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,f=(e,t)=>d.call(e,t),p=Array.isArray,m=e=>S(e)===`[object Map]`,h=e=>S(e)===`[object Set]`,g=e=>typeof e==`function`,_=e=>typeof e==`string`,v=e=>typeof e==`symbol`,y=e=>typeof e==`object`&&!!e,b=e=>(y(e)||g(e))&&g(e.then)&&g(e.catch),x=Object.prototype.toString,S=e=>x.call(e),C=e=>S(e).slice(8,-1),w=e=>S(e)===`[object Object]`,T=e=>_(e)&&e!==`NaN`&&e[0]!==`-`&&``+parseInt(e,10)===e,E=n(`,key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted`),ee=e=>{let t=Object.create(null);return n=>{let r=t[n];return r||(t[n]=e(n))}},te=/-(\w)/g,D=ee(e=>e.replace(te,(e,t)=>t?t.toUpperCase():``)),ne=/\B([A-Z])/g,re=ee(e=>e.replace(ne,`-$1`).toLowerCase()),ie=ee(e=>e.charAt(0).toUpperCase()+e.slice(1)),ae=ee(e=>{let t=e?`on${ie(e)}`:``;return t}),oe=(e,t)=>!Object.is(e,t),se=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},O=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},ce=e=>{let t=parseFloat(e);return isNaN(t)?e:t},le=e=>{let t=_(e)?Number(e):NaN;return isNaN(t)?e:t};let ue;const de=()=>ue||=typeof globalThis<`u`?globalThis:typeof self<`u`?self:typeof window<`u`?window:typeof global<`u`?global:{};function k(e){if(p(e)){let t={};for(let n=0;n<e.length;n++){let r=e[n],i=_(r)?he(r):k(r);if(i)for(let e in i)t[e]=i[e]}return t}else if(_(e)||y(e))return e}const fe=/;(?![^(]*\))/g,pe=/:([^]+)/,me=/\/\*[^]*?\*\//g;function he(e){let t={};return e.replace(me,``).split(fe).forEach(e=>{if(e){let n=e.split(pe);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function A(e){let t=``;if(_(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){let r=A(e[n]);r&&(t+=r+` `)}else if(y(e))for(let n in e)e[n]&&(t+=n+` `);return t.trim()}const ge=`itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly`,_e=n(ge);ge+``;function ve(e){return!!e||e===``}const ye=e=>!!(e&&e.__v_isRef===!0),be=e=>_(e)?e:e==null?``:p(e)||y(e)&&(e.toString===x||!g(e.toString))?ye(e)?be(e.value):JSON.stringify(e,xe,2):String(e),xe=(e,t)=>ye(t)?xe(e,t.value):m(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],r)=>(e[Se(t,r)+` =>`]=n,e),{})}:h(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>Se(e))}:v(t)?Se(t):y(t)&&!p(t)&&!w(t)?String(t):t,Se=(e,t=``)=>{var n;return v(e)?`Symbol(${(n=e.description)??t})`:e};let Ce;var we=class{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ce,!e&&Ce&&(this.index=(Ce.scopes||=[]).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,t;if(this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,t;if(this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){let t=Ce;try{return Ce=this,e()}finally{Ce=t}}}on(){++this._on===1&&(this.prevScope=Ce,Ce=this)}off(){this._on>0&&--this._on===0&&(Ce=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){this._active=!1;let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}};function Te(e){return new we(e)}function Ee(){return Ce}function De(e,t=!1){Ce&&Ce.cleanups.push(e)}let j;const Oe=new WeakSet;var ke=class{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ce&&Ce.active&&Ce.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Oe.has(this)&&(Oe.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ne(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ke(this),Ie(this);let e=j,t=He;j=this,He=!0;try{return this.fn()}finally{Le(this),j=e,He=t,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)Be(e);this.deps=this.depsTail=void 0,Ke(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Oe.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Re(this)&&this.run()}get dirty(){return Re(this)}};let Ae=0,je,Me;function Ne(e,t=!1){if(e.flags|=8,t){e.next=Me,Me=e;return}e.next=je,je=e}function Pe(){Ae++}function Fe(){if(--Ae>0)return;if(Me){let e=Me;for(Me=void 0;e;){let t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;je;){let t=je;for(je=void 0;t;){let n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(t){e||=t}t=n}}if(e)throw e}function Ie(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Le(e){let t,n=e.depsTail,r=n;for(;r;){let e=r.prevDep;r.version===-1?(r===n&&(n=e),Be(r),Ve(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=e}e.deps=t,e.depsTail=n}function Re(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ze(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ze(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===qe)||(e.globalVersion=qe,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Re(e))))return;e.flags|=2;let t=e.dep,n=j,r=He;j=e,He=!0;try{Ie(e);let n=e.fn(e._value);(t.version===0||oe(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(e){throw t.version++,e}finally{j=n,He=r,Le(e),e.flags&=-3}}function Be(e,t=!1){let{dep:n,prevSub:r,nextSub:i}=e;if(r&&(r.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)Be(e,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ve(e){let{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let He=!0;const Ue=[];function We(){Ue.push(He),He=!1}function Ge(){let e=Ue.pop();He=e===void 0?!0:e}function Ke(e){let{cleanup:t}=e;if(e.cleanup=void 0,t){let e=j;j=void 0;try{t()}finally{j=e}}}let qe=0;var Je=class{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}},Ye=class{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!j||!He||j===this.computed)return;let t=this.activeLink;if(t===void 0||t.sub!==j)t=this.activeLink=new Je(j,this),j.deps?(t.prevDep=j.depsTail,j.depsTail.nextDep=t,j.depsTail=t):j.deps=j.depsTail=t,Xe(t);else if(t.version===-1&&(t.version=this.version,t.nextDep)){let e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=j.depsTail,t.nextDep=void 0,j.depsTail.nextDep=t,j.depsTail=t,j.deps===t&&(j.deps=e)}return t}trigger(e){this.version++,qe++,this.notify(e)}notify(e){Pe();try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{Fe()}}};function Xe(e){if(e.dep.sc++,e.sub.flags&4){let t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)Xe(e)}let n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ze=new WeakMap,Qe=Symbol(``),$e=Symbol(``),et=Symbol(``);function tt(e,t,n){if(He&&j){let t=Ze.get(e);t||Ze.set(e,t=new Map);let r=t.get(n);r||(t.set(n,r=new Ye),r.map=t,r.key=n),r.track()}}function nt(e,t,n,r,i,a){let o=Ze.get(e);if(!o){qe++;return}let s=e=>{e&&e.trigger()};if(Pe(),t===`clear`)o.forEach(s);else{let i=p(e),a=i&&T(n);if(i&&n===`length`){let e=Number(r);o.forEach((t,n)=>{(n===`length`||n===et||!v(n)&&n>=e)&&s(t)})}else switch((n!==void 0||o.has(void 0))&&s(o.get(n)),a&&s(o.get(et)),t){case`add`:i?a&&s(o.get(`length`)):(s(o.get(Qe)),m(e)&&s(o.get($e)));break;case`delete`:i||(s(o.get(Qe)),m(e)&&s(o.get($e)));break;case`set`:m(e)&&s(o.get(Qe));break}}Fe()}function rt(e,t){let n=Ze.get(e);return n&&n.get(t)}function it(e){let t=M(e);return t===e?t:(tt(t,`iterate`,et),Ut(e)?t:t.map(Kt))}function at(e){return tt(e=M(e),`iterate`,et),e}const ot={__proto__:null,[Symbol.iterator](){return st(this,Symbol.iterator,Kt)},concat(...e){return it(this).concat(...e.map(e=>p(e)?it(e):e))},entries(){return st(this,`entries`,e=>(e[1]=Kt(e[1]),e))},every(e,t){return lt(this,`every`,e,t,void 0,arguments)},filter(e,t){return lt(this,`filter`,e,t,e=>e.map(Kt),arguments)},find(e,t){return lt(this,`find`,e,t,Kt,arguments)},findIndex(e,t){return lt(this,`findIndex`,e,t,void 0,arguments)},findLast(e,t){return lt(this,`findLast`,e,t,Kt,arguments)},findLastIndex(e,t){return lt(this,`findLastIndex`,e,t,void 0,arguments)},forEach(e,t){return lt(this,`forEach`,e,t,void 0,arguments)},includes(...e){return dt(this,`includes`,e)},indexOf(...e){return dt(this,`indexOf`,e)},join(e){return it(this).join(e)},lastIndexOf(...e){return dt(this,`lastIndexOf`,e)},map(e,t){return lt(this,`map`,e,t,void 0,arguments)},pop(){return ft(this,`pop`)},push(...e){return ft(this,`push`,e)},reduce(e,...t){return ut(this,`reduce`,e,t)},reduceRight(e,...t){return ut(this,`reduceRight`,e,t)},shift(){return ft(this,`shift`)},some(e,t){return lt(this,`some`,e,t,void 0,arguments)},splice(...e){return ft(this,`splice`,e)},toReversed(){return it(this).toReversed()},toSorted(e){return it(this).toSorted(e)},toSpliced(...e){return it(this).toSpliced(...e)},unshift(...e){return ft(this,`unshift`,e)},values(){return st(this,`values`,Kt)}};function st(e,t,n){let r=at(e),i=r[t]();return r!==e&&!Ut(e)&&(i._next=i.next,i.next=()=>{let e=i._next();return e.value&&=n(e.value),e}),i}const ct=Array.prototype;function lt(e,t,n,r,i,a){let o=at(e),s=o!==e&&!Ut(e),c=o[t];if(c!==ct[t]){let t=c.apply(e,a);return s?Kt(t):t}let l=n;o!==e&&(s?l=function(t,r){return n.call(this,Kt(t),r,e)}:n.length>2&&(l=function(t,r){return n.call(this,t,r,e)}));let u=c.call(o,l,r);return s&&i?i(u):u}function ut(e,t,n,r){let i=at(e),a=n;return i!==e&&(Ut(e)?n.length>3&&(a=function(t,r,i){return n.call(this,t,r,i,e)}):a=function(t,r,i){return n.call(this,t,Kt(r),i,e)}),i[t](a,...r)}function dt(e,t,n){let r=M(e);tt(r,`iterate`,et);let i=r[t](...n);return(i===-1||i===!1)&&Wt(n[0])?(n[0]=M(n[0]),r[t](...n)):i}function ft(e,t,n=[]){We(),Pe();let r=M(e)[t].apply(e,n);return Fe(),Ge(),r}const pt=n(`__proto__,__v_isRef,__isVue`),mt=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!==`arguments`&&e!==`caller`).map(e=>Symbol[e]).filter(v));function ht(e){v(e)||(e=String(e));let t=M(this);return tt(t,`has`,e),t.hasOwnProperty(e)}var gt=class{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if(t===`__v_skip`)return e.__v_skip;let r=this._isReadonly,i=this._isShallow;if(t===`__v_isReactive`)return!r;if(t===`__v_isReadonly`)return r;if(t===`__v_isShallow`)return i;if(t===`__v_raw`)return n===(r?i?Pt:Nt:i?Mt:jt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;let a=p(e);if(!r){let e;if(a&&(e=ot[t]))return e;if(t===`hasOwnProperty`)return ht}let o=Reflect.get(e,t,N(e)?e:n);return(v(t)?mt.has(t):pt(t))||(r||tt(e,`get`,t),i)?o:N(o)?a&&T(t)?o:o.value:y(o)?r?zt(o):Lt(o):o}},_t=class extends gt{constructor(e=!1){super(!1,e)}set(e,t,n,r){let i=e[t];if(!this._isShallow){let t=Ht(i);if(!Ut(n)&&!Ht(n)&&(i=M(i),n=M(n)),!p(e)&&N(i)&&!N(n))return t?!1:(i.value=n,!0)}let a=p(e)&&T(t)?Number(t)<e.length:f(e,t),o=Reflect.set(e,t,n,N(e)?e:r);return e===M(r)&&(a?oe(n,i)&&nt(e,`set`,t,n,i):nt(e,`add`,t,n)),o}deleteProperty(e,t){let n=f(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&nt(e,`delete`,t,void 0,r),i}has(e,t){let n=Reflect.has(e,t);return(!v(t)||!mt.has(t))&&tt(e,`has`,t),n}ownKeys(e){return tt(e,`iterate`,p(e)?`length`:Qe),Reflect.ownKeys(e)}},vt=class extends gt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}};const yt=new _t,bt=new vt,xt=new _t(!0),St=e=>e,Ct=e=>Reflect.getPrototypeOf(e);function wt(e,t,n){return function(...r){let i=this.__v_raw,a=M(i),o=m(a),s=e===`entries`||e===Symbol.iterator&&o,c=e===`keys`&&o,l=i[e](...r),u=n?St:t?qt:Kt;return!t&&tt(a,`iterate`,c?$e:Qe),{next(){let{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Tt(e){return function(...t){return e===`delete`?!1:e===`clear`?void 0:this}}function Et(e,t){let n={get(n){let r=this.__v_raw,i=M(r),a=M(n);e||(oe(n,a)&&tt(i,`get`,n),tt(i,`get`,a));let{has:o}=Ct(i),s=t?St:e?qt:Kt;if(o.call(i,n))return s(r.get(n));if(o.call(i,a))return s(r.get(a));r!==i&&r.get(n)},get size(){let t=this.__v_raw;return!e&&tt(M(t),`iterate`,Qe),Reflect.get(t,`size`,t)},has(t){let n=this.__v_raw,r=M(n),i=M(t);return e||(oe(t,i)&&tt(r,`has`,t),tt(r,`has`,i)),t===i?n.has(t):n.has(t)||n.has(i)},forEach(n,r){let i=this,a=i.__v_raw,o=M(a),s=t?St:e?qt:Kt;return!e&&tt(o,`iterate`,Qe),a.forEach((e,t)=>n.call(r,s(e),s(t),i))}};l(n,e?{add:Tt(`add`),set:Tt(`set`),delete:Tt(`delete`),clear:Tt(`clear`)}:{add(e){!t&&!Ut(e)&&!Ht(e)&&(e=M(e));let n=M(this),r=Ct(n),i=r.has.call(n,e);return i||(n.add(e),nt(n,`add`,e,e)),this},set(e,n){!t&&!Ut(n)&&!Ht(n)&&(n=M(n));let r=M(this),{has:i,get:a}=Ct(r),o=i.call(r,e);o||(e=M(e),o=i.call(r,e));let s=a.call(r,e);return r.set(e,n),o?oe(n,s)&&nt(r,`set`,e,n,s):nt(r,`add`,e,n),this},delete(e){let t=M(this),{has:n,get:r}=Ct(t),i=n.call(t,e);i||(e=M(e),i=n.call(t,e));let a=r?r.call(t,e):void 0,o=t.delete(e);return i&&nt(t,`delete`,e,void 0,a),o},clear(){let e=M(this),t=e.size!==0,n,r=e.clear();return t&&nt(e,`clear`,void 0,void 0,n),r}});let r=[`keys`,`values`,`entries`,Symbol.iterator];return r.forEach(r=>{n[r]=wt(r,e,t)}),n}function Dt(e,t){let n=Et(e,t);return(t,r,i)=>r===`__v_isReactive`?!e:r===`__v_isReadonly`?e:r===`__v_raw`?t:Reflect.get(f(n,r)&&r in t?n:t,r,i)}const Ot={get:Dt(!1,!1)},kt={get:Dt(!1,!0)},At={get:Dt(!0,!1)},jt=new WeakMap,Mt=new WeakMap,Nt=new WeakMap,Pt=new WeakMap;function Ft(e){switch(e){case`Object`:case`Array`:return 1;case`Map`:case`Set`:case`WeakMap`:case`WeakSet`:return 2;default:return 0}}function It(e){return e.__v_skip||!Object.isExtensible(e)?0:Ft(C(e))}function Lt(e){return Ht(e)?e:Bt(e,!1,yt,Ot,jt)}function Rt(e){return Bt(e,!1,xt,kt,Mt)}function zt(e){return Bt(e,!0,bt,At,Nt)}function Bt(e,t,n,r,i){if(!y(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let a=It(e);if(a===0)return e;let o=i.get(e);if(o)return o;let s=new Proxy(e,a===2?r:n);return i.set(e,s),s}function Vt(e){return Ht(e)?Vt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ht(e){return!!(e&&e.__v_isReadonly)}function Ut(e){return!!(e&&e.__v_isShallow)}function Wt(e){return e?!!e.__v_raw:!1}function M(e){let t=e&&e.__v_raw;return t?M(t):e}function Gt(e){return!f(e,`__v_skip`)&&Object.isExtensible(e)&&O(e,`__v_skip`,!0),e}const Kt=e=>y(e)?Lt(e):e,qt=e=>y(e)?zt(e):e;function N(e){return e?e.__v_isRef===!0:!1}function P(e){return Jt(e,!1)}function F(e){return Jt(e,!0)}function Jt(e,t){return N(e)?e:new Yt(e,t)}var Yt=class{constructor(e,t){this.dep=new Ye,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:M(e),this._value=t?e:Kt(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,n=this.__v_isShallow||Ut(e)||Ht(e);e=n?e:M(e),oe(e,t)&&(this._rawValue=e,this._value=n?e:Kt(e),this.dep.trigger())}};function I(e){return N(e)?e.value:e}function Xt(e){return g(e)?e():I(e)}const Zt={get:(e,t,n)=>t===`__v_raw`?e:I(Reflect.get(e,t,n)),set:(e,t,n,r)=>{let i=e[t];return N(i)&&!N(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function Qt(e){return Vt(e)?e:new Proxy(e,Zt)}function $t(e){let t=p(e)?Array(e.length):{};for(let n in e)t[n]=nn(e,n);return t}var en=class{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=e===void 0?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return rt(M(this._object),this._key)}},tn=class{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}};function L(e,t,n){return N(e)?e:g(e)?new tn(e):y(e)&&arguments.length>1?nn(e,t,n):P(e)}function nn(e,t,n){let r=e[t];return N(r)?r:new en(e,t,n)}var rn=class{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Ye(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=qe-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&j!==this)return Ne(this,!0),!0}get value(){let e=this.dep.track();return ze(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}};function an(e,t,n=!1){let r,i;g(e)?r=e:(r=e.get,i=e.set);let a=new rn(r,i,n);return a}const on={},sn=new WeakMap;let cn;function ln(e,t=!1,n=cn){if(n){let t=sn.get(n);t||sn.set(n,t=[]),t.push(e)}}function un(e,t,n=r){let{immediate:i,deep:o,once:s,scheduler:c,augmentJob:l,call:d}=n,f=e=>o?e:Ut(e)||o===!1||o===0?dn(e,1):dn(e),m,h,_,v,y=!1,b=!1;if(N(e)?(h=()=>e.value,y=Ut(e)):Vt(e)?(h=()=>f(e),y=!0):p(e)?(b=!0,y=e.some(e=>Vt(e)||Ut(e)),h=()=>e.map(e=>{if(N(e))return e.value;if(Vt(e))return f(e);if(g(e))return d?d(e,2):e()})):h=g(e)?t?d?()=>d(e,2):e:()=>{if(_){We();try{_()}finally{Ge()}}let t=cn;cn=m;try{return d?d(e,3,[v]):e(v)}finally{cn=t}}:a,t&&o){let e=h,t=o===!0?1/0:o;h=()=>dn(e(),t)}let x=Ee(),S=()=>{m.stop(),x&&x.active&&u(x.effects,m)};if(s&&t){let e=t;t=(...t)=>{e(...t),S()}}let C=b?Array(e.length).fill(on):on,w=e=>{if(!(!(m.flags&1)||!m.dirty&&!e))if(t){let e=m.run();if(o||y||(b?e.some((e,t)=>oe(e,C[t])):oe(e,C))){_&&_();let n=cn;cn=m;try{let n=[e,C===on?void 0:b&&C[0]===on?[]:C,v];C=e,d?d(t,3,n):t(...n)}finally{cn=n}}}else m.run()};return l&&l(w),m=new ke(h),m.scheduler=c?()=>c(w,!1):w,v=e=>ln(e,!1,m),_=m.onStop=()=>{let e=sn.get(m);if(e){if(d)d(e,4);else for(let t of e)t();sn.delete(m)}},t?i?w(!0):C=m.run():c?c(w.bind(null,!0),!0):m.run(),S.pause=m.pause.bind(m),S.resume=m.resume.bind(m),S.stop=S,S}function dn(e,t=1/0,n){if(t<=0||!y(e)||e.__v_skip||(n||=new Set,n.has(e)))return e;if(n.add(e),t--,N(e))dn(e.value,t,n);else if(p(e))for(let r=0;r<e.length;r++)dn(e[r],t,n);else if(h(e)||m(e))e.forEach(e=>{dn(e,t,n)});else if(w(e)){for(let r in e)dn(e[r],t,n);for(let r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&dn(e[r],t,n)}return e}function fn(e,t,n,r){try{return r?e(...r):e()}catch(e){mn(e,t,n)}}function pn(e,t,n,r){if(g(e)){let i=fn(e,t,n,r);return i&&b(i)&&i.catch(e=>{mn(e,t,n)}),i}if(p(e)){let i=[];for(let a=0;a<e.length;a++)i.push(pn(e[a],t,n,r));return i}}function mn(e,t,n,i=!0){let a=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||r;if(t){let r=t.parent,i=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;r;){let t=r.ec;if(t){for(let n=0;n<t.length;n++)if(t[n](e,i,a)===!1)return}r=r.parent}if(o){We(),fn(o,null,10,[e,i,a]),Ge();return}}hn(e,n,a,i,s)}function hn(e,t,n,r=!0,i=!1){if(i)throw e;console.error(e)}const gn=[];let _n=-1;const vn=[];let yn=null,bn=0;const xn=Promise.resolve();let Sn=null;function Cn(e){let t=Sn||xn;return e?t.then(this?e.bind(this):e):t}function wn(e){let t=_n+1,n=gn.length;for(;t<n;){let r=t+n>>>1,i=gn[r],a=An(i);a<e||a===e&&i.flags&2?t=r+1:n=r}return t}function Tn(e){if(!(e.flags&1)){let t=An(e),n=gn[gn.length-1];!n||!(e.flags&2)&&t>=An(n)?gn.push(e):gn.splice(wn(t),0,e),e.flags|=1,En()}}function En(){Sn||=xn.then(jn)}function Dn(e){p(e)?vn.push(...e):yn&&e.id===-1?yn.splice(bn+1,0,e):e.flags&1||(vn.push(e),e.flags|=1),En()}function On(e,t,n=_n+1){for(;n<gn.length;n++){let t=gn[n];if(t&&t.flags&2){if(e&&t.id!==e.uid)continue;gn.splice(n,1),n--,t.flags&4&&(t.flags&=-2),t(),t.flags&4||(t.flags&=-2)}}}function kn(e){if(vn.length){let e=[...new Set(vn)].sort((e,t)=>An(e)-An(t));if(vn.length=0,yn){yn.push(...e);return}for(yn=e,bn=0;bn<yn.length;bn++){let e=yn[bn];e.flags&4&&(e.flags&=-2),e.flags&8||e(),e.flags&=-2}yn=null,bn=0}}const An=e=>e.id==null?e.flags&2?-1:1/0:e.id;function jn(e){try{for(_n=0;_n<gn.length;_n++){let e=gn[_n];e&&!(e.flags&8)&&(e.flags&4&&(e.flags&=-2),fn(e,e.i,e.i?15:14),e.flags&4||(e.flags&=-2))}}finally{for(;_n<gn.length;_n++){let e=gn[_n];e&&(e.flags&=-2)}_n=-1,gn.length=0,kn(e),Sn=null,(gn.length||vn.length)&&jn(e)}}let Mn=null,Nn=null;function Pn(e){let t=Mn;return Mn=e,Nn=e&&e.type.__scopeId||null,t}function R(e,t=Mn,n){if(!t||e._n)return e;let r=(...n)=>{r._d&&ha(-1);let i=Pn(t),a;try{a=e(...n)}finally{Pn(i),r._d&&ha(1)}return a};return r._n=!0,r._c=!0,r._d=!0,r}function Fn(e,t){if(Mn===null)return e;let n=Za(Mn),i=e.dirs||=[];for(let e=0;e<t.length;e++){let[a,o,s,c=r]=t[e];a&&(g(a)&&(a={mounted:a,updated:a}),a.deep&&dn(o),i.push({dir:a,instance:n,value:o,oldValue:void 0,arg:s,modifiers:c}))}return e}function In(e,t,n,r){let i=e.dirs,a=t&&t.dirs;for(let o=0;o<i.length;o++){let s=i[o];a&&(s.oldValue=a[o].value);let c=s.dir[r];c&&(We(),pn(c,n,8,[e.el,s,e,t]),Ge())}}const Ln=Symbol(`_vte`),Rn=e=>e.__isTeleport,zn=e=>e&&(e.disabled||e.disabled===``),Bn=e=>e&&(e.defer||e.defer===``),Vn=e=>typeof SVGElement<`u`&&e instanceof SVGElement,Hn=e=>typeof MathMLElement==`function`&&e instanceof MathMLElement,Un=(e,t)=>{let n=e&&e.to;if(_(n))if(t){let e=t(n);return e}else return null;else return n},Wn={name:`Teleport`,__isTeleport:!0,process(e,t,n,r,i,a,o,s,c,l){let{mc:u,pc:d,pbc:f,o:{insert:p,querySelector:m,createText:h,createComment:g}}=l,_=zn(t.props),{shapeFlag:v,children:y,dynamicChildren:b}=t;if(e==null){let e=t.el=h(``),l=t.anchor=h(``);p(e,n,r),p(l,n,r);let d=(e,t)=>{v&16&&(i&&i.isCE&&(i.ce._teleportTarget=e),u(y,e,t,i,a,o,s,c))},f=()=>{let e=t.target=Un(t.props,m),n=Yn(e,t,h,p);e&&(o!==`svg`&&Vn(e)?o=`svg`:o!==`mathml`&&Hn(e)&&(o=`mathml`),_||(d(e,n),Jn(t,!1)))};_&&(d(n,l),Jn(t,!0)),Bn(t.props)?(t.el.__isMounted=!1,Ni(()=>{f(),delete t.el.__isMounted},a)):f()}else{if(Bn(t.props)&&e.el.__isMounted===!1){Ni(()=>{Wn.process(e,t,n,r,i,a,o,s,c,l)},a);return}t.el=e.el,t.targetStart=e.targetStart;let u=t.anchor=e.anchor,p=t.target=e.target,h=t.targetAnchor=e.targetAnchor,g=zn(e.props),v=g?n:p,y=g?u:h;if(o===`svg`||Vn(p)?o=`svg`:(o===`mathml`||Hn(p))&&(o=`mathml`),b?(f(e.dynamicChildren,b,v,i,a,o,s),zi(e,t,!0)):c||d(e,t,v,y,i,a,o,s,!1),_)g?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Gn(t,n,u,l,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){let e=t.target=Un(t.props,m);e&&Gn(t,e,null,l,0)}else g&&Gn(t,p,h,l,1);Jn(t,_)}},remove(e,t,n,{um:r,o:{remove:i}},a){let{shapeFlag:o,children:s,anchor:c,targetStart:l,targetAnchor:u,target:d,props:f}=e;if(d&&(i(l),i(u)),a&&i(c),o&16){let e=a||!zn(f);for(let i=0;i<s.length;i++){let a=s[i];r(a,t,n,e,!!a.dynamicChildren)}}},move:Gn,hydrate:Kn};function Gn(e,t,n,{o:{insert:r},m:i},a=2){a===0&&r(e.targetAnchor,t,n);let{el:o,anchor:s,shapeFlag:c,children:l,props:u}=e,d=a===2;if(d&&r(o,t,n),(!d||zn(u))&&c&16)for(let e=0;e<l.length;e++)i(l[e],t,n,2);d&&r(s,t,n)}function Kn(e,t,n,r,i,a,{o:{nextSibling:o,parentNode:s,querySelector:c,insert:l,createText:u}},d){let f=t.target=Un(t.props,c);if(f){let c=zn(t.props),p=f._lpa||f.firstChild;if(t.shapeFlag&16)if(c)t.anchor=d(o(e),t,s(e),n,r,i,a),t.targetStart=p,t.targetAnchor=p&&o(p);else{t.anchor=o(e);let s=p;for(;s;){if(s&&s.nodeType===8){if(s.data===`teleport start anchor`)t.targetStart=s;else if(s.data===`teleport anchor`){t.targetAnchor=s,f._lpa=t.targetAnchor&&o(t.targetAnchor);break}}s=o(s)}t.targetAnchor||Yn(f,t,u,l),d(p&&o(p),t,f,n,r,i,a)}Jn(t,c)}return t.anchor&&o(t.anchor)}const qn=Wn;function Jn(e,t){let n=e.ctx;if(n&&n.ut){let r,i;for(t?(r=e.el,i=e.anchor):(r=e.targetStart,i=e.targetAnchor);r&&r!==i;)r.nodeType===1&&r.setAttribute(`data-v-owner`,n.uid),r=r.nextSibling;n.ut()}}function Yn(e,t,n,r){let i=t.targetStart=n(``),a=t.targetAnchor=n(``);return i[Ln]=a,e&&(r(i,e),r(a,e)),a}const Xn=Symbol(`_leaveCb`),Zn=Symbol(`_enterCb`);function Qn(){let e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return wr(()=>{e.isMounted=!0}),Dr(()=>{e.isUnmounting=!0}),e}const $n=[Function,Array],er={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:$n,onEnter:$n,onAfterEnter:$n,onEnterCancelled:$n,onBeforeLeave:$n,onLeave:$n,onAfterLeave:$n,onLeaveCancelled:$n,onBeforeAppear:$n,onAppear:$n,onAfterAppear:$n,onAppearCancelled:$n},tr=e=>{let t=e.subTree;return t.component?tr(t.component):t},nr={name:`BaseTransition`,props:er,setup(e,{slots:t}){let n=Ia(),r=Qn();return()=>{let i=t.default&&ur(t.default(),!0);if(!i||!i.length)return;let a=rr(i),o=M(e),{mode:s}=o;if(r.isLeaving)return sr(a);let c=cr(a);if(!c)return sr(a);let l=or(c,o,r,n,e=>l=e);c.type!==ca&&lr(c,l);let u=n.subTree&&cr(n.subTree);if(u&&u.type!==ca&&!ba(c,u)&&tr(n).type!==ca){let e=or(u,o,r,n);if(lr(u,e),s===`out-in`&&c.type!==ca)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete e.afterLeave,u=void 0},sr(a);s===`in-out`&&c.type!==ca?e.delayLeave=(e,t,n)=>{let i=ar(r,u);i[String(u.key)]=u,e[Xn]=()=>{t(),e[Xn]=void 0,delete l.delayedLeave,u=void 0},l.delayedLeave=()=>{n(),delete l.delayedLeave,u=void 0}}:u=void 0}else u&&=void 0;return a}}};function rr(e){let t=e[0];if(e.length>1){let n=!1;for(let r of e)if(r.type!==ca){t=r,n=!0;break}}return t}const ir=nr;function ar(e,t){let{leavingVNodes:n}=e,r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function or(e,t,n,r,i){let{appear:a,mode:o,persisted:s=!1,onBeforeEnter:c,onEnter:l,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:f,onLeave:m,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:_,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,x=String(e.key),S=ar(n,e),C=(e,t)=>{e&&pn(e,r,9,t)},w=(e,t)=>{let n=t[1];C(e,t),p(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},T={mode:o,persisted:s,beforeEnter(t){let r=c;if(!n.isMounted)if(a)r=_||c;else return;t[Xn]&&t[Xn](!0);let i=S[x];i&&ba(e,i)&&i.el[Xn]&&i.el[Xn](),C(r,[t])},enter(e){let t=l,r=u,i=d;if(!n.isMounted)if(a)t=v||l,r=y||u,i=b||d;else return;let o=!1,s=e[Zn]=t=>{o||(o=!0,C(t?i:r,[e]),T.delayedLeave&&T.delayedLeave(),e[Zn]=void 0)};t?w(t,[e,s]):s()},leave(t,r){let i=String(e.key);if(t[Zn]&&t[Zn](!0),n.isUnmounting)return r();C(f,[t]);let a=!1,o=t[Xn]=n=>{a||(a=!0,r(),C(n?g:h,[t]),t[Xn]=void 0,S[i]===e&&delete S[i])};S[i]=e,m?w(m,[t,o]):o()},clone(e){let a=or(e,t,n,r,i);return i&&i(a),a}};return T}function sr(e){if(gr(e))return e=Ta(e),e.children=null,e}function cr(e){if(!gr(e))return Rn(e.type)&&e.children?rr(e.children):e;if(e.component)return e.component.subTree;let{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&g(n.default))return n.default()}}function lr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,lr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ur(e,t=!1,n){let r=[],i=0;for(let a=0;a<e.length;a++){let o=e[a],s=n==null?o.key:String(n)+String(o.key==null?a:o.key);o.type===V?(o.patchFlag&128&&i++,r=r.concat(ur(o.children,t,s))):(t||o.type!==ca)&&r.push(s==null?o:Ta(o,{key:s}))}if(i>1)for(let e=0;e<r.length;e++)r[e].patchFlag=-2;return r}
/*! #__NO_SIDE_EFFECTS__ */
function dr(e,t){return g(e)?(()=>l({name:e.name},t,{setup:e}))():e}function fr(){let e=Ia();return e?(e.appContext.config.idPrefix||`v`)+`-`+e.ids[0]+ e.ids[1]++:``}function pr(e){e.ids=[e.ids[0]+ e.ids[2]+++`-`,0,0]}function mr(e,t,n,i,a=!1){if(p(e)){e.forEach((e,r)=>mr(e,t&&(p(t)?t[r]:t),n,i,a));return}if(hr(i)&&!a){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&mr(e,t,n,i.component.subTree);return}let o=i.shapeFlag&4?Za(i.component):i.el,s=a?null:o,{i:c,r:l}=e,d=t&&t.r,m=c.refs===r?c.refs={}:c.refs,h=c.setupState,v=M(h),y=h===r?()=>!1:e=>f(v,e);if(d!=null&&d!==l&&(_(d)?(m[d]=null,y(d)&&(h[d]=null)):N(d)&&(d.value=null)),g(l))fn(l,c,12,[s,m]);else{let t=_(l),r=N(l);if(t||r){let i=()=>{if(e.f){let n=t?y(l)?h[l]:m[l]:l.value;a?p(n)&&u(n,o):p(n)?n.includes(o)||n.push(o):t?(m[l]=[o],y(l)&&(h[l]=m[l])):(l.value=[o],e.k&&(m[e.k]=l.value))}else t?(m[l]=s,y(l)&&(h[l]=s)):r&&(l.value=s,e.k&&(m[e.k]=s))};s?(i.id=-1,Ni(i,n)):i()}}}de().requestIdleCallback,de().cancelIdleCallback;const hr=e=>!!e.type.__asyncLoader,gr=e=>e.type.__isKeepAlive;function _r(e,t){yr(e,`a`,t)}function vr(e,t){yr(e,`da`,t)}function yr(e,t,n=Fa){let r=e.__wdc||=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()};if(xr(t,r,n),n){let e=n.parent;for(;e&&e.parent;)gr(e.parent.vnode)&&br(r,t,n,e),e=e.parent}}function br(e,t,n,r){let i=xr(t,e,r,!0);Or(()=>{u(r[t],i)},n)}function xr(e,t,n=Fa,r=!1){if(n){let i=n[e]||(n[e]=[]),a=t.__weh||=(...r)=>{We();let i=za(n),a=pn(t,n,e,r);return i(),Ge(),a};return r?i.unshift(a):i.push(a),a}}const Sr=e=>(t,n=Fa)=>{(!Ha||e===`sp`)&&xr(e,(...e)=>t(...e),n)},Cr=Sr(`bm`),wr=Sr(`m`),Tr=Sr(`bu`),Er=Sr(`u`),Dr=Sr(`bum`),Or=Sr(`um`),kr=Sr(`sp`),Ar=Sr(`rtg`),jr=Sr(`rtc`);function Mr(e,t=Fa){xr(`ec`,e,t)}const Nr=`components`;function Pr(e,t){return Lr(Nr,e,!0,t)||e}const Fr=Symbol.for(`v-ndc`);function Ir(e){return _(e)?Lr(Nr,e,!1)||e:e||Fr}function Lr(e,t,n=!0,r=!1){let i=Mn||Fa;if(i){let n=i.type;if(e===Nr){let e=Qa(n,!1);if(e&&(e===t||e===D(t)||e===ie(D(t))))return n}let a=Rr(i[e]||n[e],t)||Rr(i.appContext[e],t);return!a&&r?n:a}}function Rr(e,t){return e&&(e[t]||e[D(t)]||e[ie(D(t))])}function zr(e,t,n,r){let i,a=n&&n[r],o=p(e);if(o||_(e)){let n=o&&Vt(e),r=!1,s=!1;n&&(r=!Ut(e),s=Ht(e),e=at(e)),i=Array(e.length);for(let n=0,o=e.length;n<o;n++)i[n]=t(r?s?qt(Kt(e[n])):Kt(e[n]):e[n],n,void 0,a&&a[n])}else if(typeof e==`number`){i=Array(e);for(let n=0;n<e;n++)i[n]=t(n+1,n,void 0,a&&a[n])}else if(y(e))if(e[Symbol.iterator])i=Array.from(e,(e,n)=>t(e,n,void 0,a&&a[n]));else{let n=Object.keys(e);i=Array(n.length);for(let r=0,o=n.length;r<o;r++){let o=n[r];i[r]=t(e[o],o,r,a&&a[r])}}else i=[];return n&&(n[r]=i),i}function Br(e,t){for(let n=0;n<t.length;n++){let r=t[n];if(p(r))for(let t=0;t<r.length;t++)e[r[t].name]=r[t].fn;else r&&(e[r.name]=r.key?(...e)=>{let t=r.fn(...e);return t&&(t.key=r.key),t}:r.fn)}return e}function Vr(e,t,n={},r,i){if(Mn.ce||Mn.parent&&hr(Mn.parent)&&Mn.parent.ce)return t!==`default`&&(n.name=t),fa(),va(V,null,[U(`slot`,n,r&&r())],64);let a=e[t];a&&a._c&&(a._d=!1),fa();let o=a&&Hr(a(n)),s=n.key||o&&o.key,c=va(V,{key:(s&&!v(s)?s:`_${t}`)+(!o&&r?`_fb`:``)},o||(r?r():[]),o&&e._===1?64:-2);return!i&&c.scopeId&&(c.slotScopeIds=[c.scopeId+`-s`]),a&&a._c&&(a._d=!0),c}function Hr(e){return e.some(e=>ya(e)?!(e.type===ca||e.type===V&&!Hr(e.children)):!0)?e:null}const Ur=e=>e?Va(e)?Za(e):Ur(e.parent):null,Wr=l(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ur(e.parent),$root:e=>Ur(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>$r(e),$forceUpdate:e=>e.f||=()=>{Tn(e.update)},$nextTick:e=>e.n||=Cn.bind(e.proxy),$watch:e=>qi.bind(e)}),Gr=(e,t)=>e!==r&&!e.__isScriptSetup&&f(e,t),Kr={get({_:e},t){if(t===`__v_skip`)return!0;let{ctx:n,setupState:i,data:a,props:o,accessCache:s,type:c,appContext:l}=e,u;if(t[0]!==`$`){let c=s[t];if(c!==void 0)switch(c){case 1:return i[t];case 2:return a[t];case 4:return n[t];case 3:return o[t]}else if(Gr(i,t))return s[t]=1,i[t];else if(a!==r&&f(a,t))return s[t]=2,a[t];else if((u=e.propsOptions[0])&&f(u,t))return s[t]=3,o[t];else if(n!==r&&f(n,t))return s[t]=4,n[t];else Jr&&(s[t]=0)}let d=Wr[t],p,m;if(d)return t===`$attrs`&&tt(e.attrs,`get`,``),d(e);if((p=c.__cssModules)&&(p=p[t]))return p;if(n!==r&&f(n,t))return s[t]=4,n[t];if(m=l.config.globalProperties,f(m,t))return m[t]},set({_:e},t,n){let{data:i,setupState:a,ctx:o}=e;return Gr(a,t)?(a[t]=n,!0):i!==r&&f(i,t)?(i[t]=n,!0):f(e.props,t)||t[0]===`$`&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:i,appContext:a,propsOptions:o}},s){let c;return!!n[s]||e!==r&&f(e,s)||Gr(t,s)||(c=o[0])&&f(c,s)||f(i,s)||f(Wr,s)||f(a.config.globalProperties,s)},defineProperty(e,t,n){return n.get==null?f(n,`value`)&&this.set(e,t,n.value,null):e._.accessCache[t]=0,Reflect.defineProperty(e,t,n)}};function qr(e){return p(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}let Jr=!0;function Yr(e){let t=$r(e),n=e.proxy,r=e.ctx;Jr=!1,t.beforeCreate&&Zr(t.beforeCreate,e,`bc`);let{data:i,computed:o,methods:s,watch:c,provide:l,inject:u,created:d,beforeMount:f,mounted:m,beforeUpdate:h,updated:_,activated:v,deactivated:b,beforeDestroy:x,beforeUnmount:S,destroyed:C,unmounted:w,render:T,renderTracked:E,renderTriggered:ee,errorCaptured:te,serverPrefetch:D,expose:ne,inheritAttrs:re,components:ie,directives:ae,filters:oe}=t,se=null;if(u&&Xr(u,r,se),s)for(let e in s){let t=s[e];g(t)&&(r[e]=t.bind(n))}if(i){let t=i.call(n,n);y(t)&&(e.data=Lt(t))}if(Jr=!0,o)for(let e in o){let t=o[e],i=g(t)?t.bind(n,n):g(t.get)?t.get.bind(n,n):a,s=!g(t)&&g(t.set)?t.set.bind(n):a,c=G({get:i,set:s});Object.defineProperty(r,e,{enumerable:!0,configurable:!0,get:()=>c.value,set:e=>c.value=e})}if(c)for(let e in c)Qr(c[e],r,n,e);if(l){let e=g(l)?l.call(n):l;Reflect.ownKeys(e).forEach(t=>{pi(t,e[t])})}d&&Zr(d,e,`c`);function O(e,t){p(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(O(Cr,f),O(wr,m),O(Tr,h),O(Er,_),O(_r,v),O(vr,b),O(Mr,te),O(jr,E),O(Ar,ee),O(Dr,S),O(Or,w),O(kr,D),p(ne))if(ne.length){let t=e.exposed||={};ne.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t,enumerable:!0})})}else e.exposed||={};T&&e.render===a&&(e.render=T),re!=null&&(e.inheritAttrs=re),ie&&(e.components=ie),ae&&(e.directives=ae),D&&pr(e)}function Xr(e,t,n=a){for(let n in p(e)&&(e=ii(e)),e){let r=e[n],i;i=y(r)?`default`in r?z(r.from||n,r.default,!0):z(r.from||n):z(r),N(i)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[n]=i}}function Zr(e,t,n){pn(p(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function Qr(e,t,n,r){let i=r.includes(`.`)?Ji(n,r):()=>n[r];if(_(e)){let n=t[e];g(n)&&B(i,n)}else if(g(e))B(i,e.bind(n));else if(y(e))if(p(e))e.forEach(e=>Qr(e,t,n,r));else{let r=g(e.handler)?e.handler.bind(n):t[e.handler];g(r)&&B(i,r,e)}}function $r(e){let t=e.type,{mixins:n,extends:r}=t,{mixins:i,optionsCache:a,config:{optionMergeStrategies:o}}=e.appContext,s=a.get(t),c;return s?c=s:!i.length&&!n&&!r?c=t:(c={},i.length&&i.forEach(e=>ei(c,e,o,!0)),ei(c,t,o)),y(t)&&a.set(t,c),c}function ei(e,t,n,r=!1){let{mixins:i,extends:a}=t;for(let o in a&&ei(e,a,n,!0),i&&i.forEach(t=>ei(e,t,n,!0)),t)if(!(r&&o===`expose`)){let r=ti[o]||n&&n[o];e[o]=r?r(e[o],t[o]):t[o]}return e}const ti={data:ni,props:si,emits:si,methods:oi,computed:oi,beforeCreate:ai,created:ai,beforeMount:ai,mounted:ai,beforeUpdate:ai,updated:ai,beforeDestroy:ai,beforeUnmount:ai,destroyed:ai,unmounted:ai,activated:ai,deactivated:ai,errorCaptured:ai,serverPrefetch:ai,components:oi,directives:oi,watch:ci,provide:ni,inject:ri};function ni(e,t){return t?e?function(){return l(g(e)?e.call(this,this):e,g(t)?t.call(this,this):t)}:t:e}function ri(e,t){return oi(ii(e),ii(t))}function ii(e){if(p(e)){let t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ai(e,t){return e?[...new Set([].concat(e,t))]:t}function oi(e,t){return e?l(Object.create(null),e,t):t}function si(e,t){return e?p(e)&&p(t)?[...new Set([...e,...t])]:l(Object.create(null),qr(e),qr(t??{})):t}function ci(e,t){if(!e)return t;if(!t)return e;let n=l(Object.create(null),e);for(let r in t)n[r]=ai(e[r],t[r]);return n}function li(){return{app:null,config:{isNativeTag:o,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ui=0;function di(e,t){return function(n,r=null){g(n)||(n=l({},n)),r!=null&&!y(r)&&(r=null);let i=li(),a=new WeakSet,o=[],s=!1,c=i.app={_uid:ui++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:to,get config(){return i.config},set config(e){},use(e,...t){return a.has(e)||(e&&g(e.install)?(a.add(e),e.install(c,...t)):g(e)&&(a.add(e),e(c,...t))),c},mixin(e){return i.mixins.includes(e)||i.mixins.push(e),c},component(e,t){return t?(i.components[e]=t,c):i.components[e]},directive(e,t){return t?(i.directives[e]=t,c):i.directives[e]},mount(a,o,l){if(!s){let u=c._ceVNode||U(n,r);return u.appContext=i,l===!0?l=`svg`:l===!1&&(l=void 0),o&&t?t(u,a):e(u,a,l),s=!0,c._container=a,a.__vue_app__=c,Za(u.component)}},onUnmount(e){o.push(e)},unmount(){s&&(pn(o,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(e,t){return i.provides[e]=t,c},runWithContext(e){let t=fi;fi=c;try{return e()}finally{fi=t}}};return c}}let fi=null;function pi(e,t){if(Fa){let n=Fa.provides,r=Fa.parent&&Fa.parent.provides;r===n&&(n=Fa.provides=Object.create(r)),n[e]=t}}function z(e,t,n=!1){let r=Ia();if(r||fi){let i=fi?fi._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&g(t)?t.call(r&&r.proxy):t}}function mi(){return!!(Ia()||fi)}const hi={},gi=()=>Object.create(hi),_i=e=>Object.getPrototypeOf(e)===hi;function vi(e,t,n,r=!1){let i={},a=gi();for(let n in e.propsDefaults=Object.create(null),bi(e,t,i,a),e.propsOptions[0])n in i||(i[n]=void 0);n?e.props=r?i:Rt(i):e.type.props?e.props=i:e.props=a,e.attrs=a}function yi(e,t,n,r){let{props:i,attrs:a,vnode:{patchFlag:o}}=e,s=M(i),[c]=e.propsOptions,l=!1;if((r||o>0)&&!(o&16)){if(o&8){let n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let o=n[r];if(Qi(e.emitsOptions,o))continue;let u=t[o];if(c)if(f(a,o))u!==a[o]&&(a[o]=u,l=!0);else{let t=D(o);i[t]=xi(c,s,t,u,e,!1)}else u!==a[o]&&(a[o]=u,l=!0)}}}else{bi(e,t,i,a)&&(l=!0);let r;for(let a in s)(!t||!f(t,a)&&((r=re(a))===a||!f(t,r)))&&(c?n&&(n[a]!==void 0||n[r]!==void 0)&&(i[a]=xi(c,s,a,void 0,e,!0)):delete i[a]);if(a!==s)for(let e in a)(!t||!f(t,e))&&(delete a[e],l=!0)}l&&nt(e.attrs,`set`,``)}function bi(e,t,n,i){let[a,o]=e.propsOptions,s=!1,c;if(t)for(let r in t){if(E(r))continue;let l=t[r],u;a&&f(a,u=D(r))?!o||!o.includes(u)?n[u]=l:(c||={})[u]=l:Qi(e.emitsOptions,r)||(!(r in i)||l!==i[r])&&(i[r]=l,s=!0)}if(o){let t=M(n),i=c||r;for(let r=0;r<o.length;r++){let s=o[r];n[s]=xi(a,t,s,i[s],e,!f(i,s))}}return s}function xi(e,t,n,r,i,a){let o=e[n];if(o!=null){let e=f(o,`default`);if(e&&r===void 0){let e=o.default;if(o.type!==Function&&!o.skipFactory&&g(e)){let{propsDefaults:a}=i;if(n in a)r=a[n];else{let o=za(i);r=a[n]=e.call(null,t),o()}}else r=e;i.ce&&i.ce._setProp(n,r)}o[0]&&(a&&!e?r=!1:o[1]&&(r===``||r===re(n))&&(r=!0))}return r}const Si=new WeakMap;function Ci(e,t,n=!1){let a=n?Si:t.propsCache,o=a.get(e);if(o)return o;let s=e.props,c={},u=[],d=!1;if(!g(e)){let r=e=>{d=!0;let[n,r]=Ci(e,t,!0);l(c,n),r&&u.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}if(!s&&!d)return y(e)&&a.set(e,i),i;if(p(s))for(let e=0;e<s.length;e++){let t=D(s[e]);wi(t)&&(c[t]=r)}else if(s)for(let e in s){let t=D(e);if(wi(t)){let n=s[e],r=c[t]=p(n)||g(n)?{type:n}:l({},n),i=r.type,a=!1,o=!0;if(p(i))for(let e=0;e<i.length;++e){let t=i[e],n=g(t)&&t.name;if(n===`Boolean`){a=!0;break}else n===`String`&&(o=!1)}else a=g(i)&&i.name===`Boolean`;r[0]=a,r[1]=o,(a||f(r,`default`))&&u.push(t)}}let m=[c,u];return y(e)&&a.set(e,m),m}function wi(e){return e[0]!==`$`&&!E(e)}const Ti=e=>e===`_`||e===`__`||e===`_ctx`||e===`$stable`,Ei=e=>p(e)?e.map(Oa):[Oa(e)],Di=(e,t,n)=>{if(t._n)return t;let r=R((...e)=>Ei(t(...e)),n);return r._c=!1,r},Oi=(e,t,n)=>{let r=e._ctx;for(let n in e){if(Ti(n))continue;let i=e[n];if(g(i))t[n]=Di(n,i,r);else if(i!=null){let e=Ei(i);t[n]=()=>e}}},ki=(e,t)=>{let n=Ei(t);e.slots.default=()=>n},Ai=(e,t,n)=>{for(let r in t)(n||!Ti(r))&&(e[r]=t[r])},ji=(e,t,n)=>{let r=e.slots=gi();if(e.vnode.shapeFlag&32){let e=t.__;e&&O(r,`__`,e,!0);let i=t._;i?(Ai(r,t,n),n&&O(r,`_`,i,!0)):Oi(t,r)}else t&&ki(e,t)},Mi=(e,t,n)=>{let{vnode:i,slots:a}=e,o=!0,s=r;if(i.shapeFlag&32){let e=t._;e?n&&e===1?o=!1:Ai(a,t,n):(o=!t.$stable,Oi(t,a)),s=t}else t&&(ki(e,t),s={default:1});if(o)for(let e in a)!Ti(e)&&s[e]==null&&delete a[e]},Ni=oa;function Pi(e){return Fi(e)}function Fi(e,t){let n=de();n.__VUE__=!0;let{insert:o,remove:s,patchProp:c,createElement:l,createText:u,createComment:d,setText:f,setElementText:m,parentNode:h,nextSibling:g,setScopeId:_=a,insertStaticContent:v}=e,y=(e,t,n,r=null,i=null,a=null,o=void 0,s=null,c=!!t.dynamicChildren)=>{if(e===t)return;e&&!ba(e,t)&&(r=ye(e),he(e,i,a,!0),e=null),t.patchFlag===-2&&(c=!1,t.dynamicChildren=null);let{type:l,ref:u,shapeFlag:d}=t;switch(l){case sa:b(e,t,n,r);break;case ca:x(e,t,n,r);break;case la:e??S(t,n,r,o);break;case V:ae(e,t,n,r,i,a,o,s,c);break;default:d&1?T(e,t,n,r,i,a,o,s,c):d&6?oe(e,t,n,r,i,a,o,s,c):(d&64||d&128)&&l.process(e,t,n,r,i,a,o,s,c,Se)}u!=null&&i?mr(u,e&&e.ref,a,t||e,!t):u==null&&e&&e.ref!=null&&mr(e.ref,null,a,e,!0)},b=(e,t,n,r)=>{if(e==null)o(t.el=u(t.children),n,r);else{let n=t.el=e.el;t.children!==e.children&&f(n,t.children)}},x=(e,t,n,r)=>{e==null?o(t.el=d(t.children||``),n,r):t.el=e.el},S=(e,t,n,r)=>{[e.el,e.anchor]=v(e.children,t,n,r,e.el,e.anchor)},C=({el:e,anchor:t},n,r)=>{let i;for(;e&&e!==t;)i=g(e),o(e,n,r),e=i;o(t,n,r)},w=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=g(e),s(e),e=n;s(t)},T=(e,t,n,r,i,a,o,s,c)=>{t.type===`svg`?o=`svg`:t.type===`math`&&(o=`mathml`),e==null?ee(t,n,r,i,a,o,s,c):ne(e,t,i,a,o,s,c)},ee=(e,t,n,r,i,a,s,u)=>{let d,f,{props:p,shapeFlag:h,transition:g,dirs:_}=e;if(d=e.el=l(e.type,a,p&&p.is,p),h&8?m(d,e.children):h&16&&D(e.children,d,null,r,i,Ii(e,a),s,u),_&&In(e,null,r,`created`),te(d,e,e.scopeId,s,r),p){for(let e in p)e!==`value`&&!E(e)&&c(d,e,null,p[e],a,r);`value`in p&&c(d,`value`,null,p.value,a),(f=p.onVnodeBeforeMount)&&ja(f,r,e)}_&&In(e,null,r,`beforeMount`);let v=Ri(i,g);v&&g.beforeEnter(d),o(d,t,n),((f=p&&p.onVnodeMounted)||v||_)&&Ni(()=>{f&&ja(f,r,e),v&&g.enter(d),_&&In(e,null,r,`mounted`)},i)},te=(e,t,n,r,i)=>{if(n&&_(e,n),r)for(let t=0;t<r.length;t++)_(e,r[t]);if(i){let n=i.subTree;if(t===n||aa(n.type)&&(n.ssContent===t||n.ssFallback===t)){let t=i.vnode;te(e,t,t.scopeId,t.slotScopeIds,i.parent)}}},D=(e,t,n,r,i,a,o,s,c=0)=>{for(let l=c;l<e.length;l++){let c=e[l]=s?ka(e[l]):Oa(e[l]);y(null,c,t,n,r,i,a,o,s)}},ne=(e,t,n,i,a,o,s)=>{let l=t.el=e.el,{patchFlag:u,dynamicChildren:d,dirs:f}=t;u|=e.patchFlag&16;let p=e.props||r,h=t.props||r,g;if(n&&Li(n,!1),(g=h.onVnodeBeforeUpdate)&&ja(g,n,t,e),f&&In(t,e,n,`beforeUpdate`),n&&Li(n,!0),(p.innerHTML&&h.innerHTML==null||p.textContent&&h.textContent==null)&&m(l,``),d?re(e.dynamicChildren,d,l,n,i,Ii(t,a),o):s||k(e,t,l,null,n,i,Ii(t,a),o,!1),u>0){if(u&16)ie(l,p,h,n,a);else if(u&2&&p.class!==h.class&&c(l,`class`,null,h.class,a),u&4&&c(l,`style`,p.style,h.style,a),u&8){let e=t.dynamicProps;for(let t=0;t<e.length;t++){let r=e[t],i=p[r],o=h[r];(o!==i||r===`value`)&&c(l,r,i,o,a,n)}}u&1&&e.children!==t.children&&m(l,t.children)}else !s&&d==null&&ie(l,p,h,n,a);((g=h.onVnodeUpdated)||f)&&Ni(()=>{g&&ja(g,n,t,e),f&&In(t,e,n,`updated`)},i)},re=(e,t,n,r,i,a,o)=>{for(let s=0;s<t.length;s++){let c=e[s],l=t[s],u=c.el&&(c.type===V||!ba(c,l)||c.shapeFlag&198)?h(c.el):n;y(c,l,u,null,r,i,a,o,!0)}},ie=(e,t,n,i,a)=>{if(t!==n){if(t!==r)for(let r in t)!E(r)&&!(r in n)&&c(e,r,t[r],null,a,i);for(let r in n){if(E(r))continue;let o=n[r],s=t[r];o!==s&&r!==`value`&&c(e,r,s,o,a,i)}`value`in n&&c(e,`value`,t.value,n.value,a)}},ae=(e,t,n,r,i,a,s,c,l)=>{let d=t.el=e?e.el:u(``),f=t.anchor=e?e.anchor:u(``),{patchFlag:p,dynamicChildren:m,slotScopeIds:h}=t;h&&(c=c?c.concat(h):h),e==null?(o(d,n,r),o(f,n,r),D(t.children||[],n,f,i,a,s,c,l)):p>0&&p&64&&m&&e.dynamicChildren?(re(e.dynamicChildren,m,n,i,a,s,c),(t.key!=null||i&&t===i.subTree)&&zi(e,t,!0)):k(e,t,n,f,i,a,s,c,l)},oe=(e,t,n,r,i,a,o,s,c)=>{t.slotScopeIds=s,e==null?t.shapeFlag&512?i.ctx.activate(t,n,r,o,c):O(t,n,r,i,a,o,c):ce(e,t,c)},O=(e,t,n,r,i,a,o)=>{let s=e.component=Pa(e,r,i);if(gr(e)&&(s.ctx.renderer=Se),Ua(s,!1,o),s.asyncDep){if(i&&i.registerDep(s,le,o),!e.el){let r=s.subTree=U(ca);x(null,r,t,n),e.placeholder=r.el}}else le(s,e,t,n,i,a,o)},ce=(e,t,n)=>{let r=t.component=e.component;if(na(e,t,n))if(r.asyncDep&&!r.asyncResolved){ue(r,t,n);return}else r.next=t,r.update();else t.el=e.el,r.vnode=t},le=(e,t,n,r,i,a,o)=>{let s=()=>{if(e.isMounted){let{next:t,bu:n,u:r,parent:c,vnode:l}=e;{let n=Vi(e);if(n){t&&(t.el=l.el,ue(e,t,o)),n.asyncDep.then(()=>{e.isUnmounted||s()});return}}let u=t,d;Li(e,!1),t?(t.el=l.el,ue(e,t,o)):t=l,n&&se(n),(d=t.props&&t.props.onVnodeBeforeUpdate)&&ja(d,c,t,l),Li(e,!0);let f=$i(e),p=e.subTree;e.subTree=f,y(p,f,h(p.el),ye(p),e,i,a),t.el=f.el,u===null&&ia(e,f.el),r&&Ni(r,i),(d=t.props&&t.props.onVnodeUpdated)&&Ni(()=>ja(d,c,t,l),i)}else{let o,{el:s,props:c}=t,{bm:l,m:u,parent:d,root:f,type:p}=e,m=hr(t);if(Li(e,!1),l&&se(l),!m&&(o=c&&c.onVnodeBeforeMount)&&ja(o,d,t),Li(e,!0),s&&we){let t=()=>{e.subTree=$i(e),we(s,e.subTree,e,i,null)};m&&p.__asyncHydrate?p.__asyncHydrate(s,e,t):t()}else{f.ce&&f.ce._def.shadowRoot!==!1&&f.ce._injectChildStyle(p);let o=e.subTree=$i(e);y(null,o,n,r,e,i,a),t.el=o.el}if(u&&Ni(u,i),!m&&(o=c&&c.onVnodeMounted)){let e=t;Ni(()=>ja(o,d,e),i)}(t.shapeFlag&256||d&&hr(d.vnode)&&d.vnode.shapeFlag&256)&&e.a&&Ni(e.a,i),e.isMounted=!0,t=n=r=null}};e.scope.on();let c=e.effect=new ke(s);e.scope.off();let l=e.update=c.run.bind(c),u=e.job=c.runIfDirty.bind(c);u.i=e,u.id=e.uid,c.scheduler=()=>Tn(u),Li(e,!0),l()},ue=(e,t,n)=>{t.component=e;let r=e.vnode.props;e.vnode=t,e.next=null,yi(e,t.props,r,n),Mi(e,t.children,n),We(),On(e),Ge()},k=(e,t,n,r,i,a,o,s,c=!1)=>{let l=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(f&128){pe(l,d,n,r,i,a,o,s,c);return}else if(f&256){fe(l,d,n,r,i,a,o,s,c);return}}p&8?(u&16&&ve(l,i,a),d!==l&&m(n,d)):u&16?p&16?pe(l,d,n,r,i,a,o,s,c):ve(l,i,a,!0):(u&8&&m(n,``),p&16&&D(d,n,r,i,a,o,s,c))},fe=(e,t,n,r,a,o,s,c,l)=>{e||=i,t||=i;let u=e.length,d=t.length,f=Math.min(u,d),p;for(p=0;p<f;p++){let r=t[p]=l?ka(t[p]):Oa(t[p]);y(e[p],r,n,null,a,o,s,c,l)}u>d?ve(e,a,o,!0,!1,f):D(t,n,r,a,o,s,c,l,f)},pe=(e,t,n,r,a,o,s,c,l)=>{let u=0,d=t.length,f=e.length-1,p=d-1;for(;u<=f&&u<=p;){let r=e[u],i=t[u]=l?ka(t[u]):Oa(t[u]);if(ba(r,i))y(r,i,n,null,a,o,s,c,l);else break;u++}for(;u<=f&&u<=p;){let r=e[f],i=t[p]=l?ka(t[p]):Oa(t[p]);if(ba(r,i))y(r,i,n,null,a,o,s,c,l);else break;f--,p--}if(u>f){if(u<=p){let e=p+1,i=e<d?t[e].el:r;for(;u<=p;)y(null,t[u]=l?ka(t[u]):Oa(t[u]),n,i,a,o,s,c,l),u++}}else if(u>p)for(;u<=f;)he(e[u],a,o,!0),u++;else{let m=u,h=u,g=new Map;for(u=h;u<=p;u++){let e=t[u]=l?ka(t[u]):Oa(t[u]);e.key!=null&&g.set(e.key,u)}let _,v=0,b=p-h+1,x=!1,S=0,C=Array(b);for(u=0;u<b;u++)C[u]=0;for(u=m;u<=f;u++){let r=e[u];if(v>=b){he(r,a,o,!0);continue}let i;if(r.key!=null)i=g.get(r.key);else for(_=h;_<=p;_++)if(C[_-h]===0&&ba(r,t[_])){i=_;break}i===void 0?he(r,a,o,!0):(C[i-h]=u+1,i>=S?S=i:x=!0,y(r,t[i],n,null,a,o,s,c,l),v++)}let w=x?Bi(C):i;for(_=w.length-1,u=b-1;u>=0;u--){let e=h+u,i=t[e],f=t[e+1],p=e+1<d?f.el||f.placeholder:r;C[u]===0?y(null,i,n,p,a,o,s,c,l):x&&(_<0||u!==w[_]?me(i,n,p,2):_--)}}},me=(e,t,n,r,i=null)=>{let{el:a,type:c,transition:l,children:u,shapeFlag:d}=e;if(d&6){me(e.component.subTree,t,n,r);return}if(d&128){e.suspense.move(t,n,r);return}if(d&64){c.move(e,t,n,Se);return}if(c===V){o(a,t,n);for(let e=0;e<u.length;e++)me(u[e],t,n,r);o(e.anchor,t,n);return}if(c===la){C(e,t,n);return}let f=r!==2&&d&1&&l;if(f)if(r===0)l.beforeEnter(a),o(a,t,n),Ni(()=>l.enter(a),i);else{let{leave:r,delayLeave:i,afterLeave:c}=l,u=()=>{e.ctx.isUnmounted?s(a):o(a,t,n)},d=()=>{r(a,()=>{u(),c&&c()})};i?i(a,u,d):d()}else o(a,t,n)},he=(e,t,n,r=!1,i=!1)=>{let{type:a,props:o,ref:s,children:c,dynamicChildren:l,shapeFlag:u,patchFlag:d,dirs:f,cacheIndex:p}=e;if(d===-2&&(i=!1),s!=null&&(We(),mr(s,null,n,e,!0),Ge()),p!=null&&(t.renderCache[p]=void 0),u&256){t.ctx.deactivate(e);return}let m=u&1&&f,h=!hr(e),g;if(h&&(g=o&&o.onVnodeBeforeUnmount)&&ja(g,t,e),u&6)_e(e.component,n,r);else{if(u&128){e.suspense.unmount(n,r);return}m&&In(e,null,t,`beforeUnmount`),u&64?e.type.remove(e,t,n,Se,r):l&&!l.hasOnce&&(a!==V||d>0&&d&64)?ve(l,t,n,!1,!0):(a===V&&d&384||!i&&u&16)&&ve(c,t,n),r&&A(e)}(h&&(g=o&&o.onVnodeUnmounted)||m)&&Ni(()=>{g&&ja(g,t,e),m&&In(e,null,t,`unmounted`)},n)},A=e=>{let{type:t,el:n,anchor:r,transition:i}=e;if(t===V){ge(n,r);return}if(t===la){w(e);return}let a=()=>{s(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(e.shapeFlag&1&&i&&!i.persisted){let{leave:t,delayLeave:r}=i,o=()=>t(n,a);r?r(e.el,a,o):o()}else a()},ge=(e,t)=>{let n;for(;e!==t;)n=g(e),s(e),e=n;s(t)},_e=(e,t,n)=>{let{bum:r,scope:i,job:a,subTree:o,um:s,m:c,a:l,parent:u,slots:{__:d}}=e;Hi(c),Hi(l),r&&se(r),u&&p(d)&&d.forEach(e=>{u.renderCache[e]=void 0}),i.stop(),a&&(a.flags|=8,he(o,e,t,n)),s&&Ni(s,t),Ni(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,t.deps===0&&t.resolve())},ve=(e,t,n,r=!1,i=!1,a=0)=>{for(let o=a;o<e.length;o++)he(e[o],t,n,r,i)},ye=e=>{if(e.shapeFlag&6)return ye(e.component.subTree);if(e.shapeFlag&128)return e.suspense.next();let t=g(e.anchor||e.el),n=t&&t[Ln];return n?g(n):t},be=!1,xe=(e,t,n)=>{e==null?t._vnode&&he(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),t._vnode=e,be||(be=!0,On(),kn(),be=!1)},Se={p:y,um:he,m:me,r:A,mt:O,mc:D,pc:k,pbc:re,n:ye,o:e},Ce,we;return t&&([Ce,we]=t(Se)),{render:xe,hydrate:Ce,createApp:di(xe,Ce)}}function Ii({type:e,props:t},n){return n===`svg`&&e===`foreignObject`||n===`mathml`&&e===`annotation-xml`&&t&&t.encoding&&t.encoding.includes(`html`)?void 0:n}function Li({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ri(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function zi(e,t,n=!1){let r=e.children,i=t.children;if(p(r)&&p(i))for(let e=0;e<r.length;e++){let t=r[e],a=i[e];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[e]=ka(i[e]),a.el=t.el),!n&&a.patchFlag!==-2&&zi(t,a)),a.type===sa&&(a.el=t.el),a.type===ca&&!a.el&&(a.el=t.el)}}function Bi(e){let t=e.slice(),n=[0],r,i,a,o,s,c=e.length;for(r=0;r<c;r++){let c=e[r];if(c!==0){if(i=n[n.length-1],e[i]<c){t[r]=i,n.push(r);continue}for(a=0,o=n.length-1;a<o;)s=a+o>>1,e[n[s]]<c?a=s+1:o=s;c<e[n[a]]&&(a>0&&(t[r]=n[a-1]),n[a]=r)}}for(a=n.length,o=n[a-1];a-- >0;)n[a]=o,o=t[o];return n}function Vi(e){let t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Vi(t)}function Hi(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ui=Symbol.for(`v-scx`),Wi=()=>{{let e=z(Ui);return e}};function Gi(e,t){return Ki(e,null,t)}function B(e,t,n){return Ki(e,t,n)}function Ki(e,t,n=r){let{immediate:i,deep:o,flush:s,once:c}=n,u=l({},n),d=t&&i||!t&&s!==`post`,f;if(Ha){if(s===`sync`){let e=Wi();f=e.__watcherHandles||=[]}else if(!d){let e=()=>{};return e.stop=a,e.resume=a,e.pause=a,e}}let p=Fa;u.call=(e,t,n)=>pn(e,p,t,n);let m=!1;s===`post`?u.scheduler=e=>{Ni(e,p&&p.suspense)}:s!==`sync`&&(m=!0,u.scheduler=(e,t)=>{t?e():Tn(e)}),u.augmentJob=e=>{t&&(e.flags|=4),m&&(e.flags|=2,p&&(e.id=p.uid,e.i=p))};let h=un(e,t,u);return Ha&&(f?f.push(h):d&&h()),h}function qi(e,t,n){let r=this.proxy,i=_(e)?e.includes(`.`)?Ji(r,e):()=>r[e]:e.bind(r,r),a;g(t)?a=t:(a=t.handler,n=t);let o=za(this),s=Ki(i,a.bind(r),n);return o(),s}function Ji(e,t){let n=t.split(`.`);return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const Yi=(e,t)=>t===`modelValue`||t===`model-value`?e.modelModifiers:e[`${t}Modifiers`]||e[`${D(t)}Modifiers`]||e[`${re(t)}Modifiers`];function Xi(e,t,...n){if(e.isUnmounted)return;let i=e.vnode.props||r,a=n,o=t.startsWith(`update:`),s=o&&Yi(i,t.slice(7));s&&(s.trim&&(a=n.map(e=>_(e)?e.trim():e)),s.number&&(a=n.map(ce)));let c,l=i[c=ae(t)]||i[c=ae(D(t))];!l&&o&&(l=i[c=ae(re(t))]),l&&pn(l,e,6,a);let u=i[c+`Once`];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,pn(u,e,6,a)}}function Zi(e,t,n=!1){let r=t.emitsCache,i=r.get(e);if(i!==void 0)return i;let a=e.emits,o={},s=!1;if(!g(e)){let r=e=>{let n=Zi(e,t,!0);n&&(s=!0,l(o,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return!a&&!s?(y(e)&&r.set(e,null),null):(p(a)?a.forEach(e=>o[e]=null):l(o,a),y(e)&&r.set(e,o),o)}function Qi(e,t){return!e||!s(t)?!1:(t=t.slice(2).replace(/Once$/,``),f(e,t[0].toLowerCase()+t.slice(1))||f(e,re(t))||f(e,t))}function $i(e){let{type:t,vnode:n,proxy:r,withProxy:i,propsOptions:[a],slots:o,attrs:s,emit:l,render:u,renderCache:d,props:f,data:p,setupState:m,ctx:h,inheritAttrs:g}=e,_=Pn(e),v,y;try{if(n.shapeFlag&4){let e=i||r,t=e;v=Oa(u.call(t,e,d,f,m,p,h)),y=s}else{let e=t;v=Oa(e.length>1?e(f,{attrs:s,slots:o,emit:l}):e(f,null)),y=t.props?s:ea(s)}}catch(t){ua.length=0,mn(t,e,1),v=U(ca)}let b=v;if(y&&g!==!1){let e=Object.keys(y),{shapeFlag:t}=b;e.length&&t&7&&(a&&e.some(c)&&(y=ta(y,a)),b=Ta(b,y,!1,!0))}return n.dirs&&(b=Ta(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&lr(b,n.transition),v=b,Pn(_),v}const ea=e=>{let t;for(let n in e)(n===`class`||n===`style`||s(n))&&((t||={})[n]=e[n]);return t},ta=(e,t)=>{let n={};for(let r in e)(!c(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function na(e,t,n){let{props:r,children:i,component:a}=e,{props:o,children:s,patchFlag:c}=t,l=a.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return r?ra(r,o,l):!!o;if(c&8){let e=t.dynamicProps;for(let t=0;t<e.length;t++){let n=e[t];if(o[n]!==r[n]&&!Qi(l,n))return!0}}}else return(i||s)&&(!s||!s.$stable)?!0:r===o?!1:r?o?ra(r,o,l):!0:!!o;return!1}function ra(e,t,n){let r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let i=0;i<r.length;i++){let a=r[i];if(t[a]!==e[a]&&!Qi(n,a))return!0}return!1}function ia({vnode:e,parent:t},n){for(;t;){let r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const aa=e=>e.__isSuspense;function oa(e,t){t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):Dn(e)}const V=Symbol.for(`v-fgt`),sa=Symbol.for(`v-txt`),ca=Symbol.for(`v-cmt`),la=Symbol.for(`v-stc`),ua=[];let da=null;function fa(e=!1){ua.push(da=e?null:[])}function pa(){ua.pop(),da=ua[ua.length-1]||null}let ma=1;function ha(e,t=!1){ma+=e,e<0&&da&&t&&(da.hasOnce=!0)}function ga(e){return e.dynamicChildren=ma>0?da||i:null,pa(),ma>0&&da&&da.push(e),e}function _a(e,t,n,r,i,a){return ga(H(e,t,n,r,i,a,!0))}function va(e,t,n,r,i){return ga(U(e,t,n,r,i,!0))}function ya(e){return e?e.__v_isVNode===!0:!1}function ba(e,t){return e.type===t.type&&e.key===t.key}const xa=({key:e})=>e??null,Sa=({ref:e,ref_key:t,ref_for:n})=>(typeof e==`number`&&(e=``+e),e==null?null:_(e)||N(e)||g(e)?{i:Mn,r:e,k:t,f:!!n}:e);function H(e,t=null,n=null,r=0,i=null,a=e===V?0:1,o=!1,s=!1){let c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&xa(t),ref:t&&Sa(t),scopeId:Nn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Mn};return s?(Aa(c,n),a&128&&e.normalize(c)):n&&(c.shapeFlag|=_(n)?8:16),ma>0&&!o&&da&&(c.patchFlag>0||a&6)&&c.patchFlag!==32&&da.push(c),c}const U=Ca;function Ca(e,t=null,n=null,r=0,i=null,a=!1){if((!e||e===Fr)&&(e=ca),ya(e)){let r=Ta(e,t,!0);return n&&Aa(r,n),ma>0&&!a&&da&&(r.shapeFlag&6?da[da.indexOf(e)]=r:da.push(r)),r.patchFlag=-2,r}if($a(e)&&(e=e.__vccOpts),t){t=wa(t);let{class:e,style:n}=t;e&&!_(e)&&(t.class=A(e)),y(n)&&(Wt(n)&&!p(n)&&(n=l({},n)),t.style=k(n))}let o=_(e)?1:aa(e)?128:Rn(e)?64:y(e)?4:g(e)?2:0;return H(e,t,n,r,i,o,a,!0)}function wa(e){return e?Wt(e)||_i(e)?l({},e):e:null}function Ta(e,t,n=!1,r=!1){let{props:i,ref:a,patchFlag:o,children:s,transition:c}=e,l=t?W(i||{},t):i,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&xa(l),ref:t&&t.ref?n&&a?p(a)?a.concat(Sa(t)):[a,Sa(t)]:Sa(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==V?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ta(e.ssContent),ssFallback:e.ssFallback&&Ta(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&lr(u,c.clone(u)),u}function Ea(e=` `,t=0){return U(sa,null,e,t)}function Da(e=``,t=!1){return t?(fa(),va(ca,null,e)):U(ca,null,e)}function Oa(e){return e==null||typeof e==`boolean`?U(ca):p(e)?U(V,null,e.slice()):ya(e)?ka(e):U(sa,null,String(e))}function ka(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ta(e)}function Aa(e,t){let n=0,{shapeFlag:r}=e;if(t==null)t=null;else if(p(t))n=16;else if(typeof t==`object`)if(r&65){let n=t.default;n&&(n._c&&(n._d=!1),Aa(e,n()),n._c&&(n._d=!0));return}else{n=32;let r=t._;!r&&!_i(t)?t._ctx=Mn:r===3&&Mn&&(Mn.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else g(t)?(t={default:t,_ctx:Mn},n=32):(t=String(t),r&64?(n=16,t=[Ea(t)]):n=8);e.children=t,e.shapeFlag|=n}function W(...e){let t={};for(let n=0;n<e.length;n++){let r=e[n];for(let e in r)if(e===`class`)t.class!==r.class&&(t.class=A([t.class,r.class]));else if(e===`style`)t.style=k([t.style,r.style]);else if(s(e)){let n=t[e],i=r[e];i&&n!==i&&!(p(n)&&n.includes(i))&&(t[e]=n?[].concat(n,i):i)}else e!==``&&(t[e]=r[e])}return t}function ja(e,t,n,r=null){pn(e,t,7,[n,r])}const Ma=li();let Na=0;function Pa(e,t,n){let i=e.type,a=(t?t.appContext:e.appContext)||Ma,o={uid:Na++,vnode:e,type:i,parent:t,appContext:a,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new we(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(a.provides),ids:t?t.ids:[``,0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ci(i,a),emitsOptions:Zi(i,a),emit:null,emitted:null,propsDefaults:r,inheritAttrs:i.inheritAttrs,ctx:r,data:r,props:r,attrs:r,slots:r,refs:r,setupState:r,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Xi.bind(null,o),e.ce&&e.ce(o),o}let Fa=null;const Ia=()=>Fa||Mn;let La,Ra;{let e=de(),t=(t,n)=>{let r;return(r=e[t])||(r=e[t]=[]),r.push(n),e=>{r.length>1?r.forEach(t=>t(e)):r[0](e)}};La=t(`__VUE_INSTANCE_SETTERS__`,e=>Fa=e),Ra=t(`__VUE_SSR_SETTERS__`,e=>Ha=e)}const za=e=>{let t=Fa;return La(e),e.scope.on(),()=>{e.scope.off(),La(t)}},Ba=()=>{Fa&&Fa.scope.off(),La(null)};function Va(e){return e.vnode.shapeFlag&4}let Ha=!1;function Ua(e,t=!1,n=!1){t&&Ra(t);let{props:r,children:i}=e.vnode,a=Va(e);vi(e,r,a,t),ji(e,i,n||t);let o=a?Wa(e,t):void 0;return t&&Ra(!1),o}function Wa(e,t){let n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Kr);let{setup:r}=n;if(r){We();let n=e.setupContext=r.length>1?Xa(e):null,i=za(e),a=fn(r,e,0,[e.props,n]),o=b(a);if(Ge(),i(),(o||e.sp)&&!hr(e)&&pr(e),o){if(a.then(Ba,Ba),t)return a.then(n=>{Ga(e,n,t)}).catch(t=>{mn(t,e,0)});e.asyncDep=a}else Ga(e,a,t)}else Ja(e,t)}function Ga(e,t,n){g(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:y(t)&&(e.setupState=Qt(t)),Ja(e,n)}let Ka,qa;function Ja(e,t,n){let r=e.type;if(!e.render){if(!t&&Ka&&!r.render){let t=r.template||$r(e).template;if(t){let{isCustomElement:n,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:o}=r,s=l(l({isCustomElement:n,delimiters:a},i),o);r.render=Ka(t,s)}}e.render=r.render||a,qa&&qa(e)}{let t=za(e);We();try{Yr(e)}finally{Ge(),t()}}}const Ya={get(e,t){return tt(e,`get`,``),e[t]}};function Xa(e){let t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,Ya),slots:e.slots,emit:e.emit,expose:t}}function Za(e){return e.exposed?e.exposeProxy||=new Proxy(Qt(Gt(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Wr)return Wr[n](e)},has(e,t){return t in e||t in Wr}}):e.proxy}function Qa(e,t=!0){return g(e)?e.displayName||e.name:e.name||t&&e.__name}function $a(e){return g(e)&&`__vccOpts`in e}const G=(e,t)=>{let n=an(e,t,Ha);return n};function eo(e,t,n){let r=arguments.length;return r===2?y(t)&&!p(t)?ya(t)?U(e,null,[t]):U(e,t):U(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&ya(n)&&(n=[n]),U(e,t,n))}const to=`3.5.18`,no=a;let ro;const io=typeof window<`u`&&window.trustedTypes;if(io)try{ro=io.createPolicy(`vue`,{createHTML:e=>e})}catch{}const ao=ro?e=>ro.createHTML(e):e=>e,oo=`http://www.w3.org/2000/svg`,so=`http://www.w3.org/1998/Math/MathML`,co=typeof document<`u`?document:null,lo=co&&co.createElement(`template`),uo={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{let t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{let i=t===`svg`?co.createElementNS(oo,e):t===`mathml`?co.createElementNS(so,e):n?co.createElement(e,{is:n}):co.createElement(e);return e===`select`&&r&&r.multiple!=null&&i.setAttribute(`multiple`,r.multiple),i},createText:e=>co.createTextNode(e),createComment:e=>co.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>co.querySelector(e),setScopeId(e,t){e.setAttribute(t,``)},insertStaticContent(e,t,n,r,i,a){let o=n?n.previousSibling:t.lastChild;if(i&&(i===a||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===a||!(i=i.nextSibling)););else{lo.innerHTML=ao(r===`svg`?`<svg>${e}</svg>`:r===`mathml`?`<math>${e}</math>`:e);let i=lo.content;if(r===`svg`||r===`mathml`){let e=i.firstChild;for(;e.firstChild;)i.appendChild(e.firstChild);i.removeChild(e)}t.insertBefore(i,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},fo=`transition`,po=`animation`,mo=Symbol(`_vtc`),ho={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},go=l({},er,ho),_o=e=>(e.displayName=`Transition`,e.props=go,e),vo=_o((e,{slots:t})=>eo(ir,xo(e),t)),yo=(e,t=[])=>{p(e)?e.forEach(e=>e(...t)):e&&e(...t)},bo=e=>e?p(e)?e.some(e=>e.length>1):e.length>1:!1;function xo(e){let t={};for(let n in e)n in ho||(t[n]=e[n]);if(e.css===!1)return t;let{name:n=`v`,type:r,duration:i,enterFromClass:a=`${n}-enter-from`,enterActiveClass:o=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:c=a,appearActiveClass:u=o,appearToClass:d=s,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,h=So(i),g=h&&h[0],_=h&&h[1],{onBeforeEnter:v,onEnter:y,onEnterCancelled:b,onLeave:x,onLeaveCancelled:S,onBeforeAppear:C=v,onAppear:w=y,onAppearCancelled:T=b}=t,E=(e,t,n,r)=>{e._enterCancelled=r,To(e,t?d:s),To(e,t?u:o),n&&n()},ee=(e,t)=>{e._isLeaving=!1,To(e,f),To(e,m),To(e,p),t&&t()},te=e=>(t,n)=>{let i=e?w:y,o=()=>E(t,e,n);yo(i,[t,o]),Eo(()=>{To(t,e?c:a),wo(t,e?d:s),bo(i)||Oo(t,r,g,o)})};return l(t,{onBeforeEnter(e){yo(v,[e]),wo(e,a),wo(e,o)},onBeforeAppear(e){yo(C,[e]),wo(e,c),wo(e,u)},onEnter:te(!1),onAppear:te(!0),onLeave(e,t){e._isLeaving=!0;let n=()=>ee(e,t);wo(e,f),e._enterCancelled?(wo(e,p),Mo()):(Mo(),wo(e,p)),Eo(()=>{e._isLeaving&&(To(e,f),wo(e,m),bo(x)||Oo(e,r,_,n))}),yo(x,[e,n])},onEnterCancelled(e){E(e,!1,void 0,!0),yo(b,[e])},onAppearCancelled(e){E(e,!0,void 0,!0),yo(T,[e])},onLeaveCancelled(e){ee(e),yo(S,[e])}})}function So(e){if(e==null)return null;if(y(e))return[Co(e.enter),Co(e.leave)];{let t=Co(e);return[t,t]}}function Co(e){let t=le(e);return t}function wo(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e[mo]||(e[mo]=new Set)).add(t)}function To(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));let n=e[mo];n&&(n.delete(t),n.size||(e[mo]=void 0))}function Eo(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Do=0;function Oo(e,t,n,r){let i=e._endId=++Do,a=()=>{i===e._endId&&r()};if(n!=null)return setTimeout(a,n);let{type:o,timeout:s,propCount:c}=ko(e,t);if(!o)return r();let l=o+`end`,u=0,d=()=>{e.removeEventListener(l,f),a()},f=t=>{t.target===e&&++u>=c&&d()};setTimeout(()=>{u<c&&d()},s+1),e.addEventListener(l,f)}function ko(e,t){let n=window.getComputedStyle(e),r=e=>(n[e]||``).split(`, `),i=r(`${fo}Delay`),a=r(`${fo}Duration`),o=Ao(i,a),s=r(`${po}Delay`),c=r(`${po}Duration`),l=Ao(s,c),u=null,d=0,f=0;t===fo?o>0&&(u=fo,d=o,f=a.length):t===po?l>0&&(u=po,d=l,f=c.length):(d=Math.max(o,l),u=d>0?o>l?fo:po:null,f=u?u===fo?a.length:c.length:0);let p=u===fo&&/\b(transform|all)(,|$)/.test(r(`${fo}Property`).toString());return{type:u,timeout:d,propCount:f,hasTransform:p}}function Ao(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>jo(t)+jo(e[n])))}function jo(e){return e===`auto`?0:Number(e.slice(0,-1).replace(`,`,`.`))*1e3}function Mo(){return document.body.offsetHeight}function No(e,t,n){let r=e[mo];r&&(t=(t?[t,...r]:[...r]).join(` `)),t==null?e.removeAttribute(`class`):n?e.setAttribute(`class`,t):e.className=t}const Po=Symbol(`_vod`),Fo=Symbol(`_vsh`),Io={beforeMount(e,{value:t},{transition:n}){e[Po]=e.style.display===`none`?``:e.style.display,n&&t?n.beforeEnter(e):Lo(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Lo(e,!0),r.enter(e)):r.leave(e,()=>{Lo(e,!1)}):Lo(e,t))},beforeUnmount(e,{value:t}){Lo(e,t)}};function Lo(e,t){e.style.display=t?e[Po]:`none`,e[Fo]=!t}const Ro=Symbol(``),zo=/(^|;)\s*display\s*:/;function Bo(e,t,n){let r=e.style,i=_(n),a=!1;if(n&&!i){if(t)if(_(t))for(let e of t.split(`;`)){let t=e.slice(0,e.indexOf(`:`)).trim();n[t]??Ho(r,t,``)}else for(let e in t)n[e]??Ho(r,e,``);for(let e in n)e===`display`&&(a=!0),Ho(r,e,n[e])}else if(i){if(t!==n){let e=r[Ro];e&&(n+=`;`+e),r.cssText=n,a=zo.test(n)}}else t&&e.removeAttribute(`style`);Po in e&&(e[Po]=a?r.display:``,e[Fo]&&(r.display=`none`))}const Vo=/\s*!important$/;function Ho(e,t,n){if(p(n))n.forEach(n=>Ho(e,t,n));else if(n??=``,t.startsWith(`--`))e.setProperty(t,n);else{let r=Go(e,t);Vo.test(n)?e.setProperty(re(r),n.replace(Vo,``),`important`):e[r]=n}}const Uo=[`Webkit`,`Moz`,`ms`],Wo={};function Go(e,t){let n=Wo[t];if(n)return n;let r=D(t);if(r!==`filter`&&r in e)return Wo[t]=r;r=ie(r);for(let n=0;n<Uo.length;n++){let i=Uo[n]+r;if(i in e)return Wo[t]=i}return t}const Ko=`http://www.w3.org/1999/xlink`;function qo(e,t,n,r,i,a=_e(t)){r&&t.startsWith(`xlink:`)?n==null?e.removeAttributeNS(Ko,t.slice(6,t.length)):e.setAttributeNS(Ko,t,n):n==null||a&&!ve(n)?e.removeAttribute(t):e.setAttribute(t,a?``:v(n)?String(n):n)}function Jo(e,t,n,r,i){if(t===`innerHTML`||t===`textContent`){n!=null&&(e[t]=t===`innerHTML`?ao(n):n);return}let a=e.tagName;if(t===`value`&&a!==`PROGRESS`&&!a.includes(`-`)){let r=a===`OPTION`?e.getAttribute(`value`)||``:e.value,i=n==null?e.type===`checkbox`?`on`:``:String(n);(r!==i||!(`_value`in e))&&(e.value=i),n??e.removeAttribute(t),e._value=n;return}let o=!1;if(n===``||n==null){let r=typeof e[t];r===`boolean`?n=ve(n):n==null&&r===`string`?(n=``,o=!0):r===`number`&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(i||t)}function Yo(e,t,n,r){e.addEventListener(t,n,r)}function Xo(e,t,n,r){e.removeEventListener(t,n,r)}const Zo=Symbol(`_vei`);function Qo(e,t,n,r,i=null){let a=e[Zo]||(e[Zo]={}),o=a[t];if(r&&o)o.value=r;else{let[n,s]=es(t);if(r){let o=a[t]=os(r,i);Yo(e,n,o,s)}else o&&(Xo(e,n,o,s),a[t]=void 0)}}const $o=/(?:Once|Passive|Capture)$/;function es(e){let t;if($o.test(e)){t={};let n;for(;n=e.match($o);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}let n=e[2]===`:`?e.slice(3):re(e.slice(2));return[n,t]}let ts=0;const ns=Promise.resolve(),rs=()=>ts||(ns.then(()=>ts=0),ts=Date.now());function os(e,t){let n=e=>{if(!e._vts)e._vts=Date.now();else if(e._vts<=n.attached)return;pn(ss(e,n.value),t,5,[e])};return n.value=e,n.attached=rs(),n}function ss(e,t){if(p(t)){let n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}else return t}const cs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,ls=(e,t,n,r,i,a)=>{let o=i===`svg`;t===`class`?No(e,r,o):t===`style`?Bo(e,n,r):s(t)?c(t)||Qo(e,t,n,r,a):(t[0]===`.`?(t=t.slice(1),!0):t[0]===`^`?(t=t.slice(1),!1):us(e,t,r,o))?(Jo(e,t,r),!e.tagName.includes(`-`)&&(t===`value`||t===`checked`||t===`selected`)&&qo(e,t,r,o,a,t!==`value`)):e._isVueCE&&(/[A-Z]/.test(t)||!_(r))?Jo(e,D(t),r,a,t):(t===`true-value`?e._trueValue=r:t===`false-value`&&(e._falseValue=r),qo(e,t,r,o))};function us(e,t,n,r){if(r)return!!(t===`innerHTML`||t===`textContent`||t in e&&cs(t)&&g(n));if(t===`spellcheck`||t===`draggable`||t===`translate`||t===`autocorrect`||t===`form`||t===`list`&&e.tagName===`INPUT`||t===`type`&&e.tagName===`TEXTAREA`)return!1;if(t===`width`||t===`height`){let t=e.tagName;if(t===`IMG`||t===`VIDEO`||t===`CANVAS`||t===`SOURCE`)return!1}return cs(t)&&_(n)?!1:t in e}const ds=new WeakMap,fs=new WeakMap,ps=Symbol(`_moveCb`),ms=Symbol(`_enterCb`),hs=e=>(delete e.props.mode,e),gs=hs({name:`TransitionGroup`,props:l({},go,{tag:String,moveClass:String}),setup(e,{slots:t}){let n=Ia(),r=Qn(),i,a;return Er(()=>{if(!i.length)return;let t=e.moveClass||`${e.name||`v`}-move`;if(!xs(i[0].el,n.vnode.el,t)){i=[];return}i.forEach(vs),i.forEach(ys);let r=i.filter(bs);Mo(),r.forEach(e=>{let n=e.el,r=n.style;wo(n,t),r.transform=r.webkitTransform=r.transitionDuration=``;let i=n[ps]=e=>{e&&e.target!==n||(!e||/transform$/.test(e.propertyName))&&(n.removeEventListener(`transitionend`,i),n[ps]=null,To(n,t))};n.addEventListener(`transitionend`,i)}),i=[]}),()=>{let o=M(e),s=xo(o),c=o.tag||V;if(i=[],a)for(let e=0;e<a.length;e++){let t=a[e];t.el&&t.el instanceof Element&&(i.push(t),lr(t,or(t,s,r,n)),ds.set(t,t.el.getBoundingClientRect()))}a=t.default?ur(t.default()):[];for(let e=0;e<a.length;e++){let t=a[e];t.key!=null&&lr(t,or(t,s,r,n))}return U(c,null,a)}}}),_s=gs;function vs(e){let t=e.el;t[ps]&&t[ps](),t[ms]&&t[ms]()}function ys(e){fs.set(e,e.el.getBoundingClientRect())}function bs(e){let t=ds.get(e),n=fs.get(e),r=t.left-n.left,i=t.top-n.top;if(r||i){let t=e.el.style;return t.transform=t.webkitTransform=`translate(${r}px,${i}px)`,t.transitionDuration=`0s`,e}}function xs(e,t,n){let r=e.cloneNode(),i=e[mo];i&&i.forEach(e=>{e.split(/\s+/).forEach(e=>e&&r.classList.remove(e))}),n.split(/\s+/).forEach(e=>e&&r.classList.add(e)),r.style.display=`none`;let a=t.nodeType===1?t:t.parentNode;a.appendChild(r);let{hasTransform:o}=ko(r);return a.removeChild(r),o}const Ss=e=>{let t=e.props[`onUpdate:modelValue`]||!1;return p(t)?e=>se(t,e):t};function Cs(e){e.target.composing=!0}function ws(e){let t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event(`input`)))}const Ts=Symbol(`_assign`),Es={created(e,{modifiers:{lazy:t,trim:n,number:r}},i){e[Ts]=Ss(i);let a=r||i.props&&i.props.type===`number`;Yo(e,t?`change`:`input`,t=>{if(t.target.composing)return;let r=e.value;n&&(r=r.trim()),a&&(r=ce(r)),e[Ts](r)}),n&&Yo(e,`change`,()=>{e.value=e.value.trim()}),t||(Yo(e,`compositionstart`,Cs),Yo(e,`compositionend`,ws),Yo(e,`change`,ws))},mounted(e,{value:t}){e.value=t??``},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:i,number:a}},o){if(e[Ts]=Ss(o),e.composing)return;let s=(a||e.type===`number`)&&!/^0\d/.test(e.value)?ce(e.value):e.value,c=t??``;s!==c&&(document.activeElement===e&&e.type!==`range`&&(r&&t===n||i&&e.value.trim()===c)||(e.value=c))}},Ds=[`ctrl`,`shift`,`alt`,`meta`],Os={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>`button`in e&&e.button!==0,middle:e=>`button`in e&&e.button!==1,right:e=>`button`in e&&e.button!==2,exact:(e,t)=>Ds.some(n=>e[`${n}Key`]&&!t.includes(n))},ks=(e,t)=>{let n=e._withMods||={},r=t.join(`.`);return n[r]||(n[r]=(n,...r)=>{for(let e=0;e<t.length;e++){let r=Os[t[e]];if(r&&r(n,t))return}return e(n,...r)})},As=l({patchProp:ls},uo);let js;function Ms(){return js||=Pi(As)}const Ns=(...e)=>{let t=Ms().createApp(...e),{mount:n}=t;return t.mount=e=>{let r=Fs(e);if(!r)return;let i=t._component;!g(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent=``);let a=n(r,!1,Ps(r));return r instanceof Element&&(r.removeAttribute(`v-cloak`),r.setAttribute(`data-v-app`,``)),a},t};function Ps(e){if(e instanceof SVGElement)return`svg`;if(typeof MathMLElement==`function`&&e instanceof MathMLElement)return`mathml`}function Fs(e){if(_(e)){let t=document.querySelector(e);return t}return e}let Is;const Ls=e=>Is=e,Rs=Symbol();function zs(e){return e&&typeof e==`object`&&Object.prototype.toString.call(e)===`[object Object]`&&typeof e.toJSON!=`function`}var Bs;(function(e){e.direct=`direct`,e.patchObject=`patch object`,e.patchFunction=`patch function`})(Bs||={});const Vs=typeof window<`u`,Hs=(()=>typeof window==`object`&&window.window===window?window:typeof self==`object`&&self.self===self?self:typeof global==`object`&&global.global===global?global:typeof globalThis==`object`?globalThis:{HTMLElement:null})();function Us(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([`﻿`,e],{type:e.type}):e}function Ws(e,t,n){let r=new XMLHttpRequest;r.open(`GET`,e),r.responseType=`blob`,r.onload=function(){Ys(r.response,t,n)},r.onerror=function(){console.error(`could not download file`)},r.send()}function Gs(e){let t=new XMLHttpRequest;t.open(`HEAD`,e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function Ks(e){try{e.dispatchEvent(new MouseEvent(`click`))}catch{let t=new MouseEvent(`click`,{bubbles:!0,cancelable:!0,view:window,detail:0,screenX:80,screenY:20,clientX:80,clientY:20,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:null});e.dispatchEvent(t)}}const qs=typeof navigator==`object`?navigator:{userAgent:``},Js=(()=>/Macintosh/.test(qs.userAgent)&&/AppleWebKit/.test(qs.userAgent)&&!/Safari/.test(qs.userAgent))(),Ys=Vs?typeof HTMLAnchorElement<`u`&&`download`in HTMLAnchorElement.prototype&&!Js?Xs:`msSaveOrOpenBlob`in qs?Zs:Qs:()=>{};function Xs(e,t=`download`,n){let r=document.createElement(`a`);r.download=t,r.rel=`noopener`,typeof e==`string`?(r.href=e,r.origin===location.origin?Ks(r):Gs(r.href)?Ws(e,t,n):(r.target=`_blank`,Ks(r))):(r.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(r.href)},4e4),setTimeout(function(){Ks(r)},0))}function Zs(e,t=`download`,n){if(typeof e==`string`)if(Gs(e))Ws(e,t,n);else{let t=document.createElement(`a`);t.href=e,t.target=`_blank`,setTimeout(function(){Ks(t)})}else navigator.msSaveOrOpenBlob(Us(e,n),t)}function Qs(e,t,n,r){if(r||=open(``,`_blank`),r&&(r.document.title=r.document.body.innerText=`downloading...`),typeof e==`string`)return Ws(e,t,n);let i=e.type===`application/octet-stream`,a=/constructor/i.test(String(Hs.HTMLElement))||`safari`in Hs,o=/CriOS\/[\d]+/.test(navigator.userAgent);if((o||i&&a||Js)&&typeof FileReader<`u`){let t=new FileReader;t.onloadend=function(){let e=t.result;if(typeof e!=`string`)throw r=null,Error(`Wrong reader.result type`);e=o?e:e.replace(/^data:[^;]*;/,`data:attachment/file;`),r?r.location.href=e:location.assign(e),r=null},t.readAsDataURL(e)}else{let t=URL.createObjectURL(e);r?r.location.assign(t):location.href=t,r=null,setTimeout(function(){URL.revokeObjectURL(t)},4e4)}}const{assign:$s}=Object;function ec(){let e=Te(!0),t=e.run(()=>P({})),n=[],r=[],i=Gt({install(e){Ls(i),i._a=e,e.provide(Rs,i),e.config.globalProperties.$pinia=i,r.forEach(e=>n.push(e)),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const tc=()=>{};function nc(e,t,n,r=tc){e.push(t);let i=()=>{let n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};return!n&&Ee()&&De(i),i}function rc(e,...t){e.slice().forEach(e=>{e(...t)})}const ic=e=>e(),ac=Symbol(),oc=Symbol();function sc(e,t){for(let n in e instanceof Map&&t instanceof Map?t.forEach((t,n)=>e.set(n,t)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e),t){if(!t.hasOwnProperty(n))continue;let r=t[n],i=e[n];zs(i)&&zs(r)&&e.hasOwnProperty(n)&&!N(r)&&!Vt(r)?e[n]=sc(i,r):e[n]=r}return e}const cc=Symbol();function lc(e){return!zs(e)||!Object.prototype.hasOwnProperty.call(e,cc)}const{assign:uc}=Object;function dc(e){return!!(N(e)&&e.effect)}function fc(e,t,n,r){let{state:i,actions:a,getters:o}=t,s=n.state.value[e],c;function l(){s||(n.state.value[e]=i?i():{});let t=$t(n.state.value[e]);return uc(t,a,Object.keys(o||{}).reduce((t,r)=>(t[r]=Gt(G(()=>{Ls(n);let t=n._s.get(e);return o[r].call(t,t)})),t),{}))}return c=pc(e,l,t,n,r,!0),c}function pc(e,t,n={},r,i,a){let o,s=uc({actions:{}},n),c={deep:!0},l,u,d=[],f=[],p,m=r.state.value[e];!a&&!m&&(r.state.value[e]={}),P({});let h;function g(t){let n;l=u=!1,typeof t==`function`?(t(r.state.value[e]),n={type:Bs.patchFunction,storeId:e,events:p}):(sc(r.state.value[e],t),n={type:Bs.patchObject,payload:t,storeId:e,events:p});let i=h=Symbol();Cn().then(()=>{h===i&&(l=!0)}),u=!0,rc(d,n,r.state.value[e])}let _=a?function(){let{state:e}=n,t=e?e():{};this.$patch(e=>{uc(e,t)})}:tc;function v(){o.stop(),d=[],f=[],r._s.delete(e)}let y=(t,n=``)=>{if(ac in t)return t[oc]=n,t;let i=function(){Ls(r);let n=Array.from(arguments),a=[],o=[];function s(e){a.push(e)}function c(e){o.push(e)}rc(f,{args:n,name:i[oc],store:x,after:s,onError:c});let l;try{l=t.apply(this&&this.$id===e?this:x,n)}catch(e){throw rc(o,e),e}return l instanceof Promise?l.then(e=>(rc(a,e),e)).catch(e=>(rc(o,e),Promise.reject(e))):(rc(a,l),l)};return i[ac]=!0,i[oc]=n,i},b={_p:r,$id:e,$onAction:nc.bind(null,f),$patch:g,$reset:_,$subscribe(t,n={}){let i=nc(d,t,n.detached,()=>a()),a=o.run(()=>B(()=>r.state.value[e],r=>{(n.flush===`sync`?u:l)&&t({storeId:e,type:Bs.direct,events:p},r)},uc({},c,n)));return i},$dispose:v},x=Lt(b);r._s.set(e,x);let S=r._a&&r._a.runWithContext||ic,C=S(()=>r._e.run(()=>(o=Te()).run(()=>t({action:y}))));for(let t in C){let n=C[t];if(N(n)&&!dc(n)||Vt(n))a||(m&&lc(n)&&(N(n)?n.value=m[t]:sc(n,m[t])),r.state.value[e][t]=n);else if(typeof n==`function`){let e=y(n,t);C[t]=e,s.actions[t]=n}}return uc(x,C),uc(M(x),C),Object.defineProperty(x,`$state`,{get:()=>r.state.value[e],set:e=>{g(t=>{uc(t,e)})}}),r._p.forEach(e=>{uc(x,o.run(()=>e({store:x,app:r._a,pinia:r,options:s})))}),m&&a&&n.hydrate&&n.hydrate(x.$state,m),l=!0,u=!0,x}
/*! #__NO_SIDE_EFFECTS__ */
function mc(e,t,n){let r,i=typeof t==`function`;r=i?n:t;function a(n,a){let o=mi();n||=o?z(Rs,null):null,n&&Ls(n),n=Is,n._s.has(e)||(i?pc(e,t,r,n):fc(e,r,n));let s=n._s.get(e);return s}return a.$id=e,a}function hc(e,t){let n;function r(){n=Te(),n.run(()=>t.length?t(()=>{n?.stop(),r()}):t())}B(e,e=>{e&&!n?r():e||(n?.stop(),n=void 0)},{immediate:!0}),De(()=>{n?.stop()})}const K=typeof window<`u`,gc=K&&`IntersectionObserver`in window,_c=K&&(`ontouchstart`in window||window.navigator.maxTouchPoints>0);K&&`EyeDropper`in window;const vc=K&&`matchMedia`in window&&typeof window.matchMedia==`function`;function yc(e,t,n){bc(e,t),t.set(e,n)}function bc(e,t){if(t.has(e))throw TypeError(`Cannot initialize the same private elements twice on an object`)}function xc(e,t,n){return e.set(Cc(e,t),n),n}function Sc(e,t){return e.get(Cc(e,t))}function Cc(e,t,n){if(typeof e==`function`?e===t:e.has(t))return arguments.length<3?t:n;throw TypeError(`Private element is not present on this object`)}function wc(e,t,n){let r=t.length-1;if(r<0)return e===void 0?n:e;for(let i=0;i<r;i++){if(e==null)return n;e=e[t[i]]}return e==null||e[t[r]]===void 0?n:e[t[r]]}function Tc(e,t){if(e===t)return!0;if(e instanceof Date&&t instanceof Date&&e.getTime()!==t.getTime()||e!==Object(e)||t!==Object(t))return!1;let n=Object.keys(e);return n.length===Object.keys(t).length?n.every(n=>Tc(e[n],t[n])):!1}function Ec(e,t,n){return e==null||!t||typeof t!=`string`?n:e[t]===void 0?(t=t.replace(/\[(\w+)\]/g,`.$1`),t=t.replace(/^\./,``),wc(e,t.split(`.`),n)):e[t]}function Dc(e,t,n){if(t===!0)return e===void 0?n:e;if(t==null||typeof t==`boolean`)return n;if(e!==Object(e)){if(typeof t!=`function`)return n;let r=t(e,n);return r===void 0?n:r}if(typeof t==`string`)return Ec(e,t,n);if(Array.isArray(t))return wc(e,t,n);if(typeof t!=`function`)return n;let r=t(e,n);return r===void 0?n:r}function Oc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Array.from({length:e},(e,n)=>t+n)}function q(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:`px`;if(e==null||e===``)return;let n=Number(e);if(isNaN(n))return String(e);if(isFinite(n))return`${n}${t}`}function kc(e){return typeof e==`object`&&!!e&&!Array.isArray(e)}function Ac(e){let t;return typeof e==`object`&&!!e&&((t=Object.getPrototypeOf(e))===Object.prototype||t===null)}function jc(e){if(e&&`$el`in e){let t=e.$el;return t?.nodeType===Node.TEXT_NODE?t.nextElementSibling:t}return e}Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16});const Mc=Object.freeze({enter:`Enter`,tab:`Tab`,delete:`Delete`,esc:`Escape`,space:`Space`,up:`ArrowUp`,down:`ArrowDown`,left:`ArrowLeft`,right:`ArrowRight`,end:`End`,home:`Home`,del:`Delete`,backspace:`Backspace`,insert:`Insert`,pageup:`PageUp`,pagedown:`PageDown`,shift:`Shift`});function Nc(e){return Object.keys(e)}function Pc(e,t){return t.every(t=>e.hasOwnProperty(t))}function Fc(e,t){let n={};for(let r of t)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}function Ic(e,t,n){let r=Object.create(null),i=Object.create(null);for(let a in e)t.some(e=>e instanceof RegExp?e.test(a):e===a)&&!n?.some(e=>e===a)?r[a]=e[a]:i[a]=e[a];return[r,i]}function Lc(e,t){let n={...e};return t.forEach(e=>delete n[e]),n}const Rc=/^on[^a-z]/,zc=e=>Rc.test(e),Bc=`onAfterscriptexecute.onAnimationcancel.onAnimationend.onAnimationiteration.onAnimationstart.onAuxclick.onBeforeinput.onBeforescriptexecute.onChange.onClick.onCompositionend.onCompositionstart.onCompositionupdate.onContextmenu.onCopy.onCut.onDblclick.onFocusin.onFocusout.onFullscreenchange.onFullscreenerror.onGesturechange.onGestureend.onGesturestart.onGotpointercapture.onInput.onKeydown.onKeypress.onKeyup.onLostpointercapture.onMousedown.onMousemove.onMouseout.onMouseover.onMouseup.onMousewheel.onPaste.onPointercancel.onPointerdown.onPointerenter.onPointerleave.onPointermove.onPointerout.onPointerover.onPointerup.onReset.onSelect.onSubmit.onTouchcancel.onTouchend.onTouchmove.onTouchstart.onTransitioncancel.onTransitionend.onTransitionrun.onTransitionstart.onWheel`.split(`.`);function Vc(e){let[t,n]=Ic(e,[Rc]),r=Lc(t,Bc),[i,a]=Ic(n,[`class`,`style`,`id`,/^data-/]);return Object.assign(i,t),Object.assign(a,r),[i,a]}function Hc(e){return e==null?[]:Array.isArray(e)?e:[e]}function Uc(e,t){let n=0,r=function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];clearTimeout(n),n=setTimeout(()=>e(...i),I(t))};return r.clear=()=>{clearTimeout(n)},r.immediate=e,r}function Wc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;return Math.max(t,Math.min(n,e))}function Gc(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:`0`;return e+n.repeat(Math.max(0,t-e.length))}function Kc(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:`0`;return n.repeat(Math.max(0,t-e.length))+e}function qc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,n=[],r=0;for(;r<e.length;)n.push(e.substr(r,t)),r+=t;return n}function Jc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r={};for(let t in e)r[t]=e[t];for(let i in t){let a=e[i],o=t[i];if(Ac(a)&&Ac(o)){r[i]=Jc(a,o,n);continue}if(n&&Array.isArray(a)&&Array.isArray(o)){r[i]=n(a,o);continue}r[i]=o}return r}function Yc(e){return e.map(e=>e.type===V?Yc(e.children):e).flat()}function Xc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:``;if(Xc.cache.has(e))return Xc.cache.get(e);let t=e.replace(/[^a-z]/gi,`-`).replace(/\B([A-Z])/g,`-$1`).toLowerCase();return Xc.cache.set(e,t),t}Xc.cache=new Map;function Zc(e,t){if(!t||typeof t!=`object`)return[];if(Array.isArray(t))return t.map(t=>Zc(e,t)).flat(1);if(t.suspense)return Zc(e,t.ssContent);if(Array.isArray(t.children))return t.children.map(t=>Zc(e,t)).flat(1);if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component];if(t.component.subTree)return Zc(e,t.component.subTree).flat(1)}return[]}var Qc=new WeakMap,$c=new WeakMap,el=class{constructor(e){yc(this,Qc,[]),yc(this,$c,0),this.size=e}get isFull(){return Sc(Qc,this).length===this.size}push(e){Sc(Qc,this)[Sc($c,this)]=e,xc($c,this,(Sc($c,this)+1)%this.size)}values(){return Sc(Qc,this).slice(Sc($c,this)).concat(Sc(Qc,this).slice(0,Sc($c,this)))}clear(){Sc(Qc,this).length=0,xc($c,this,0)}};function tl(e){let t=Lt({});Gi(()=>{let n=e();for(let e in n)t[e]=n[e]},{flush:`sync`});let n={};for(let e in t)n[e]=L(()=>t[e]);return n}function nl(e,t){return e.includes(t)}function rl(e){return e[2].toLowerCase()+e.slice(3)}const il=()=>[Function,Array];function al(e,t){return t=`on`+ie(t),!!(e[t]||e[`${t}Once`]||e[`${t}Capture`]||e[`${t}OnceCapture`]||e[`${t}CaptureOnce`])}function ol(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(Array.isArray(e))for(let t of e)t(...n);else typeof e==`function`&&e(...n)}function sl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,n=[`button`,`[href]`,`input:not([type="hidden"])`,`select`,`textarea`,`[tabindex]`].map(e=>`${e}${t?`:not([tabindex="-1"])`:``}:not([disabled])`).join(`, `);return[...e.querySelectorAll(n)]}function cl(e,t,n){let r,i=e.indexOf(document.activeElement),a=t===`next`?1:-1;do i+=a,r=e[i];while((!r||r.offsetParent==null||!(n?.(r)??!0))&&i<e.length&&i>=0);return r}function ll(e,t){let n=sl(e);if(t==null)(e===document.activeElement||!e.contains(document.activeElement))&&n[0]?.focus();else if(t===`first`)n[0]?.focus();else if(t===`last`)n.at(-1)?.focus();else if(typeof t==`number`)n[t]?.focus();else{let r=cl(n,t);r?r.focus():ll(e,t===`next`?`first`:`last`)}}function ul(e){return e==null||typeof e==`string`&&e.trim()===``}function dl(e,t){let n=K&&typeof CSS<`u`&&CSS.supports!==void 0&&CSS.supports(`selector(${t})`);if(!n)return null;try{return!!e&&e.matches(t)}catch{return null}}function fl(e){return e.some(e=>ya(e)?e.type===ca?!1:e.type!==V||fl(e.children):!0)?e:null}function pl(e,t){if(!K||e===0)return t(),()=>{};let n=window.setTimeout(t,e);return()=>window.clearTimeout(n)}function ml(e,t){let n=e.clientX,r=e.clientY,i=t.getBoundingClientRect(),a=i.left,o=i.top,s=i.right,c=i.bottom;return n>=a&&n<=s&&r>=o&&r<=c}function hl(){let e=F(),t=t=>{e.value=t};return Object.defineProperty(t,`value`,{enumerable:!0,get:()=>e.value,set:t=>e.value=t}),Object.defineProperty(t,`el`,{enumerable:!0,get:()=>jc(e.value)}),t}function gl(e){let t=e.key.length===1,n=!e.ctrlKey&&!e.metaKey&&!e.altKey;return t&&n}function _l(e){return typeof e==`string`||typeof e==`number`||typeof e==`boolean`||typeof e==`bigint`}function vl(e){let t={};for(let n in e)t[D(n)]=e[n];return t}function yl(e){let t=[`checked`,`disabled`];return Object.fromEntries(Object.entries(e).filter(e=>{let[n,r]=e;return t.includes(n)?!!r:r!==void 0}))}const bl=[`top`,`bottom`],xl=[`start`,`end`,`left`,`right`];function Sl(e,t){let[n,r]=e.split(` `);return r||=nl(bl,n)?`start`:nl(xl,n)?`top`:`center`,{side:Cl(n,t),align:Cl(r,t)}}function Cl(e,t){return e===`start`?t?`right`:`left`:e===`end`?t?`left`:`right`:e}function wl(e){return{side:{center:`center`,top:`bottom`,bottom:`top`,left:`right`,right:`left`}[e.side],align:e.align}}function Tl(e){return{side:e.side,align:{center:`center`,top:`bottom`,bottom:`top`,left:`right`,right:`left`}[e.align]}}function El(e){return{side:e.align,align:e.side}}function Dl(e){return nl(bl,e.side)?`y`:`x`}var Ol=class{constructor(e){let{x:t,y:n,width:r,height:i}=e;this.x=t,this.y=n,this.width=r,this.height=i}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}};function kl(e,t){return{x:{before:Math.max(0,t.left-e.left),after:Math.max(0,e.right-t.right)},y:{before:Math.max(0,t.top-e.top),after:Math.max(0,e.bottom-t.bottom)}}}function Al(e){return Array.isArray(e)?new Ol({x:e[0],y:e[1],width:0,height:0}):e.getBoundingClientRect()}function jl(e){if(e===document.documentElement)return visualViewport?new Ol({x:visualViewport.scale>1?0:visualViewport.offsetLeft,y:visualViewport.scale>1?0:visualViewport.offsetTop,width:visualViewport.width*visualViewport.scale,height:visualViewport.height*visualViewport.scale}):new Ol({x:0,y:0,width:document.documentElement.clientWidth,height:document.documentElement.clientHeight});{let t=e.getBoundingClientRect();return new Ol({x:t.x,y:t.y,width:e.clientWidth,height:e.clientHeight})}}function Ml(e){let t=e.getBoundingClientRect(),n=getComputedStyle(e),r=n.transform;if(r){let i,a,o,s,c;if(r.startsWith(`matrix3d(`))i=r.slice(9,-1).split(/, /),a=Number(i[0]),o=Number(i[5]),s=Number(i[12]),c=Number(i[13]);else if(r.startsWith(`matrix(`))i=r.slice(7,-1).split(/, /),a=Number(i[0]),o=Number(i[3]),s=Number(i[4]),c=Number(i[5]);else return new Ol(t);let l=n.transformOrigin,u=t.x-s-(1-a)*parseFloat(l),d=t.y-c-(1-o)*parseFloat(l.slice(l.indexOf(` `)+1)),f=a?t.width/a:e.offsetWidth+1,p=o?t.height/o:e.offsetHeight+1;return new Ol({x:u,y:d,width:f,height:p})}else return new Ol(t)}function Nl(e,t,n){if(e.animate===void 0)return{finished:Promise.resolve()};let r;try{r=e.animate(t,n)}catch{return{finished:Promise.resolve()}}return r.finished===void 0&&(r.finished=new Promise(e=>{r.onfinish=()=>{e(r)}})),r}const Pl=new WeakMap;function Fl(e,t){Object.keys(t).forEach(n=>{if(zc(n)){let r=rl(n),i=Pl.get(e);if(t[n]==null)i?.forEach(t=>{let[n,a]=t;n===r&&(e.removeEventListener(r,a),i.delete(t))});else if(!i||![...i]?.some(e=>e[0]===r&&e[1]===t[n])){e.addEventListener(r,t[n]);let a=i||new Set;a.add([r,t[n]]),Pl.has(e)||Pl.set(e,a)}}else t[n]==null?e.removeAttribute(n):e.setAttribute(n,t[n])})}function Il(e,t){Object.keys(t).forEach(t=>{if(zc(t)){let n=rl(t),r=Pl.get(e);r?.forEach(t=>{let[i,a]=t;i===n&&(e.removeEventListener(n,a),r.delete(t))})}else e.removeAttribute(t)})}const Ll=2.4,Rl=.2126729,zl=.7151522,Bl=.072175,Vl=.55,Hl=.58,Ul=.57,Wl=.62,Gl=.03,Kl=1.45,ql=5e-4,Jl=1.25,Yl=1.25,Xl=.078,Zl=12.82051282051282,Ql=.06,$l=.001;function eu(e,t){let n=(e.r/255)**Ll,r=(e.g/255)**Ll,i=(e.b/255)**Ll,a=(t.r/255)**Ll,o=(t.g/255)**Ll,s=(t.b/255)**Ll,c=n*Rl+r*zl+i*Bl,l=a*Rl+o*zl+s*Bl;if(c<=Gl&&(c+=(Gl-c)**Kl),l<=Gl&&(l+=(Gl-l)**Kl),Math.abs(l-c)<ql)return 0;let u;if(l>c){let e=(l**Vl-c**Hl)*Jl;u=e<$l?0:e<Xl?e-e*Zl*Ql:e-Ql}else{let e=(l**Wl-c**Ul)*Yl;u=e>-$l?0:e>-Xl?e-e*Zl*Ql:e+Ql}return u*100}function tu(e){no(`Vuetify: ${e}`)}function nu(e){no(`Vuetify error: ${e}`)}function ru(e,t){t=Array.isArray(t)?t.slice(0,-1).map(e=>`'${e}'`).join(`, `)+` or '${t.at(-1)}'`:`'${t}'`,no(`[Vuetify UPGRADE] '${e}' is deprecated, use ${t} instead.`)}const iu=.20689655172413793,au=e=>e>iu**3?Math.cbrt(e):e/(3*iu**2)+4/29,ou=e=>e>iu?e**3:3*iu**2*(e-4/29);function su(e){let t=au,n=t(e[1]);return[116*n-16,500*(t(e[0]/.95047)-n),200*(n-t(e[2]/1.08883))]}function cu(e){let t=ou,n=(e[0]+16)/116;return[t(n+e[1]/500)*.95047,t(n),t(n-e[2]/200)*1.08883]}const lu=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],uu=e=>e<=.0031308?e*12.92:1.055*e**(1/2.4)-.055,du=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],fu=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function pu(e){let t=[,,,],n=uu,r=lu;for(let i=0;i<3;++i)t[i]=Math.round(Wc(n(r[i][0]*e[0]+r[i][1]*e[1]+r[i][2]*e[2]))*255);return{r:t[0],g:t[1],b:t[2]}}function mu(e){let{r:t,g:n,b:r}=e,i=[0,0,0],a=fu,o=du;t=a(t/255),n=a(n/255),r=a(r/255);for(let e=0;e<3;++e)i[e]=o[e][0]*t+o[e][1]*n+o[e][2]*r;return i}function hu(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}function gu(e){return hu(e)&&!/^((rgb|hsl)a?\()?var\(--/.test(e)}const _u=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,vu={rgb:(e,t,n,r)=>({r:e,g:t,b:n,a:r}),rgba:(e,t,n,r)=>({r:e,g:t,b:n,a:r}),hsl:(e,t,n,r)=>xu({h:e,s:t,l:n,a:r}),hsla:(e,t,n,r)=>xu({h:e,s:t,l:n,a:r}),hsv:(e,t,n,r)=>bu({h:e,s:t,v:n,a:r}),hsva:(e,t,n,r)=>bu({h:e,s:t,v:n,a:r})};function yu(e){if(typeof e==`number`)return(isNaN(e)||e<0||e>16777215)&&tu(`'${e}' is not a valid hex color`),{r:(e&16711680)>>16,g:(e&65280)>>8,b:e&255};if(typeof e==`string`&&_u.test(e)){let{groups:t}=e.match(_u),{fn:n,values:r}=t,i=r.split(/,\s*|\s*\/\s*|\s+/).map((e,t)=>e.endsWith(`%`)||t>0&&t<3&&[`hsl`,`hsla`,`hsv`,`hsva`].includes(n)?parseFloat(e)/100:parseFloat(e));return vu[n](...i)}else if(typeof e==`string`){let t=e.startsWith(`#`)?e.slice(1):e;[3,4].includes(t.length)?t=t.split(``).map(e=>e+e).join(``):[6,8].includes(t.length)||tu(`'${e}' is not a valid hex(a) color`);let n=parseInt(t,16);return(isNaN(n)||n<0||n>4294967295)&&tu(`'${e}' is not a valid hex(a) color`),Tu(t)}else if(typeof e==`object`){if(Pc(e,[`r`,`g`,`b`]))return e;if(Pc(e,[`h`,`s`,`l`]))return bu(Su(e));if(Pc(e,[`h`,`s`,`v`]))return bu(e)}throw TypeError(`Invalid color: ${e==null?e:String(e)||e.constructor.name}\nExpected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function bu(e){let{h:t,s:n,v:r,a:i}=e,a=e=>{let i=(e+t/60)%6;return r-r*n*Math.max(Math.min(i,4-i,1),0)},o=[a(5),a(3),a(1)].map(e=>Math.round(e*255));return{r:o[0],g:o[1],b:o[2],a:i}}function xu(e){return bu(Su(e))}function Su(e){let{h:t,s:n,l:r,a:i}=e,a=r+n*Math.min(r,1-r),o=a===0?0:2-2*r/a;return{h:t,s:o,v:a,a:i}}function Cu(e){let t=Math.round(e).toString(16);return(`00`.substr(0,2-t.length)+t).toUpperCase()}function wu(e){let{r:t,g:n,b:r,a:i}=e;return`#${[Cu(t),Cu(n),Cu(r),i===void 0?``:Cu(Math.round(i*255))].join(``)}`}function Tu(e){e=Eu(e);let[t,n,r,i]=qc(e,2).map(e=>parseInt(e,16));return i=i===void 0?i:i/255,{r:t,g:n,b:r,a:i}}function Eu(e){return e.startsWith(`#`)&&(e=e.slice(1)),e=e.replace(/([^0-9a-f])/gi,`F`),(e.length===3||e.length===4)&&(e=e.split(``).map(e=>e+e).join(``)),e.length!==6&&(e=Gc(Gc(e,6),8,`F`)),e}function Du(e,t){let n=su(mu(e));return n[0]+=t*10,pu(cu(n))}function Ou(e,t){let n=su(mu(e));return n[0]-=t*10,pu(cu(n))}function ku(e){let t=yu(e);return mu(t)[1]}function Au(e){let t=Math.abs(eu(yu(0),yu(e))),n=Math.abs(eu(yu(16777215),yu(e)));return n>Math.min(t,50)?`#fff`:`#000`}function J(e,t){return n=>Object.keys(e).reduce((r,i)=>{let a=typeof e[i]==`object`&&e[i]!=null&&!Array.isArray(e[i]),o=a?e[i]:{type:e[i]};return n&&i in n?r[i]={...o,default:n[i]}:r[i]=o,t&&!r[i].source&&(r[i].source=t),r},{})}const ju=J({class:[String,Array,Object],style:{type:[String,Array,Object],default:null}},`component`);function Mu(e,t){let n=Ia();if(!n)throw Error(`[Vuetify] ${e} ${t||`must be called from inside a setup function`}`);return n}function Nu(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:`composables`,t=Mu(e).type;return Xc(t?.aliasName||t?.name)}function Pu(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Mu(`injectSelf`),{provides:n}=t;if(n&&e in n)return n[e]}const Fu=Symbol.for(`vuetify:defaults`);function Iu(e){return P(e)}function Lu(){let e=z(Fu);if(!e)throw Error(`[Vuetify] Could not find defaults instance`);return e}function Ru(e,t){let n=Lu(),r=P(e),i=G(()=>{let e=I(t?.disabled);if(e)return n.value;let i=I(t?.scoped),a=I(t?.reset),o=I(t?.root);if(r.value==null&&!(i||a||o))return n.value;let s=Jc(r.value,{prev:n.value});if(i)return s;if(a||o){let e=Number(a||1/0);for(let t=0;t<=e&&!(!s||!(`prev`in s));t++)s=s.prev;return s&&typeof o==`string`&&o in s&&(s=Jc(Jc(s,{prev:s}),s[o])),s}return s.prev?Jc(s.prev,s):s});return pi(Fu,i),i}function zu(e,t){return e.props&&(e.props[t]!==void 0||e.props[Xc(t)]!==void 0)}function Bu(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Lu(),r=Mu(`useDefaults`);if(t=t??r.type.name??r.type.__name,!t)throw Error(`[Vuetify] Could not determine component name`);let i=G(()=>n.value?.[e._as??t]),a=new Proxy(e,{get(e,t){let a=Reflect.get(e,t);if(t===`class`||t===`style`)return[i.value?.[t],a].filter(e=>e!=null);if(zu(r.vnode,t))return a;let o=i.value?.[t];if(o!==void 0)return o;let s=n.value?.global?.[t];return s===void 0?a:s}}),o=F();Gi(()=>{if(i.value){let e=Object.entries(i.value).filter(e=>{let[t]=e;return t.startsWith(t[0].toUpperCase())});o.value=e.length?Object.fromEntries(e):void 0}else o.value=void 0});function s(){let e=Pu(Fu,r);pi(Fu,G(()=>o.value?Jc(e?.value??{},o.value):e?.value))}return{props:a,provideSubDefaults:s}}function Vu(e){if(e._setup=e._setup??e.setup,!e.name)return tu(`The component is missing an explicit name, unable to generate default prop value`),e;if(e._setup){e.props=J(e.props??{},e.name)();let t=Object.keys(e.props).filter(e=>e!==`class`&&e!==`style`);e.filterProps=function(e){return Fc(e,t)},e.props._as=String,e.setup=function(t,n){let r=Lu();if(!r.value)return e._setup(t,n);let{props:i,provideSubDefaults:a}=Bu(t,t._as??e.name,r),o=e._setup(i,n);return a(),o}}return e}function Y(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return t=>(e?Vu:dr)(t)}function Hu(e,t){return t.props=e,t}function Uu(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:`div`,n=arguments.length>2?arguments[2]:void 0;return Y()({name:n??ie(D(e.replace(/__/g,`-`))),props:{tag:{type:String,default:t},...ju()},setup(t,n){let{slots:r}=n;return()=>eo(t.tag,{class:[e,t.class],style:t.style},r.default?.())}})}function Wu(e){if(typeof e.getRootNode!=`function`){for(;e.parentNode;)e=e.parentNode;return e===document?document:null}let t=e.getRootNode();return t!==document&&t.getRootNode({composed:!0})!==document?null:t}const Gu=`cubic-bezier(0.4, 0, 0.2, 1)`,Ku=`cubic-bezier(0.0, 0, 0.2, 1)`,qu=`cubic-bezier(0.4, 0, 1, 1)`;function Ju(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(t?Zu(e):Xu(e))return e;e=e.parentElement}return document.scrollingElement}function Yu(e,t){let n=[];if(t&&e&&!t.contains(e))return n;for(;e&&(Xu(e)&&n.push(e),e!==t);)e=e.parentElement;return n}function Xu(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;let t=window.getComputedStyle(e);return t.overflowY===`scroll`||t.overflowY===`auto`&&e.scrollHeight>e.clientHeight}function Zu(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;let t=window.getComputedStyle(e);return[`scroll`,`auto`].includes(t.overflowY)}function Qu(e){for(;e;){if(window.getComputedStyle(e).position===`fixed`)return!0;e=e.offsetParent}return!1}function X(e){let t=Mu(`useRender`);t.render=e}function $u(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e=>e,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:e=>e,a=Mu(`useProxiedModel`),o=P(e[t]===void 0?n:e[t]),s=Xc(t),c=s!==t,l=G(c?()=>(e[t],!!((a.vnode.props?.hasOwnProperty(t)||a.vnode.props?.hasOwnProperty(s))&&(a.vnode.props?.hasOwnProperty(`onUpdate:${t}`)||a.vnode.props?.hasOwnProperty(`onUpdate:${s}`)))):()=>(e[t],!!(a.vnode.props?.hasOwnProperty(t)&&a.vnode.props?.hasOwnProperty(`onUpdate:${t}`))));hc(()=>!l.value,()=>{B(()=>e[t],e=>{o.value=e})});let u=G({get(){let n=e[t];return r(l.value?n:o.value)},set(n){let s=i(n),c=M(l.value?e[t]:o.value);c===s||r(c)===n||(o.value=s,a?.emit(`update:${t}`,s))}});return Object.defineProperty(u,`externalValue`,{get:()=>l.value?e[t]:o.value}),u}var ed={badge:`Badge`,open:`Open`,close:`Close`,dismiss:`Dismiss`,confirmEdit:{ok:`OK`,cancel:`Cancel`},dataIterator:{noResultsText:`No matching records found`,loadingText:`Loading items...`},dataTable:{itemsPerPageText:`Rows per page:`,ariaLabel:{sortDescending:`Sorted descending.`,sortAscending:`Sorted ascending.`,sortNone:`Not sorted.`,activateNone:`Activate to remove sorting.`,activateDescending:`Activate to sort descending.`,activateAscending:`Activate to sort ascending.`},sortBy:`Sort by`},dataFooter:{itemsPerPageText:`Items per page:`,itemsPerPageAll:`All`,nextPage:`Next page`,prevPage:`Previous page`,firstPage:`First page`,lastPage:`Last page`,pageText:`{0}-{1} of {2}`},dateRangeInput:{divider:`to`},datePicker:{itemsSelected:`{0} selected`,range:{title:`Select dates`,header:`Enter dates`},title:`Select date`,header:`Enter date`,input:{placeholder:`Enter date`}},noDataText:`No data available`,carousel:{prev:`Previous visual`,next:`Next visual`,ariaLabel:{delimiter:`Carousel slide {0} of {1}`}},calendar:{moreEvents:`{0} more`,today:`Today`},input:{clear:`Clear {0}`,prependAction:`{0} prepended action`,appendAction:`{0} appended action`,otp:`Please enter OTP character {0}`},fileInput:{counter:`{0} files`,counterSize:`{0} files ({1} in total)`},fileUpload:{title:`Drag and drop files here`,divider:`or`,browse:`Browse Files`},timePicker:{am:`AM`,pm:`PM`,title:`Select Time`},pagination:{ariaLabel:{root:`Pagination Navigation`,next:`Next page`,previous:`Previous page`,page:`Go to page {0}`,currentPage:`Page {0}, Current page`,first:`First page`,last:`Last page`}},stepper:{next:`Next`,prev:`Previous`},rating:{ariaLabel:{item:`Rating {0} of {1}`}},loading:`Loading...`,infiniteScroll:{loadMore:`Load more`,empty:`No more`},rules:{required:`This field is required`,email:`Please enter a valid email`,number:`This field can only contain numbers`,integer:`This field can only contain integer values`,capital:`This field can only contain uppercase letters`,maxLength:`You must enter a maximum of {0} characters`,minLength:`You must enter a minimum of {0} characters`,strictLength:`The length of the entered field is invalid`,exclude:`The {0} character is not allowed`,notEmpty:`Please choose at least one value`,pattern:`Invalid format`},hotkey:{then:`then`,ctrl:`Ctrl`,command:`Command`,space:`Space`,shift:`Shift`,alt:`Alt`,enter:`Enter`,escape:`Escape`,upArrow:`Up Arrow`,downArrow:`Down Arrow`,leftArrow:`Left Arrow`,rightArrow:`Right Arrow`,backspace:`Backspace`,option:`Option`,plus:`plus`,shortcut:`Keyboard shortcut: {0}`}};const td=`$vuetify.`,nd=(e,t)=>e.replace(/\{(\d+)\}/g,(e,n)=>String(t[Number(n)])),rd=(e,t,n)=>function(r){for(var i=arguments.length,a=Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];if(!r.startsWith(td))return nd(r,a);let s=r.replace(td,``),c=e.value&&n.value[e.value],l=t.value&&n.value[t.value],u=Ec(c,s,null);return u||(tu(`Translation key "${r}" not found in "${e.value}", trying fallback locale`),u=Ec(l,s,null)),u||(nu(`Translation key "${r}" not found in fallback`),u=r),typeof u!=`string`&&(nu(`Translation key "${r}" has a non-string value`),u=r),nd(u,a)};function id(e,t){return(n,r)=>{let i=new Intl.NumberFormat([e.value,t.value],r);return i.format(n)}}function ad(e,t){let n=id(e,t);return n(.1).includes(`,`)?`,`:`.`}function od(e,t,n){let r=$u(e,t,e[t]??n.value);return r.value=e[t]??n.value,B(n,i=>{e[t]??(r.value=n.value)}),r}function sd(e){return t=>{let n=od(t,`locale`,e.current),r=od(t,`fallback`,e.fallback),i=od(t,`messages`,e.messages);return{name:`vuetify`,current:n,fallback:r,messages:i,decimalSeparator:L(()=>ad(n,r)),t:rd(n,r,i),n:id(n,r),provide:sd({current:n,fallback:r,messages:i})}}}function cd(e){let t=F(e?.locale??`en`),n=F(e?.fallback??`en`),r=P({en:ed,...e?.messages});return{name:`vuetify`,current:t,fallback:n,messages:r,decimalSeparator:L(()=>e?.decimalSeparator??ad(t,n)),t:rd(t,n,r),n:id(t,n),provide:sd({current:t,fallback:n,messages:r})}}const ld=Symbol.for(`vuetify:locale`);function ud(e){return e.name!=null}function dd(e){let t=e?.adapter&&ud(e?.adapter)?e?.adapter:cd(e),n=md(t,e);return{...t,...n}}function fd(){let e=z(ld);if(!e)throw Error(`[Vuetify] Could not find injected locale instance`);return e}Symbol.for(`vuetify:rtl`);function pd(){return{af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,km:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1}}function md(e,t){let n=P(t?.rtl??pd()),r=G(()=>n.value[e.current.value]??!1);return{isRtl:r,rtl:n,rtlClasses:L(()=>`v-locale--is-${r.value?`rtl`:`ltr`}`)}}function hd(){let e=z(ld);if(!e)throw Error(`[Vuetify] Could not find injected rtl instance`);return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}function gd(e){let t=e.slice(-2).toUpperCase();switch(!0){case e===`GB-alt-variant`:return{firstDay:0,firstWeekSize:4};case e===`001`:return{firstDay:1,firstWeekSize:1};case`AG AS BD BR BS BT BW BZ CA CO DM DO ET GT GU HK HN ID IL IN JM JP KE
    KH KR LA MH MM MO MT MX MZ NI NP PA PE PH PK PR PY SA SG SV TH TT TW UM US
    VE VI WS YE ZA ZW`.includes(t):return{firstDay:0,firstWeekSize:1};case`AI AL AM AR AU AZ BA BM BN BY CL CM CN CR CY EC GE HR KG KZ LB LK LV
    MD ME MK MN MY NZ RO RS SI TJ TM TR UA UY UZ VN XK`.includes(t):return{firstDay:1,firstWeekSize:1};case`AD AN AT AX BE BG CH CZ DE DK EE ES FI FJ FO FR GB GF GP GR HU IE IS
    IT LI LT LU MC MQ NL NO PL RE RU SE SK SM VA`.includes(t):return{firstDay:1,firstWeekSize:4};case`AE AF BH DJ DZ EG IQ IR JO KW LY OM QA SD SY`.includes(t):return{firstDay:6,firstWeekSize:1};case t===`MV`:return{firstDay:5,firstWeekSize:1};case t===`PT`:return{firstDay:0,firstWeekSize:4};default:return null}}function _d(e,t,n){let r=[],i=[],a=bd(e),o=xd(e),s=n??gd(t)?.firstDay??0,c=(a.getDay()-s+7)%7,l=(o.getDay()-s+7)%7;for(let e=0;e<c;e++){let t=new Date(a);t.setDate(t.getDate()-(c-e)),i.push(t)}for(let t=1;t<=o.getDate();t++){let n=new Date(e.getFullYear(),e.getMonth(),t);i.push(n),i.length===7&&(r.push(i),i=[])}for(let e=1;e<7-l;e++){let t=new Date(o);t.setDate(t.getDate()+e),i.push(t)}return i.length>0&&r.push(i),r}function vd(e,t,n){let r=n??gd(t)?.firstDay??0,i=new Date(e);for(;i.getDay()!==r;)i.setDate(i.getDate()-1);return i}function yd(e,t){let n=new Date(e),r=((gd(t)?.firstDay??0)+6)%7;for(;n.getDay()!==r;)n.setDate(n.getDate()+1);return n}function bd(e){return new Date(e.getFullYear(),e.getMonth(),1)}function xd(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}function Sd(e){let t=e.split(`-`).map(Number);return new Date(t[0],t[1]-1,t[2])}const Cd=/^([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))$/;function wd(e){if(e==null)return new Date;if(e instanceof Date)return e;if(typeof e==`string`){let t;if(Cd.test(e))return Sd(e);if(t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const Td=new Date(2e3,0,2);function Ed(e,t,n){let r=t??gd(e)?.firstDay??0;return Oc(7).map(t=>{let i=new Date(Td);return i.setDate(Td.getDate()+r+t),new Intl.DateTimeFormat(e,{weekday:n??`narrow`}).format(i)})}function Dd(e,t,n,r){let i=wd(e)??new Date,a=r?.[t];if(typeof a==`function`)return a(i,t,n);let o={};switch(t){case`fullDate`:o={year:`numeric`,month:`short`,day:`numeric`};break;case`fullDateWithWeekday`:o={weekday:`long`,year:`numeric`,month:`long`,day:`numeric`};break;case`normalDate`:let e=i.getDate(),t=new Intl.DateTimeFormat(n,{month:`long`}).format(i);return`${e} ${t}`;case`normalDateWithWeekday`:o={weekday:`short`,day:`numeric`,month:`short`};break;case`shortDate`:o={month:`short`,day:`numeric`};break;case`year`:o={year:`numeric`};break;case`month`:o={month:`long`};break;case`monthShort`:o={month:`short`};break;case`monthAndYear`:o={month:`long`,year:`numeric`};break;case`monthAndDate`:o={month:`long`,day:`numeric`};break;case`weekday`:o={weekday:`long`};break;case`weekdayShort`:o={weekday:`short`};break;case`dayOfMonth`:return new Intl.NumberFormat(n).format(i.getDate());case`hours12h`:o={hour:`numeric`,hour12:!0};break;case`hours24h`:o={hour:`numeric`,hour12:!1};break;case`minutes`:o={minute:`numeric`};break;case`seconds`:o={second:`numeric`};break;case`fullTime`:o={hour:`numeric`,minute:`numeric`};break;case`fullTime12h`:o={hour:`numeric`,minute:`numeric`,hour12:!0};break;case`fullTime24h`:o={hour:`numeric`,minute:`numeric`,hour12:!1};break;case`fullDateTime`:o={year:`numeric`,month:`short`,day:`numeric`,hour:`numeric`,minute:`numeric`};break;case`fullDateTime12h`:o={year:`numeric`,month:`short`,day:`numeric`,hour:`numeric`,minute:`numeric`,hour12:!0};break;case`fullDateTime24h`:o={year:`numeric`,month:`short`,day:`numeric`,hour:`numeric`,minute:`numeric`,hour12:!1};break;case`keyboardDate`:o={year:`numeric`,month:`2-digit`,day:`2-digit`};break;case`keyboardDateTime`:return o={year:`numeric`,month:`2-digit`,day:`2-digit`,hour:`numeric`,minute:`numeric`},new Intl.DateTimeFormat(n,o).format(i).replace(/, /g,` `);case`keyboardDateTime12h`:return o={year:`numeric`,month:`2-digit`,day:`2-digit`,hour:`numeric`,minute:`numeric`,hour12:!0},new Intl.DateTimeFormat(n,o).format(i).replace(/, /g,` `);case`keyboardDateTime24h`:return o={year:`numeric`,month:`2-digit`,day:`2-digit`,hour:`numeric`,minute:`numeric`,hour12:!1},new Intl.DateTimeFormat(n,o).format(i).replace(/, /g,` `);default:o=a??{timeZone:`UTC`,timeZoneName:`short`}}return new Intl.DateTimeFormat(n,o).format(i)}function Od(e,t){let n=e.toJsDate(t),r=n.getFullYear(),i=Kc(String(n.getMonth()+1),2,`0`),a=Kc(String(n.getDate()),2,`0`);return`${r}-${i}-${a}`}function kd(e){let[t,n,r]=e.split(`-`).map(Number);return new Date(t,n-1,r)}function Ad(e,t){let n=new Date(e);return n.setMinutes(n.getMinutes()+t),n}function jd(e,t){let n=new Date(e);return n.setHours(n.getHours()+t),n}function Md(e,t){let n=new Date(e);return n.setDate(n.getDate()+t),n}function Nd(e,t){let n=new Date(e);return n.setDate(n.getDate()+t*7),n}function Pd(e,t){let n=new Date(e);return n.setDate(1),n.setMonth(n.getMonth()+t),n}function Fd(e){return e.getFullYear()}function Id(e){return e.getMonth()}function Ld(e,t,n,r){let i=gd(t),a=n??i?.firstDay??0,o=r??i?.firstWeekSize??1;function s(e){let n=new Date(e,0,1);return 7-ef(n,vd(n,t,a),`days`)}let c=Fd(e),l=Md(vd(e,t,a),6);c<Fd(l)&&s(c+1)>=o&&c++;let u=new Date(c,0,1),d=s(c),f=d>=o?Md(u,d-7):Md(u,d);return 1+ef(lf(e),cf(f),`weeks`)}function Rd(e){return e.getDate()}function zd(e){return new Date(e.getFullYear(),e.getMonth()+1,1)}function Bd(e){return new Date(e.getFullYear(),e.getMonth()-1,1)}function Vd(e){return e.getHours()}function Hd(e){return e.getMinutes()}function Ud(e){return new Date(e.getFullYear(),0,1)}function Wd(e){return new Date(e.getFullYear(),11,31)}function Gd(e,t){return qd(e,t[0])&&Yd(e,t[1])}function Kd(e){let t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}function qd(e,t){return e.getTime()>t.getTime()}function Jd(e,t){return qd(cf(e),cf(t))}function Yd(e,t){return e.getTime()<t.getTime()}function Xd(e,t){return e.getTime()===t.getTime()}function Zd(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function Qd(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function $d(e,t){return e.getFullYear()===t.getFullYear()}function ef(e,t,n){let r=new Date(e),i=new Date(t);switch(n){case`years`:return r.getFullYear()-i.getFullYear();case`quarters`:return Math.floor((r.getMonth()-i.getMonth()+(r.getFullYear()-i.getFullYear())*12)/4);case`months`:return r.getMonth()-i.getMonth()+(r.getFullYear()-i.getFullYear())*12;case`weeks`:return Math.floor((r.getTime()-i.getTime())/(1e3*60*60*24*7));case`days`:return Math.floor((r.getTime()-i.getTime())/(1e3*60*60*24));case`hours`:return Math.floor((r.getTime()-i.getTime())/(1e3*60*60));case`minutes`:return Math.floor((r.getTime()-i.getTime())/(1e3*60));case`seconds`:return Math.floor((r.getTime()-i.getTime())/1e3);default:return r.getTime()-i.getTime()}}function tf(e,t){let n=new Date(e);return n.setHours(t),n}function nf(e,t){let n=new Date(e);return n.setMinutes(t),n}function rf(e,t){let n=new Date(e);return n.setMonth(t),n}function af(e,t){let n=new Date(e);return n.setDate(t),n}function sf(e,t){let n=new Date(e);return n.setFullYear(t),n}function cf(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0,0)}function lf(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59,999)}var uf=class{constructor(e){this.locale=e.locale,this.formats=e.formats}date(e){return wd(e)}toJsDate(e){return e}toISO(e){return Od(this,e)}parseISO(e){return kd(e)}addMinutes(e,t){return Ad(e,t)}addHours(e,t){return jd(e,t)}addDays(e,t){return Md(e,t)}addWeeks(e,t){return Nd(e,t)}addMonths(e,t){return Pd(e,t)}getWeekArray(e,t){let n=t===void 0?void 0:Number(t);return _d(e,this.locale,n)}startOfWeek(e,t){let n=t===void 0?void 0:Number(t);return vd(e,this.locale,n)}endOfWeek(e){return yd(e,this.locale)}startOfMonth(e){return bd(e)}endOfMonth(e){return xd(e)}format(e,t){return Dd(e,t,this.locale,this.formats)}isEqual(e,t){return Xd(e,t)}isValid(e){return Kd(e)}isWithinRange(e,t){return Gd(e,t)}isAfter(e,t){return qd(e,t)}isAfterDay(e,t){return Jd(e,t)}isBefore(e,t){return!qd(e,t)&&!Xd(e,t)}isSameDay(e,t){return Zd(e,t)}isSameMonth(e,t){return Qd(e,t)}isSameYear(e,t){return $d(e,t)}setMinutes(e,t){return nf(e,t)}setHours(e,t){return tf(e,t)}setMonth(e,t){return rf(e,t)}setDate(e,t){return af(e,t)}setYear(e,t){return sf(e,t)}getDiff(e,t,n){return ef(e,t,n)}getWeekdays(e,t){let n=e===void 0?void 0:Number(e);return Ed(this.locale,n,t)}getYear(e){return Fd(e)}getMonth(e){return Id(e)}getWeek(e,t,n){let r=t===void 0?void 0:Number(t);return Ld(e,this.locale,r,n)}getDate(e){return Rd(e)}getNextMonth(e){return zd(e)}getPreviousMonth(e){return Bd(e)}getHours(e){return Vd(e)}getMinutes(e){return Hd(e)}startOfDay(e){return cf(e)}endOfDay(e){return lf(e)}startOfYear(e){return Ud(e)}endOfYear(e){return Wd(e)}};const df=Symbol.for(`vuetify:date-options`),ff=Symbol.for(`vuetify:date-adapter`);function pf(e,t){let n=Jc({adapter:uf,locale:{af:`af-ZA`,bg:`bg-BG`,ca:`ca-ES`,ckb:``,cs:`cs-CZ`,de:`de-DE`,el:`el-GR`,en:`en-US`,et:`et-EE`,fa:`fa-IR`,fi:`fi-FI`,hr:`hr-HR`,hu:`hu-HU`,he:`he-IL`,id:`id-ID`,it:`it-IT`,ja:`ja-JP`,ko:`ko-KR`,lv:`lv-LV`,lt:`lt-LT`,nl:`nl-NL`,no:`no-NO`,pl:`pl-PL`,pt:`pt-PT`,ro:`ro-RO`,ru:`ru-RU`,sk:`sk-SK`,sl:`sl-SI`,srCyrl:`sr-SP`,srLatn:`sr-SP`,sv:`sv-SE`,th:`th-TH`,tr:`tr-TR`,az:`az-AZ`,uk:`uk-UA`,vi:`vi-VN`,zhHans:`zh-CN`,zhHant:`zh-TW`}},e);return{options:n,instance:mf(n,t)}}function mf(e,t){let n=Lt(typeof e.adapter==`function`?new e.adapter({locale:e.locale[t.current.value]??t.current.value,formats:e.formats}):e.adapter);return B(t.current,t=>{n.locale=e.locale[t]??t??n.locale}),n}const hf=[`sm`,`md`,`lg`,`xl`,`xxl`],gf=Symbol.for(`vuetify:display`),_f={mobileBreakpoint:`lg`,thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},vf=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:_f;return Jc(_f,e)};function yf(e){return K&&!e?window.innerWidth:typeof e==`object`&&e.clientWidth||0}function bf(e){return K&&!e?window.innerHeight:typeof e==`object`&&e.clientHeight||0}function xf(e){let t=K&&!e?window.navigator.userAgent:`ssr`;function n(e){return!!t.match(e)}let r=n(/android/i),i=n(/iphone|ipad|ipod/i),a=n(/cordova/i),o=n(/electron/i),s=n(/chrome/i),c=n(/edge/i),l=n(/firefox/i),u=n(/opera/i),d=n(/win/i),f=n(/mac/i),p=n(/linux/i);return{android:r,ios:i,cordova:a,electron:o,chrome:s,edge:c,firefox:l,opera:u,win:d,mac:f,linux:p,touch:_c,ssr:t===`ssr`}}function Sf(e,t){let{thresholds:n,mobileBreakpoint:r}=vf(e),i=F(bf(t)),a=F(xf(t)),o=Lt({}),s=F(yf(t));function c(){i.value=bf(),s.value=yf()}function l(){c(),a.value=xf()}return Gi(()=>{let e=s.value<n.sm,t=s.value<n.md&&!e,c=s.value<n.lg&&!(t||e),l=s.value<n.xl&&!(c||t||e),u=s.value<n.xxl&&!(l||c||t||e),d=s.value>=n.xxl,f=e?`xs`:t?`sm`:c?`md`:l?`lg`:u?`xl`:`xxl`,p=typeof r==`number`?r:n[r],m=s.value<p;o.xs=e,o.sm=t,o.md=c,o.lg=l,o.xl=u,o.xxl=d,o.smAndUp=!e,o.mdAndUp=!(e||t),o.lgAndUp=!(e||t||c),o.xlAndUp=!(e||t||c||l),o.smAndDown=!(c||l||u||d),o.mdAndDown=!(l||u||d),o.lgAndDown=!(u||d),o.xlAndDown=!d,o.name=f,o.height=i.value,o.width=s.value,o.mobile=m,o.mobileBreakpoint=r,o.platform=a.value,o.thresholds=n}),K&&(window.addEventListener(`resize`,c,{passive:!0}),De(()=>{window.removeEventListener(`resize`,c)},!0)),{...$t(o),update:l,ssr:!!t}}const Cf=J({mobile:{type:Boolean,default:!1},mobileBreakpoint:[Number,String]},`display`);function wf(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{mobile:null},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=z(gf);if(!n)throw Error(`Could not find Vuetify display injection`);let r=G(()=>e.mobile?!0:typeof e.mobileBreakpoint==`number`?n.width.value<e.mobileBreakpoint:e.mobileBreakpoint?n.width.value<n.thresholds.value[e.mobileBreakpoint]:e.mobile===null?n.mobile.value:!1),i=L(()=>t?{[`${t}--mobile`]:r.value}:{});return{...n,displayClasses:i,mobile:r}}const Tf=Symbol.for(`vuetify:goto`);function Ef(){return{container:void 0,duration:300,layout:!1,offset:0,easing:`easeInOutCubic`,patterns:{linear:e=>e,easeInQuad:e=>e**2,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e**2:-1+(4-2*e)*e,easeInCubic:e=>e**3,easeOutCubic:e=>--e**3+1,easeInOutCubic:e=>e<.5?4*e**3:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e**4,easeOutQuart:e=>1- --e**4,easeInOutQuart:e=>e<.5?8*e**4:1-8*--e**4,easeInQuint:e=>e**5,easeOutQuint:e=>1+--e**5,easeInOutQuint:e=>e<.5?16*e**5:1+16*--e**5}}}function Df(e){return Of(e)??(document.scrollingElement||document.body)}function Of(e){return typeof e==`string`?document.querySelector(e):jc(e)}function kf(e,t,n){if(typeof e==`number`)return t&&n?-e:e;let r=Of(e),i=0;for(;r;)i+=t?r.offsetLeft:r.offsetTop,r=r.offsetParent;return i}function Af(e,t){return{rtl:t.isRtl,options:Jc(Ef(),e)}}async function jf(e,t,n,r){let i=n?`scrollLeft`:`scrollTop`,a=Jc(r?.options??Ef(),t),o=r?.rtl.value,s=(typeof e==`number`?e:Of(e))??0,c=a.container===`parent`&&s instanceof HTMLElement?s.parentElement:Df(a.container),l=typeof a.easing==`function`?a.easing:a.patterns[a.easing];if(!l)throw TypeError(`Easing function "${a.easing}" not found.`);let u;if(typeof s==`number`)u=kf(s,n,o);else if(u=kf(s,n,o)-kf(c,n,o),a.layout){let e=window.getComputedStyle(s),t=e.getPropertyValue(`--v-layout-top`);t&&(u-=parseInt(t,10))}u+=a.offset,u=Nf(c,u,!!o,!!n);let d=c[i]??0;if(u===d)return Promise.resolve(u);let f=performance.now();return new Promise(e=>requestAnimationFrame(function t(n){let r=n-f,o=r/a.duration,s=Math.floor(d+(u-d)*l(Wc(o,0,1)));if(c[i]=s,o>=1&&Math.abs(s-c[i])<10)return e(u);if(o>2)return tu(`Scroll target is not reachable`),e(c[i]);requestAnimationFrame(t)}))}function Mf(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=z(Tf),{isRtl:n}=hd();if(!t)throw Error(`[Vuetify] Could not find injected goto instance`);let r={...t,rtl:L(()=>t.rtl.value||n.value)};async function i(t,n){return jf(t,Jc(e,n),!1,r)}return i.horizontal=async(t,n)=>jf(t,Jc(e,n),!0,r),i}function Nf(e,t,n,r){let{scrollWidth:i,scrollHeight:a}=e,[o,s]=e===document.scrollingElement?[window.innerWidth,window.innerHeight]:[e.offsetWidth,e.offsetHeight],c,l;return r?n?(c=-(i-o),l=0):(c=0,l=i-o):(c=0,l=a+-s),Wc(t,c,l)}const Pf={collapse:`mdi-chevron-up`,complete:`mdi-check`,cancel:`mdi-close-circle`,close:`mdi-close`,delete:`mdi-close-circle`,clear:`mdi-close-circle`,success:`mdi-check-circle`,info:`mdi-information`,warning:`mdi-alert-circle`,error:`mdi-close-circle`,prev:`mdi-chevron-left`,next:`mdi-chevron-right`,checkboxOn:`mdi-checkbox-marked`,checkboxOff:`mdi-checkbox-blank-outline`,checkboxIndeterminate:`mdi-minus-box`,delimiter:`mdi-circle`,sortAsc:`mdi-arrow-up`,sortDesc:`mdi-arrow-down`,expand:`mdi-chevron-down`,menu:`mdi-menu`,subgroup:`mdi-menu-down`,dropdown:`mdi-menu-down`,radioOn:`mdi-radiobox-marked`,radioOff:`mdi-radiobox-blank`,edit:`mdi-pencil`,ratingEmpty:`mdi-star-outline`,ratingFull:`mdi-star`,ratingHalf:`mdi-star-half-full`,loading:`mdi-cached`,first:`mdi-page-first`,last:`mdi-page-last`,unfold:`mdi-unfold-more-horizontal`,file:`mdi-paperclip`,plus:`mdi-plus`,minus:`mdi-minus`,calendar:`mdi-calendar`,treeviewCollapse:`mdi-menu-down`,treeviewExpand:`mdi-menu-right`,eyeDropper:`mdi-eyedropper`,upload:`mdi-cloud-upload`,color:`mdi-palette`,command:`mdi-apple-keyboard-command`,ctrl:`mdi-apple-keyboard-control`,space:`mdi-keyboard-space`,shift:`mdi-apple-keyboard-shift`,alt:`mdi-apple-keyboard-option`,enter:`mdi-keyboard-return`,arrowup:`mdi-arrow-up`,arrowdown:`mdi-arrow-down`,arrowleft:`mdi-arrow-left`,arrowright:`mdi-arrow-right`,backspace:`mdi-backspace`},Ff={component:e=>eo(Vf,{...e,class:`mdi`})},If=[String,Function,Object,Array],Lf=Symbol.for(`vuetify:icons`),Rf=J({icon:{type:If},tag:{type:[String,Object,Function],required:!0}},`icon`),zf=Y()({name:`VComponentIcon`,props:Rf(),setup(e,t){let{slots:n}=t;return()=>{let t=e.icon;return U(e.tag,null,{default:()=>[e.icon?U(t,null,null):n.default?.()]})}}}),Bf=Vu({name:`VSvgIcon`,inheritAttrs:!1,props:Rf(),setup(e,t){let{attrs:n}=t;return()=>U(e.tag,W(n,{style:null}),{default:()=>[H(`svg`,{class:`v-icon__svg`,xmlns:`http://www.w3.org/2000/svg`,viewBox:`0 0 24 24`,role:`img`,"aria-hidden":`true`},[Array.isArray(e.icon)?e.icon.map(e=>Array.isArray(e)?H(`path`,{d:e[0],"fill-opacity":e[1]},null):H(`path`,{d:e},null)):H(`path`,{d:e.icon},null)])]})}});Vu({name:`VLigatureIcon`,props:Rf(),setup(e){return()=>U(e.tag,null,{default:()=>[e.icon]})}});const Vf=Vu({name:`VClassIcon`,props:Rf(),setup(e){return()=>U(e.tag,{class:A(e.icon)},null)}});function Hf(){return{svg:{component:Bf},class:{component:Vf}}}function Uf(e){let t=Hf(),n=e?.defaultSet??`mdi`;return n===`mdi`&&!t.mdi&&(t.mdi=Ff),Jc({defaultSet:n,sets:t,aliases:{...Pf,vuetify:[`M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z`,[`M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z`,.6]],"vuetify-outline":`svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z`,"vuetify-play":[`m6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z`,[`M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z`,.6]]}},e)}const Wf=e=>{let t=z(Lf);if(!t)throw Error(`Missing Vuetify Icons provide!`);let n=G(()=>{let n=Xt(e);if(!n)return{component:zf};let r=n;if(typeof r==`string`&&(r=r.trim(),r.startsWith(`$`)&&(r=t.aliases?.[r.slice(1)])),r||tu(`Could not find aliased icon "${n}"`),Array.isArray(r))return{component:Bf,icon:r};if(typeof r!=`string`)return{component:zf,icon:r};let i=Object.keys(t.sets).find(e=>typeof r==`string`&&r.startsWith(`${e}:`)),a=i?r.slice(i.length+1):r,o=t.sets[i??t.defaultSet];return{component:o.component,icon:a}});return{iconData:n}},Gf=Symbol.for(`vuetify:theme`),Kf=J({theme:String},`theme`);function qf(){return{defaultTheme:`light`,prefix:`v-`,variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:`#FFFFFF`,surface:`#FFFFFF`,"surface-bright":`#FFFFFF`,"surface-light":`#EEEEEE`,"surface-variant":`#424242`,"on-surface-variant":`#EEEEEE`,primary:`#1867C0`,"primary-darken-1":`#1F5592`,secondary:`#48A9A6`,"secondary-darken-1":`#018786`,error:`#B00020`,info:`#2196F3`,success:`#4CAF50`,warning:`#FB8C00`},variables:{"border-color":`#000000`,"border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":`#EEEEEE`,"theme-on-kbd":`#000000`,"theme-code":`#F5F5F5`,"theme-on-code":`#000000`}},dark:{dark:!0,colors:{background:`#121212`,surface:`#212121`,"surface-bright":`#ccbfd6`,"surface-light":`#424242`,"surface-variant":`#c8c8c8`,"on-surface-variant":`#000000`,primary:`#2196F3`,"primary-darken-1":`#277CC1`,secondary:`#54B6B2`,"secondary-darken-1":`#48A9A6`,error:`#CF6679`,info:`#2196F3`,success:`#4CAF50`,warning:`#FB8C00`},variables:{"border-color":`#FFFFFF`,"border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":`#424242`,"theme-on-kbd":`#FFFFFF`,"theme-code":`#343434`,"theme-on-code":`#CCCCCC`}}},stylesheetId:`vuetify-theme-stylesheet`,scoped:!1,unimportant:!1,utilities:!0}}function Jf(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:qf(),t=qf();if(!e)return{...t,isDisabled:!0};let n={};for(let[r,i]of Object.entries(e.themes??{})){let e=i.dark||r===`dark`?t.themes?.dark:t.themes?.light;n[r]=Jc(e,i)}return Jc(t,{...e,themes:n})}function Yf(e,t,n,r){e.push(`${ep(t,r)} {\n`,...n.map(e=>`  ${e};\n`),`}
`)}function Xf(e,t){let n=e.dark?2:1,r=e.dark?1:2,i=[];for(let[a,o]of Object.entries(e.colors)){let e=yu(o);i.push(`--${t}theme-${a}: ${e.r},${e.g},${e.b}`),a.startsWith(`on-`)||i.push(`--${t}theme-${a}-overlay-multiplier: ${ku(o)>.18?n:r}`)}for(let[n,r]of Object.entries(e.variables)){let e=typeof r==`string`&&r.startsWith(`#`)?yu(r):void 0,a=e?`${e.r}, ${e.g}, ${e.b}`:void 0;i.push(`--${t}${n}: ${a??r}`)}return i}function Zf(e,t,n){let r={};if(n)for(let i of[`lighten`,`darken`]){let a=i===`lighten`?Du:Ou;for(let o of Oc(n[i],1))r[`${e}-${i}-${o}`]=wu(a(yu(t),o))}return r}function Qf(e,t){if(!t)return{};let n={};for(let r of t.colors){let i=e[r];if(!i)continue;n={...n,...Zf(r,i,t)}}return n}function $f(e){let t={};for(let n of Object.keys(e)){if(n.startsWith(`on-`)||e[`on-${n}`])continue;let r=`on-${n}`,i=yu(e[n]);t[r]=Au(i)}return t}function ep(e,t){if(!t)return e;let n=`:where(${t})`;return e===`:root`?n:`${n} ${e}`}function tp(e,t,n){let r=np(e,t);r&&(r.innerHTML=n)}function np(e,t){if(!K)return null;let n=document.getElementById(e);return n||(n=document.createElement(`style`),n.id=e,n.type=`text/css`,t&&n.setAttribute(`nonce`,t),document.head.appendChild(n)),n}function rp(e){let t=Jf(e),n=F(t.defaultTheme),r=P(t.themes),i=F(`light`),a=G({get(){return n.value===`system`?i.value:n.value},set(e){n.value=e}}),o=G(()=>{let e={};for(let[n,i]of Object.entries(r.value)){let r={...i.colors,...Qf(i.colors,t.variations)};e[n]={...i,colors:{...r,...$f(r)}}}return e}),s=L(()=>o.value[a.value]),c=G(()=>{let e=[],n=t.unimportant?``:` !important`,r=t.scoped?t.prefix:``;s.value?.dark&&Yf(e,`:root`,[`color-scheme: dark`],t.scope),Yf(e,`:root`,Xf(s.value,t.prefix),t.scope);for(let[n,r]of Object.entries(o.value))Yf(e,`.${t.prefix}theme--${n}`,[`color-scheme: ${r.dark?`dark`:`normal`}`,...Xf(r,t.prefix)],t.scope);if(t.utilities){let i=[],a=[],s=new Set(Object.values(o.value).flatMap(e=>Object.keys(e.colors)));for(let e of s)e.startsWith(`on-`)?Yf(a,`.${e}`,[`color: rgb(var(--${t.prefix}theme-${e}))${n}`],t.scope):(Yf(i,`.${r}bg-${e}`,[`--${t.prefix}theme-overlay-multiplier: var(--${t.prefix}theme-${e}-overlay-multiplier)`,`background-color: rgb(var(--${t.prefix}theme-${e}))${n}`,`color: rgb(var(--${t.prefix}theme-on-${e}))${n}`],t.scope),Yf(a,`.${r}text-${e}`,[`color: rgb(var(--${t.prefix}theme-${e}))${n}`],t.scope),Yf(a,`.${r}border-${e}`,[`--${t.prefix}border-color: var(--${t.prefix}theme-${e})`],t.scope));e.push(...i,...a)}return e.map((e,t)=>t===0?e:`    ${e}`).join(``)}),l=L(()=>t.isDisabled?void 0:`${t.prefix}theme--${a.value}`),u=L(()=>Object.keys(o.value));if(vc){let e=window.matchMedia(`(prefers-color-scheme: dark)`);function t(){i.value=e.matches?`dark`:`light`}t(),e.addEventListener(`change`,t,{passive:!0}),Ee()&&De(()=>{e.removeEventListener(`change`,t)})}function d(e){if(t.isDisabled)return;let n=e._context.provides.usehead;if(n){function e(){return{style:[{textContent:c.value,id:t.stylesheetId,nonce:t.cspNonce||!1}]}}if(n.push){let t=n.push(e);K&&B(c,()=>{t.patch(e)})}else K?(n.addHeadObjs(L(e)),Gi(()=>n.updateDOM())):n.addHeadObjs(e())}else{K?B(c,e,{immediate:!0}):e();function e(){tp(t.stylesheetId,t.cspNonce,c.value)}}}function f(e){if(!u.value.includes(e)){tu(`Theme "${e}" not found on the Vuetify theme instance`);return}a.value=e}function p(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:u.value,t=e.indexOf(a.value),n=t===-1?0:(t+1)%e.length;f(e[n])}function m(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[`light`,`dark`];p(e)}let h=new Proxy(a,{get(e,t){return Reflect.get(e,t)},set(e,t,n){return t===`value`&&ru(`theme.global.name.value = ${n}`,`theme.change('${n}')`),Reflect.set(e,t,n)}});return{install:d,change:f,cycle:p,toggle:m,isDisabled:t.isDisabled,name:a,themes:r,current:s,computedThemes:o,prefix:t.prefix,themeClasses:l,styles:c,global:{name:h,current:s}}}function ip(e){Mu(`provideTheme`);let t=z(Gf,null);if(!t)throw Error(`Could not find Vuetify theme injection`);let n=L(()=>e.theme??t.name.value),r=L(()=>t.themes.value[n.value]),i=L(()=>t.isDisabled?void 0:`${t.prefix}theme--${n.value}`),a={...t,name:n,current:r,themeClasses:i};return pi(Gf,a),a}function ap(){Mu(`useTheme`);let e=z(Gf,null);if(!e)throw Error(`Could not find Vuetify theme injection`);return e}function op(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:`content`,n=hl(),r=P();if(K){let i=new ResizeObserver(n=>{e?.(n,i),n.length&&(t===`content`?r.value=n[0].contentRect:r.value=n[0].target.getBoundingClientRect())});Dr(()=>{i.disconnect()}),B(()=>n.el,(e,t)=>{t&&(i.unobserve(t),r.value=void 0),e&&i.observe(e)},{flush:`post`})}return{resizeRef:n,contentRect:zt(r)}}const sp=Symbol.for(`vuetify:layout`),cp=Symbol.for(`vuetify:layout-item`),lp=1e3,up=J({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},`layout`),dp=J({name:{type:String},order:{type:[Number,String],default:0},absolute:Boolean},`layout-item`);function fp(){let e=z(sp);if(!e)throw Error(`[Vuetify] Could not find injected layout`);return{getLayoutItem:e.getLayoutItem,mainRect:e.mainRect,mainStyles:e.mainStyles}}function pp(e){let t=z(sp);if(!t)throw Error(`[Vuetify] Could not find injected layout`);let n=e.id??`layout-item-${fr()}`,r=Mu(`useLayoutItem`);pi(cp,{id:n});let i=F(!1);vr(()=>i.value=!0),_r(()=>i.value=!1);let{layoutItemStyles:a,layoutItemScrimStyles:o}=t.register(r,{...e,active:G(()=>i.value?!1:e.active.value),id:n});return Dr(()=>t.unregister(n)),{layoutItemStyles:a,layoutRect:t.layoutRect,layoutItemScrimStyles:o}}const mp=(e,t,n,r)=>{let i={top:0,left:0,right:0,bottom:0},a=[{id:``,layer:{...i}}];for(let o of e){let e=t.get(o),s=n.get(o),c=r.get(o);if(!e||!s||!c)continue;let l={...i,[e.value]:parseInt(i[e.value],10)+(c.value?parseInt(s.value,10):0)};a.push({id:o,layer:l}),i=l}return a};function hp(e){let t=z(sp,null),n=G(()=>t?t.rootZIndex.value-100:lp),r=P([]),i=Lt(new Map),a=Lt(new Map),o=Lt(new Map),s=Lt(new Map),c=Lt(new Map),{resizeRef:l,contentRect:u}=op(),d=G(()=>{let t=new Map,n=e.overlaps??[];for(let e of n.filter(e=>e.includes(`:`))){let[n,o]=e.split(`:`);if(!r.value.includes(n)||!r.value.includes(o))continue;let s=i.get(n),c=i.get(o),l=a.get(n),u=a.get(o);if(!s||!c||!l||!u)continue;t.set(o,{position:s.value,amount:parseInt(l.value,10)}),t.set(n,{position:c.value,amount:-parseInt(u.value,10)})}return t}),f=G(()=>{let e=[...new Set([...o.values()].map(e=>e.value))].sort((e,t)=>e-t),t=[];for(let n of e){let e=r.value.filter(e=>o.get(e)?.value===n);t.push(...e)}return mp(t,i,a,s)}),p=G(()=>!Array.from(c.values()).some(e=>e.value)),m=G(()=>f.value[f.value.length-1].layer),h=L(()=>({"--v-layout-left":q(m.value.left),"--v-layout-right":q(m.value.right),"--v-layout-top":q(m.value.top),"--v-layout-bottom":q(m.value.bottom),...p.value?void 0:{transition:`none`}})),g=G(()=>f.value.slice(1).map((e,t)=>{let{id:n}=e,{layer:r}=f.value[t],o=a.get(n),s=i.get(n);return{id:n,...r,size:Number(o.value),position:s.value}})),_=e=>g.value.find(t=>t.id===e),v=Mu(`createLayout`),y=F(!1);wr(()=>{y.value=!0}),pi(sp,{register:(e,t)=>{let{id:l,order:u,position:m,layoutSize:h,elementSize:_,active:b,disableTransitions:x,absolute:S}=t;o.set(l,u),i.set(l,m),a.set(l,h),s.set(l,b),x&&c.set(l,x);let C=Zc(cp,v?.vnode),w=C.indexOf(e);w>-1?r.value.splice(w,0,l):r.value.push(l);let T=G(()=>g.value.findIndex(e=>e.id===l)),E=G(()=>n.value+f.value.length*2-T.value*2),ee=G(()=>{let e=m.value===`left`||m.value===`right`,t=m.value===`right`,r=m.value===`bottom`,i=_.value??h.value,a=i===0?`%`:`px`,o={[m.value]:0,zIndex:E.value,transform:`translate${e?`X`:`Y`}(${(b.value?0:-(i===0?100:i))*(t||r?-1:1)}${a})`,position:S.value||n.value!==lp?`absolute`:`fixed`,...p.value?void 0:{transition:`none`}};if(!y.value)return o;let s=g.value[T.value];s||tu(`[Vuetify] Could not find layout item "${l}"`);let c=d.value.get(l);return c&&(s[c.position]+=c.amount),{...o,height:e?`calc(100% - ${s.top}px - ${s.bottom}px)`:_.value?`${_.value}px`:void 0,left:t?void 0:`${s.left}px`,right:t?`${s.right}px`:void 0,top:m.value===`bottom`?void 0:`${s.top}px`,bottom:m.value===`top`?void 0:`${s.bottom}px`,width:e?_.value?`${_.value}px`:void 0:`calc(100% - ${s.left}px - ${s.right}px)`}}),te=G(()=>({zIndex:E.value-1}));return{layoutItemStyles:ee,layoutItemScrimStyles:te,zIndex:E}},unregister:e=>{o.delete(e),i.delete(e),a.delete(e),s.delete(e),c.delete(e),r.value=r.value.filter(t=>t!==e)},mainRect:m,mainStyles:h,getLayoutItem:_,items:g,layoutRect:u,rootZIndex:n});let b=L(()=>[`v-layout`,{"v-layout--full-height":e.fullHeight}]),x=L(()=>({zIndex:t?n.value:void 0,position:t?`relative`:void 0,overflow:t?`hidden`:void 0}));return{layoutClasses:b,layoutStyles:x,getLayoutItem:_,items:g,layoutRect:u,layoutRef:l}}function gp(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{blueprint:t,...n}=e,r=Jc(t,n),{aliases:i={},components:a={},directives:o={}}=r,s=Te();return s.run(()=>{let e=Iu(r.defaults),t=Sf(r.display,r.ssr),n=rp(r.theme),c=Uf(r.icons),l=dd(r.locale),u=pf(r.date,l),d=Af(r.goTo,l);function f(s){for(let e in o)s.directive(e,o[e]);for(let e in a)s.component(e,a[e]);for(let e in i)s.component(e,Vu({...i[e],name:e,aliasName:i[e].name}));let f=Te();if(f.run(()=>{n.install(s)}),s.onUnmount(()=>f.stop()),s.provide(Fu,e),s.provide(gf,t),s.provide(Gf,n),s.provide(Lf,c),s.provide(ld,l),s.provide(df,u.options),s.provide(ff,u.instance),s.provide(Tf,d),K&&r.ssr)if(s.$nuxt)s.$nuxt.hook(`app:suspense:resolve`,()=>{t.update()});else{let{mount:e}=s;s.mount=function(){let n=e(...arguments);return Cn(()=>t.update()),s.mount=e,n}}s.mixin({computed:{$vuetify(){return Lt({defaults:vp.call(this,Fu),display:vp.call(this,gf),theme:vp.call(this,Gf),icons:vp.call(this,Lf),locale:vp.call(this,ld),date:vp.call(this,ff)})}}})}function p(){s.stop()}return{install:f,unmount:p,defaults:e,display:t,theme:n,icons:c,locale:l,date:u,goTo:d}})}const _p=`3.9.2`;gp.version=_p;function vp(e){let t=this.$,n=t.parent?.provides??t.vnode.appContext?.provides;if(n&&e in n)return n[e]}function yp(e,t){return function(){return e.apply(t,arguments)}}const{toString:bp}=Object.prototype,{getPrototypeOf:xp}=Object,{iterator:Sp,toStringTag:Cp}=Symbol,wp=(e=>t=>{let n=bp.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Tp=e=>(e=e.toLowerCase(),t=>wp(t)===e),Ep=e=>t=>typeof t===e,{isArray:Dp}=Array,Op=Ep(`undefined`);function kp(e){return e!==null&&!Op(e)&&e.constructor!==null&&!Op(e.constructor)&&Np(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ap=Tp(`ArrayBuffer`);function jp(e){let t;return t=typeof ArrayBuffer<`u`&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Ap(e.buffer),t}const Mp=Ep(`string`),Np=Ep(`function`),Pp=Ep(`number`),Fp=e=>typeof e==`object`&&!!e,Ip=e=>e===!0||e===!1,Lp=e=>{if(wp(e)!==`object`)return!1;let t=xp(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Cp in e)&&!(Sp in e)},Rp=e=>{if(!Fp(e)||kp(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},zp=Tp(`Date`),Bp=Tp(`File`),Vp=Tp(`Blob`),Hp=Tp(`FileList`),Up=e=>Fp(e)&&Np(e.pipe),Wp=e=>{let t;return e&&(typeof FormData==`function`&&e instanceof FormData||Np(e.append)&&((t=wp(e))===`formdata`||t===`object`&&Np(e.toString)&&e.toString()===`[object FormData]`))},Gp=Tp(`URLSearchParams`),[Kp,qp,Jp,Yp]=[`ReadableStream`,`Request`,`Response`,`Headers`].map(Tp),Xp=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,``);function Zp(e,t,{allOwnKeys:n=!1}={}){if(e==null)return;let r,i;if(typeof e!=`object`&&(e=[e]),Dp(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{if(kp(e))return;let i=n?Object.getOwnPropertyNames(e):Object.keys(e),a=i.length,o;for(r=0;r<a;r++)o=i[r],t.call(null,e[o],o,e)}}function Qp(e,t){if(kp(e))return null;t=t.toLowerCase();let n=Object.keys(e),r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const $p=(()=>typeof globalThis<`u`?globalThis:typeof self<`u`?self:typeof window<`u`?window:global)(),em=e=>!Op(e)&&e!==$p;function tm(){let{caseless:e}=em(this)&&this||{},t={},n=(n,r)=>{let i=e&&Qp(t,r)||r;Lp(t[i])&&Lp(n)?t[i]=tm(t[i],n):Lp(n)?t[i]=tm({},n):Dp(n)?t[i]=n.slice():t[i]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&Zp(arguments[e],n);return t}const nm=(e,t,n,{allOwnKeys:r}={})=>(Zp(t,(t,r)=>{n&&Np(t)?e[r]=yp(t,n):e[r]=t},{allOwnKeys:r}),e),rm=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),im=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,`super`,{value:t.prototype}),n&&Object.assign(e.prototype,n)},am=(e,t,n,r)=>{let i,a,o,s={};if(t||={},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),a=i.length;a-- >0;)o=i[a],(!r||r(o,e,t))&&!s[o]&&(t[o]=e[o],s[o]=!0);e=n!==!1&&xp(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},om=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;let r=e.indexOf(t,n);return r!==-1&&r===n},sm=e=>{if(!e)return null;if(Dp(e))return e;let t=e.length;if(!Pp(t))return null;let n=Array(t);for(;t-- >0;)n[t]=e[t];return n},cm=(e=>t=>e&&t instanceof e)(typeof Uint8Array<`u`&&xp(Uint8Array)),lm=(e,t)=>{let n=e&&e[Sp],r=n.call(e),i;for(;(i=r.next())&&!i.done;){let n=i.value;t.call(e,n[0],n[1])}},um=(e,t)=>{let n,r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},dm=Tp(`HTMLFormElement`),fm=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),pm=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),mm=Tp(`RegExp`),hm=(e,t)=>{let n=Object.getOwnPropertyDescriptors(e),r={};Zp(n,(n,i)=>{let a;(a=t(n,i,e))!==!1&&(r[i]=a||n)}),Object.defineProperties(e,r)},gm=e=>{hm(e,(t,n)=>{if(Np(e)&&[`arguments`,`caller`,`callee`].indexOf(n)!==-1)return!1;let r=e[n];if(Np(r)){if(t.enumerable=!1,`writable`in t){t.writable=!1;return}t.set||=()=>{throw Error(`Can not rewrite read-only method '`+n+`'`)}}})},_m=(e,t)=>{let n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Dp(e)?r(e):r(String(e).split(t)),n},vm=()=>{},ym=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function bm(e){return!!(e&&Np(e.append)&&e[Cp]===`FormData`&&e[Sp])}const xm=e=>{let t=Array(10),n=(e,r)=>{if(Fp(e)){if(t.indexOf(e)>=0)return;if(kp(e))return e;if(!(`toJSON`in e)){t[r]=e;let i=Dp(e)?[]:{};return Zp(e,(e,t)=>{let a=n(e,r+1);!Op(a)&&(i[t]=a)}),t[r]=void 0,i}}return e};return n(e,0)},Sm=Tp(`AsyncFunction`),Cm=e=>e&&(Fp(e)||Np(e))&&Np(e.then)&&Np(e.catch),wm=((e,t)=>e?setImmediate:t?((e,t)=>($p.addEventListener(`message`,({source:n,data:r})=>{n===$p&&r===e&&t.length&&t.shift()()},!1),n=>{t.push(n),$p.postMessage(e,`*`)}))(`axios@${Math.random()}`,[]):e=>setTimeout(e))(typeof setImmediate==`function`,Np($p.postMessage)),Tm=typeof queueMicrotask<`u`?queueMicrotask.bind($p):typeof process<`u`&&process.nextTick||wm,Em=e=>e!=null&&Np(e[Sp]);var Z={isArray:Dp,isArrayBuffer:Ap,isBuffer:kp,isFormData:Wp,isArrayBufferView:jp,isString:Mp,isNumber:Pp,isBoolean:Ip,isObject:Fp,isPlainObject:Lp,isEmptyObject:Rp,isReadableStream:Kp,isRequest:qp,isResponse:Jp,isHeaders:Yp,isUndefined:Op,isDate:zp,isFile:Bp,isBlob:Vp,isRegExp:mm,isFunction:Np,isStream:Up,isURLSearchParams:Gp,isTypedArray:cm,isFileList:Hp,forEach:Zp,merge:tm,extend:nm,trim:Xp,stripBOM:rm,inherits:im,toFlatObject:am,kindOf:wp,kindOfTest:Tp,endsWith:om,toArray:sm,forEachEntry:lm,matchAll:um,isHTMLForm:dm,hasOwnProperty:pm,hasOwnProp:pm,reduceDescriptors:hm,freezeMethods:gm,toObjectSet:_m,toCamelCase:fm,noop:vm,toFiniteNumber:ym,findKey:Qp,global:$p,isContextDefined:em,isSpecCompliantForm:bm,toJSONObject:xm,isAsyncFn:Sm,isThenable:Cm,setImmediate:wm,asap:Tm,isIterable:Em};function Dm(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name=`AxiosError`,t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}Z.inherits(Dm,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Z.toJSONObject(this.config),code:this.code,status:this.status}}});const Om=Dm.prototype,km={};[`ERR_BAD_OPTION_VALUE`,`ERR_BAD_OPTION`,`ECONNABORTED`,`ETIMEDOUT`,`ERR_NETWORK`,`ERR_FR_TOO_MANY_REDIRECTS`,`ERR_DEPRECATED`,`ERR_BAD_RESPONSE`,`ERR_BAD_REQUEST`,`ERR_CANCELED`,`ERR_NOT_SUPPORT`,`ERR_INVALID_URL`].forEach(e=>{km[e]={value:e}}),Object.defineProperties(Dm,km),Object.defineProperty(Om,`isAxiosError`,{value:!0}),Dm.from=(e,t,n,r,i,a)=>{let o=Object.create(Om);return Z.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>e!==`isAxiosError`),Dm.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,a&&Object.assign(o,a),o};var Q=Dm,Am=null;function jm(e){return Z.isPlainObject(e)||Z.isArray(e)}function Mm(e){return Z.endsWith(e,`[]`)?e.slice(0,-2):e}function Nm(e,t,n){return e?e.concat(t).map(function(e,t){return e=Mm(e),!n&&t?`[`+e+`]`:e}).join(n?`.`:``):t}function Pm(e){return Z.isArray(e)&&!e.some(jm)}const Fm=Z.toFlatObject(Z,{},null,function(e){return/^is[A-Z]/.test(e)});function Im(e,t,n){if(!Z.isObject(e))throw TypeError(`target must be an object`);t||=new(Am||FormData),n=Z.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Z.isUndefined(t[e])});let r=n.metaTokens,i=n.visitor||u,a=n.dots,o=n.indexes,s=n.Blob||typeof Blob<`u`&&Blob,c=s&&Z.isSpecCompliantForm(t);if(!Z.isFunction(i))throw TypeError(`visitor must be a function`);function l(e){if(e===null)return``;if(Z.isDate(e))return e.toISOString();if(Z.isBoolean(e))return e.toString();if(!c&&Z.isBlob(e))throw new Q(`Blob is not supported. Use a Buffer instead.`);return Z.isArrayBuffer(e)||Z.isTypedArray(e)?c&&typeof Blob==`function`?new Blob([e]):Buffer.from(e):e}function u(e,n,i){let s=e;if(e&&!i&&typeof e==`object`){if(Z.endsWith(n,`{}`))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Z.isArray(e)&&Pm(e)||(Z.isFileList(e)||Z.endsWith(n,`[]`))&&(s=Z.toArray(e)))return n=Mm(n),s.forEach(function(e,r){!(Z.isUndefined(e)||e===null)&&t.append(o===!0?Nm([n],r,a):o===null?n:n+`[]`,l(e))}),!1}return jm(e)?!0:(t.append(Nm(i,n,a),l(e)),!1)}let d=[],f=Object.assign(Fm,{defaultVisitor:u,convertValue:l,isVisitable:jm});function p(e,n){if(!Z.isUndefined(e)){if(d.indexOf(e)!==-1)throw Error(`Circular reference detected in `+n.join(`.`));d.push(e),Z.forEach(e,function(e,r){let a=!(Z.isUndefined(e)||e===null)&&i.call(t,e,Z.isString(r)?r.trim():r,n,f);a===!0&&p(e,n?n.concat(r):[r])}),d.pop()}}if(!Z.isObject(e))throw TypeError(`data must be an object`);return p(e),t}var Lm=Im;function Rm(e){let t={"!":`%21`,"'":`%27`,"(":`%28`,")":`%29`,"~":`%7E`,"%20":`+`,"%00":`\0`};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function zm(e,t){this._pairs=[],e&&Lm(e,this,t)}const Bm=zm.prototype;Bm.append=function(e,t){this._pairs.push([e,t])},Bm.toString=function(e){let t=e?function(t){return e.call(this,t,Rm)}:Rm;return this._pairs.map(function(e){return t(e[0])+`=`+t(e[1])},``).join(`&`)};var Vm=zm;function Hm(e){return encodeURIComponent(e).replace(/%3A/gi,`:`).replace(/%24/g,`$`).replace(/%2C/gi,`,`).replace(/%20/g,`+`).replace(/%5B/gi,`[`).replace(/%5D/gi,`]`)}function Um(e,t,n){if(!t)return e;let r=n&&n.encode||Hm;Z.isFunction(n)&&(n={serialize:n});let i=n&&n.serialize,a;if(a=i?i(t,n):Z.isURLSearchParams(t)?t.toString():new Vm(t,n).toString(r),a){let t=e.indexOf(`#`);t!==-1&&(e=e.slice(0,t)),e+=(e.indexOf(`?`)===-1?`?`:`&`)+a}return e}var Wm=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&=[]}forEach(e){Z.forEach(this.handlers,function(t){t!==null&&e(t)})}},Gm=Wm,Km={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},qm=typeof URLSearchParams<`u`?URLSearchParams:Vm,Jm=typeof FormData<`u`?FormData:null,Ym=typeof Blob<`u`?Blob:null,Xm={isBrowser:!0,classes:{URLSearchParams:qm,FormData:Jm,Blob:Ym},protocols:[`http`,`https`,`file`,`blob`,`url`,`data`]},Zm={};t(Zm,{hasBrowserEnv:()=>Qm,hasStandardBrowserEnv:()=>eh,hasStandardBrowserWebWorkerEnv:()=>th,navigator:()=>$m,origin:()=>nh});const Qm=typeof window<`u`&&typeof document<`u`,$m=typeof navigator==`object`&&navigator||void 0,eh=Qm&&(!$m||[`ReactNative`,`NativeScript`,`NS`].indexOf($m.product)<0),th=(()=>typeof WorkerGlobalScope<`u`&&self instanceof WorkerGlobalScope&&typeof self.importScripts==`function`)(),nh=Qm&&window.location.href||`http://localhost`;var rh={...Zm,...Xm};function ih(e,t){return Lm(e,new rh.classes.URLSearchParams,{visitor:function(e,t,n,r){return rh.isNode&&Z.isBuffer(e)?(this.append(t,e.toString(`base64`)),!1):r.defaultVisitor.apply(this,arguments)},...t})}function ah(e){return Z.matchAll(/\w+|\[(\w*)]/g,e).map(e=>e[0]===`[]`?``:e[1]||e[0])}function oh(e){let t={},n=Object.keys(e),r,i=n.length,a;for(r=0;r<i;r++)a=n[r],t[a]=e[a];return t}function sh(e){function t(e,n,r,i){let a=e[i++];if(a===`__proto__`)return!0;let o=Number.isFinite(+a),s=i>=e.length;if(a=!a&&Z.isArray(r)?r.length:a,s)return Z.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!o;(!r[a]||!Z.isObject(r[a]))&&(r[a]=[]);let c=t(e,n,r[a],i);return c&&Z.isArray(r[a])&&(r[a]=oh(r[a])),!o}if(Z.isFormData(e)&&Z.isFunction(e.entries)){let n={};return Z.forEachEntry(e,(e,r)=>{t(ah(e),r,n,0)}),n}return null}var ch=sh;function lh(e,t,n){if(Z.isString(e))try{return(t||JSON.parse)(e),Z.trim(e)}catch(e){if(e.name!==`SyntaxError`)throw e}return(n||JSON.stringify)(e)}const uh={transitional:Km,adapter:[`xhr`,`http`,`fetch`],transformRequest:[function(e,t){let n=t.getContentType()||``,r=n.indexOf(`application/json`)>-1,i=Z.isObject(e);i&&Z.isHTMLForm(e)&&(e=new FormData(e));let a=Z.isFormData(e);if(a)return r?JSON.stringify(ch(e)):e;if(Z.isArrayBuffer(e)||Z.isBuffer(e)||Z.isStream(e)||Z.isFile(e)||Z.isBlob(e)||Z.isReadableStream(e))return e;if(Z.isArrayBufferView(e))return e.buffer;if(Z.isURLSearchParams(e))return t.setContentType(`application/x-www-form-urlencoded;charset=utf-8`,!1),e.toString();let o;if(i){if(n.indexOf(`application/x-www-form-urlencoded`)>-1)return ih(e,this.formSerializer).toString();if((o=Z.isFileList(e))||n.indexOf(`multipart/form-data`)>-1){let t=this.env&&this.env.FormData;return Lm(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType(`application/json`,!1),lh(e)):e}],transformResponse:[function(e){let t=this.transitional||uh.transitional,n=t&&t.forcedJSONParsing,r=this.responseType===`json`;if(Z.isResponse(e)||Z.isReadableStream(e))return e;if(e&&Z.isString(e)&&(n&&!this.responseType||r)){let n=t&&t.silentJSONParsing,i=!n&&r;try{return JSON.parse(e)}catch(e){if(i)throw e.name===`SyntaxError`?Q.from(e,Q.ERR_BAD_RESPONSE,this,null,this.response):e}}return e}],timeout:0,xsrfCookieName:`XSRF-TOKEN`,xsrfHeaderName:`X-XSRF-TOKEN`,maxContentLength:-1,maxBodyLength:-1,env:{FormData:rh.classes.FormData,Blob:rh.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:`application/json, text/plain, */*`,"Content-Type":void 0}}};Z.forEach([`delete`,`get`,`head`,`post`,`put`,`patch`],e=>{uh.headers[e]={}});var dh=uh;const fh=Z.toObjectSet([`age`,`authorization`,`content-length`,`content-type`,`etag`,`expires`,`from`,`host`,`if-modified-since`,`if-unmodified-since`,`last-modified`,`location`,`max-forwards`,`proxy-authorization`,`referer`,`retry-after`,`user-agent`]);var ph=e=>{let t={},n,r,i;return e&&e.split(`
`).forEach(function(e){i=e.indexOf(`:`),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!(!n||t[n]&&fh[n])&&(n===`set-cookie`?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+`, `+r:r)}),t};const mh=Symbol(`internals`);function hh(e){return e&&String(e).trim().toLowerCase()}function gh(e){return e===!1||e==null?e:Z.isArray(e)?e.map(gh):String(e)}function _h(e){let t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const vh=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function yh(e,t,n,r,i){if(Z.isFunction(r))return r.call(this,t,n);if(i&&(t=n),Z.isString(t)){if(Z.isString(r))return t.indexOf(r)!==-1;if(Z.isRegExp(r))return r.test(t)}}function bh(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}function xh(e,t){let n=Z.toCamelCase(` `+t);[`get`,`set`,`has`].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})})}var Sh=class{constructor(e){e&&this.set(e)}set(e,t,n){let r=this;function i(e,t,n){let i=hh(t);if(!i)throw Error(`header name must be a non-empty string`);let a=Z.findKey(r,i);(!a||r[a]===void 0||n===!0||n===void 0&&r[a]!==!1)&&(r[a||t]=gh(e))}let a=(e,t)=>Z.forEach(e,(e,n)=>i(e,n,t));if(Z.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(Z.isString(e)&&(e=e.trim())&&!vh(e))a(ph(e),t);else if(Z.isObject(e)&&Z.isIterable(e)){let n={},r,i;for(let t of e){if(!Z.isArray(t))throw TypeError(`Object iterator must return a key-value pair`);n[i=t[0]]=(r=n[i])?Z.isArray(r)?[...r,t[1]]:[r,t[1]]:t[1]}a(n,t)}else e!=null&&i(t,e,n);return this}get(e,t){if(e=hh(e),e){let n=Z.findKey(this,e);if(n){let e=this[n];if(!t)return e;if(t===!0)return _h(e);if(Z.isFunction(t))return t.call(this,e,n);if(Z.isRegExp(t))return t.exec(e);throw TypeError(`parser must be boolean|regexp|function`)}}}has(e,t){if(e=hh(e),e){let n=Z.findKey(this,e);return!!(n&&this[n]!==void 0&&(!t||yh(this,this[n],n,t)))}return!1}delete(e,t){let n=this,r=!1;function i(e){if(e=hh(e),e){let i=Z.findKey(n,e);i&&(!t||yh(n,n[i],i,t))&&(delete n[i],r=!0)}}return Z.isArray(e)?e.forEach(i):i(e),r}clear(e){let t=Object.keys(this),n=t.length,r=!1;for(;n--;){let i=t[n];(!e||yh(this,this[i],i,e,!0))&&(delete this[i],r=!0)}return r}normalize(e){let t=this,n={};return Z.forEach(this,(r,i)=>{let a=Z.findKey(n,i);if(a){t[a]=gh(r),delete t[i];return}let o=e?bh(i):String(i).trim();o!==i&&delete t[i],t[o]=gh(r),n[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return Z.forEach(this,(n,r)=>{n!=null&&n!==!1&&(t[r]=e&&Z.isArray(n)?n.join(`, `):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+`: `+t).join(`
`)}getSetCookie(){return this.get(`set-cookie`)||[]}get[Symbol.toStringTag](){return`AxiosHeaders`}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){let t=this[mh]=this[mh]={accessors:{}},n=t.accessors,r=this.prototype;function i(e){let t=hh(e);n[t]||(xh(r,e),n[t]=!0)}return Z.isArray(e)?e.forEach(i):i(e),this}};Sh.accessor([`Content-Type`,`Content-Length`,`Accept`,`Accept-Encoding`,`User-Agent`,`Authorization`]),Z.reduceDescriptors(Sh.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),Z.freezeMethods(Sh);var Ch=Sh;function wh(e,t){let n=this||dh,r=t||n,i=Ch.from(r.headers),a=r.data;return Z.forEach(e,function(e){a=e.call(n,a,i.normalize(),t?t.status:void 0)}),i.normalize(),a}function Th(e){return!!(e&&e.__CANCEL__)}function Eh(e,t,n){Q.call(this,e??`canceled`,Q.ERR_CANCELED,t,n),this.name=`CanceledError`}Z.inherits(Eh,Q,{__CANCEL__:!0});var Dh=Eh;function Oh(e,t,n){let r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Q(`Request failed with status code `+n.status,[Q.ERR_BAD_REQUEST,Q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function kh(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||``}function Ah(e,t){e||=10;let n=Array(e),r=Array(e),i=0,a=0,o;return t=t===void 0?1e3:t,function(s){let c=Date.now(),l=r[a];o||=c,n[i]=s,r[i]=c;let u=a,d=0;for(;u!==i;)d+=n[u++],u%=e;if(i=(i+1)%e,i===a&&(a=(a+1)%e),c-o<t)return;let f=l&&c-l;return f?Math.round(d*1e3/f):void 0}}var jh=Ah;function Mh(e,t){let n=0,r=1e3/t,i,a,o=(t,r=Date.now())=>{n=r,i=null,a&&(clearTimeout(a),a=null),e(...t)},s=(...e)=>{let t=Date.now(),s=t-n;s>=r?o(e,t):(i=e,a||=setTimeout(()=>{a=null,o(i)},r-s))},c=()=>i&&o(i);return[s,c]}var Nh=Mh;const Ph=(e,t,n=3)=>{let r=0,i=jh(50,250);return Nh(n=>{let a=n.loaded,o=n.lengthComputable?n.total:void 0,s=a-r,c=i(s),l=a<=o;r=a;let u={loaded:a,total:o,progress:o?a/o:void 0,bytes:s,rate:c||void 0,estimated:c&&o&&l?(o-a)/c:void 0,event:n,lengthComputable:o!=null,[t?`download`:`upload`]:!0};e(u)},n)},Fh=(e,t)=>{let n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ih=e=>(...t)=>Z.asap(()=>e(...t));var Lh=rh.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,rh.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(rh.origin),rh.navigator&&/(msie|trident)/i.test(rh.navigator.userAgent)):()=>!0,Rh=rh.hasStandardBrowserEnv?{write(e,t,n,r,i,a){let o=[e+`=`+encodeURIComponent(t)];Z.isNumber(n)&&o.push(`expires=`+new Date(n).toGMTString()),Z.isString(r)&&o.push(`path=`+r),Z.isString(i)&&o.push(`domain=`+i),a===!0&&o.push(`secure`),document.cookie=o.join(`; `)},read(e){let t=document.cookie.match(RegExp(`(^|;\\s*)(`+e+`)=([^;]*)`));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,``,Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function zh(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bh(e,t){return t?e.replace(/\/?\/$/,``)+`/`+t.replace(/^\/+/,``):e}function Vh(e,t,n){let r=!zh(t);return e&&(r||n==0)?Bh(e,t):t}const Hh=e=>e instanceof Ch?{...e}:e;function Uh(e,t){t||={};let n={};function r(e,t,n,r){return Z.isPlainObject(e)&&Z.isPlainObject(t)?Z.merge.call({caseless:r},e,t):Z.isPlainObject(t)?Z.merge({},t):Z.isArray(t)?t.slice():t}function i(e,t,n,i){if(Z.isUndefined(t)){if(!Z.isUndefined(e))return r(void 0,e,n,i)}else return r(e,t,n,i)}function a(e,t){if(!Z.isUndefined(t))return r(void 0,t)}function o(e,t){if(Z.isUndefined(t)){if(!Z.isUndefined(e))return r(void 0,e)}else return r(void 0,t)}function s(n,i,a){if(a in t)return r(n,i);if(a in e)return r(void 0,n)}let c={url:a,method:a,data:a,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:s,headers:(e,t,n)=>i(Hh(e),Hh(t),n,!0)};return Z.forEach(Object.keys({...e,...t}),function(r){let a=c[r]||i,o=a(e[r],t[r],r);Z.isUndefined(o)&&a!==s||(n[r]=o)}),n}var Wh=e=>{let t=Uh({},e),{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:a,headers:o,auth:s}=t;t.headers=o=Ch.from(o),t.url=Um(Vh(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&o.set(`Authorization`,`Basic `+btoa((s.username||``)+`:`+(s.password?unescape(encodeURIComponent(s.password)):``)));let c;if(Z.isFormData(n)){if(rh.hasStandardBrowserEnv||rh.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((c=o.getContentType())!==!1){let[e,...t]=c?c.split(`;`).map(e=>e.trim()).filter(Boolean):[];o.setContentType([e||`multipart/form-data`,...t].join(`; `))}}if(rh.hasStandardBrowserEnv&&(r&&Z.isFunction(r)&&(r=r(t)),r||r!==!1&&Lh(t.url))){let e=i&&a&&Rh.read(a);e&&o.set(i,e)}return t};const Gh=typeof XMLHttpRequest<`u`;var Kh=Gh&&function(e){return new Promise(function(t,n){let r=Wh(e),i=r.data,a=Ch.from(r.headers).normalize(),{responseType:o,onUploadProgress:s,onDownloadProgress:c}=r,l,u,d,f,p;function m(){f&&f(),p&&p(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener(`abort`,l)}let h=new XMLHttpRequest;h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout;function g(){if(!h)return;let r=Ch.from(`getAllResponseHeaders`in h&&h.getAllResponseHeaders()),i=!o||o===`text`||o===`json`?h.responseText:h.response,a={data:i,status:h.status,statusText:h.statusText,headers:r,config:e,request:h};Oh(function(e){t(e),m()},function(e){n(e),m()},a),h=null}`onloadend`in h?h.onloadend=g:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf(`file:`)===0)||setTimeout(g)},h.onabort=function(){h&&(n(new Q(`Request aborted`,Q.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new Q(`Network Error`,Q.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=r.timeout?`timeout of `+r.timeout+`ms exceeded`:`timeout exceeded`,i=r.transitional||Km;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Q(t,i.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,h)),h=null},i===void 0&&a.setContentType(null),`setRequestHeader`in h&&Z.forEach(a.toJSON(),function(e,t){h.setRequestHeader(t,e)}),Z.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),o&&o!==`json`&&(h.responseType=r.responseType),c&&([d,p]=Ph(c,!0),h.addEventListener(`progress`,d)),s&&h.upload&&([u,f]=Ph(s),h.upload.addEventListener(`progress`,u),h.upload.addEventListener(`loadend`,f)),(r.cancelToken||r.signal)&&(l=t=>{h&&(n(!t||t.type?new Dh(null,e,h):t),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener(`abort`,l)));let _=kh(r.url);if(_&&rh.protocols.indexOf(_)===-1){n(new Q(`Unsupported protocol `+_+`:`,Q.ERR_BAD_REQUEST,e));return}h.send(i||null)})};const qh=(e,t)=>{let{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n=new AbortController,r,i=function(e){if(!r){r=!0,o();let t=e instanceof Error?e:this.reason;n.abort(t instanceof Q?t:new Dh(t instanceof Error?t.message:t))}},a=t&&setTimeout(()=>{a=null,i(new Q(`timeout ${t} of ms exceeded`,Q.ETIMEDOUT))},t),o=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener(`abort`,i)}),e=null)};e.forEach(e=>e.addEventListener(`abort`,i));let{signal:s}=n;return s.unsubscribe=()=>Z.asap(o),s}};var Jh=qh;const Yh=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},Xh=async function*(e,t){for await(let n of Zh(e))yield*Yh(n,t)},Zh=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Qh=(e,t,n,r)=>{let i=Xh(e,t),a=0,o,s=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{let{done:t,value:r}=await i.next();if(t){s(),e.close();return}let o=r.byteLength;if(n){let e=a+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(e){throw s(e),e}},cancel(e){return s(e),i.return()}},{highWaterMark:2})},$h=typeof fetch==`function`&&typeof Request==`function`&&typeof Response==`function`,eg=$h&&typeof ReadableStream==`function`,tg=$h&&(typeof TextEncoder==`function`?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ng=(e,...t)=>{try{return!!e(...t)}catch{return!1}},rg=eg&&ng(()=>{let e=!1,t=new Request(rh.origin,{body:new ReadableStream,method:`POST`,get duplex(){return e=!0,`half`}}).headers.has(`Content-Type`);return e&&!t}),ig=64*1024,ag=eg&&ng(()=>Z.isReadableStream(new Response(``).body)),og={stream:ag&&(e=>e.body)};$h&&(e=>{[`text`,`arrayBuffer`,`blob`,`formData`,`stream`].forEach(t=>{!og[t]&&(og[t]=Z.isFunction(e[t])?e=>e[t]():(e,n)=>{throw new Q(`Response type '${t}' is not supported`,Q.ERR_NOT_SUPPORT,n)})})})(new Response);const sg=async e=>{if(e==null)return 0;if(Z.isBlob(e))return e.size;if(Z.isSpecCompliantForm(e)){let t=new Request(rh.origin,{method:`POST`,body:e});return(await t.arrayBuffer()).byteLength}if(Z.isArrayBufferView(e)||Z.isArrayBuffer(e))return e.byteLength;if(Z.isURLSearchParams(e)&&(e+=``),Z.isString(e))return(await tg(e)).byteLength},cg=async(e,t)=>{let n=Z.toFiniteNumber(e.getContentLength());return n??sg(t)};var lg=$h&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:a,timeout:o,onDownloadProgress:s,onUploadProgress:c,responseType:l,headers:u,withCredentials:d=`same-origin`,fetchOptions:f}=Wh(e);l=l?(l+``).toLowerCase():`text`;let p=Jh([i,a&&a.toAbortSignal()],o),m,h=p&&p.unsubscribe&&(()=>{p.unsubscribe()}),g;try{if(c&&rg&&n!==`get`&&n!==`head`&&(g=await cg(u,r))!==0){let e=new Request(t,{method:`POST`,body:r,duplex:`half`}),n;if(Z.isFormData(r)&&(n=e.headers.get(`content-type`))&&u.setContentType(n),e.body){let[t,n]=Fh(g,Ph(Ih(c)));r=Qh(e.body,ig,t,n)}}Z.isString(d)||(d=d?`include`:`omit`);let i=`credentials`in Request.prototype;m=new Request(t,{...f,signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:`half`,credentials:i?d:void 0});let a=await fetch(m,f),o=ag&&(l===`stream`||l===`response`);if(ag&&(s||o&&h)){let e={};[`status`,`statusText`,`headers`].forEach(t=>{e[t]=a[t]});let t=Z.toFiniteNumber(a.headers.get(`content-length`)),[n,r]=s&&Fh(t,Ph(Ih(s),!0))||[];a=new Response(Qh(a.body,ig,n,()=>{r&&r(),h&&h()}),e)}l||=`text`;let _=await og[Z.findKey(og,l)||`text`](a,e);return!o&&h&&h(),await new Promise((t,n)=>{Oh(t,n,{data:_,headers:Ch.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:m})})}catch(t){throw h&&h(),t&&t.name===`TypeError`&&/Load failed|fetch/i.test(t.message)?Object.assign(new Q(`Network Error`,Q.ERR_NETWORK,e,m),{cause:t.cause||t}):Q.from(t,t&&t.code,e,m)}});const ug={http:Am,xhr:Kh,fetch:lg};Z.forEach(ug,(e,t)=>{if(e){try{Object.defineProperty(e,`name`,{value:t})}catch{}Object.defineProperty(e,`adapterName`,{value:t})}});const dg=e=>`- ${e}`,fg=e=>Z.isFunction(e)||e===null||e===!1;var pg={getAdapter:e=>{e=Z.isArray(e)?e:[e];let{length:t}=e,n,r,i={};for(let a=0;a<t;a++){n=e[a];let t;if(r=n,!fg(n)&&(r=ug[(t=String(n)).toLowerCase()],r===void 0))throw new Q(`Unknown adapter '${t}'`);if(r)break;i[t||`#`+a]=r}if(!r){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(t===!1?`is not supported by the environment`:`is not available in the build`)),n=t?e.length>1?`since :
`+e.map(dg).join(`
`):` `+dg(e[0]):`as no adapter specified`;throw new Q(`There is no suitable adapter to dispatch the request `+n,`ERR_NOT_SUPPORT`)}return r},adapters:ug};function mg(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Dh(null,e)}function hg(e){mg(e),e.headers=Ch.from(e.headers),e.data=wh.call(e,e.transformRequest),[`post`,`put`,`patch`].indexOf(e.method)!==-1&&e.headers.setContentType(`application/x-www-form-urlencoded`,!1);let t=pg.getAdapter(e.adapter||dh.adapter);return t(e).then(function(t){return mg(e),t.data=wh.call(e,e.transformResponse,t),t.headers=Ch.from(t.headers),t},function(t){return Th(t)||(mg(e),t&&t.response&&(t.response.data=wh.call(e,e.transformResponse,t.response),t.response.headers=Ch.from(t.response.headers))),Promise.reject(t)})}const gg=`1.11.0`,_g={};[`object`,`boolean`,`number`,`function`,`string`,`symbol`].forEach((e,t)=>{_g[e]=function(n){return typeof n===e||`a`+(t<1?`n `:` `)+e}});const vg={};_g.transitional=function(e,t,n){function r(e,t){return`[Axios v`+gg+`] Transitional option '`+e+`'`+t+(n?`. `+n:``)}return(n,i,a)=>{if(e===!1)throw new Q(r(i,` has been removed`+(t?` in `+t:``)),Q.ERR_DEPRECATED);return t&&!vg[i]&&(vg[i]=!0,console.warn(r(i,` has been deprecated since v`+t+` and will be removed in the near future`))),e?e(n,i,a):!0}},_g.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function yg(e,t,n){if(typeof e!=`object`)throw new Q(`options must be an object`,Q.ERR_BAD_OPTION_VALUE);let r=Object.keys(e),i=r.length;for(;i-- >0;){let a=r[i],o=t[a];if(o){let t=e[a],n=t===void 0||o(t,a,e);if(n!==!0)throw new Q(`option `+a+` must be `+n,Q.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Q(`Unknown option `+a,Q.ERR_BAD_OPTION)}}var bg={assertOptions:yg,validators:_g};const xg=bg.validators;var Sg=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Gm,response:new Gm}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let n=t.stack?t.stack.replace(/^.+\n/,``):``;try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,``))&&(e.stack+=`
`+n):e.stack=n}catch{}}throw e}}_request(e,t){typeof e==`string`?(t||={},t.url=e):t=e||{},t=Uh(this.defaults,t);let{transitional:n,paramsSerializer:r,headers:i}=t;n!==void 0&&bg.assertOptions(n,{silentJSONParsing:xg.transitional(xg.boolean),forcedJSONParsing:xg.transitional(xg.boolean),clarifyTimeoutError:xg.transitional(xg.boolean)},!1),r!=null&&(Z.isFunction(r)?t.paramsSerializer={serialize:r}:bg.assertOptions(r,{encode:xg.function,serialize:xg.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls===void 0?t.allowAbsoluteUrls=!0:t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls),bg.assertOptions(t,{baseUrl:xg.spelling(`baseURL`),withXsrfToken:xg.spelling(`withXSRFToken`)},!0),t.method=(t.method||this.defaults.method||`get`).toLowerCase();let a=i&&Z.merge(i.common,i[t.method]);i&&Z.forEach([`delete`,`get`,`head`,`post`,`put`,`patch`,`common`],e=>{delete i[e]}),t.headers=Ch.concat(a,i);let o=[],s=!0;this.interceptors.request.forEach(function(e){typeof e.runWhen==`function`&&e.runWhen(t)===!1||(s&&=e.synchronous,o.unshift(e.fulfilled,e.rejected))});let c=[];this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let l,u=0,d;if(!s){let e=[hg.bind(this),void 0];for(e.unshift(...o),e.push(...c),d=e.length,l=Promise.resolve(t);u<d;)l=l.then(e[u++],e[u++]);return l}d=o.length;let f=t;for(u=0;u<d;){let e=o[u++],t=o[u++];try{f=e(f)}catch(e){t.call(this,e);break}}try{l=hg.call(this,f)}catch(e){return Promise.reject(e)}for(u=0,d=c.length;u<d;)l=l.then(c[u++],c[u++]);return l}getUri(e){e=Uh(this.defaults,e);let t=Vh(e.baseURL,e.url,e.allowAbsoluteUrls);return Um(t,e.params,e.paramsSerializer)}};Z.forEach([`delete`,`get`,`head`,`options`],function(e){Sg.prototype[e]=function(t,n){return this.request(Uh(n||{},{method:e,url:t,data:(n||{}).data}))}}),Z.forEach([`post`,`put`,`patch`],function(e){function t(t){return function(n,r,i){return this.request(Uh(i||{},{method:e,headers:t?{"Content-Type":`multipart/form-data`}:{},url:n,data:r}))}}Sg.prototype[e]=t(),Sg.prototype[e+`Form`]=t(!0)});var Cg=Sg,wg=class e{constructor(e){if(typeof e!=`function`)throw TypeError(`executor must be a function.`);let t;this.promise=new Promise(function(e){t=e});let n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t,r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,i){n.reason||(n.reason=new Dh(e,r,i),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t,n=new e(function(e){t=e});return{token:n,cancel:t}}},Tg=wg;function Eg(e){return function(t){return e.apply(null,t)}}function Dg(e){return Z.isObject(e)&&e.isAxiosError===!0}const Og={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Og).forEach(([e,t])=>{Og[t]=e});var kg=Og;function Ag(e){let t=new Cg(e),n=yp(Cg.prototype.request,t);return Z.extend(n,Cg.prototype,t,{allOwnKeys:!0}),Z.extend(n,t,null,{allOwnKeys:!0}),n.create=function(t){return Ag(Uh(e,t))},n}const jg=Ag(dh);jg.Axios=Cg,jg.CanceledError=Dh,jg.CancelToken=Tg,jg.isCancel=Th,jg.VERSION=gg,jg.toFormData=Lm,jg.AxiosError=Q,jg.Cancel=jg.CanceledError,jg.all=function(e){return Promise.all(e)},jg.spread=Eg,jg.isAxiosError=Dg,jg.mergeConfig=Uh,jg.AxiosHeaders=Ch,jg.formToJSON=e=>ch(Z.isHTMLForm(e)?new FormData(e):e),jg.getAdapter=pg.getAdapter,jg.HttpStatusCode=kg,jg.default=jg;var Mg=jg;const Ng=mc(`auth`,()=>{let e=P(null),t=P(localStorage.getItem(`rps_auth_token`)),n=P(localStorage.getItem(`rps_refresh_token`)),r=P(!1),i=P(null),a=G(()=>!!t.value&&!!e.value),o=G(()=>e.value?.roles||[]),s=G(()=>e=>o.value.includes(e)),c=async a=>{r.value=!0,i.value=null;try{let r=await Mg.post(`/api/v1/auth/login`,a),{user:i,token:o,refresh_token:s}=r.data.data;t.value=o,n.value=s,e.value=i,localStorage.setItem(`rps_auth_token`,o),localStorage.setItem(`rps_refresh_token`,s),Mg.defaults.headers.common.Authorization=`Bearer ${o}`}catch(e){throw i.value=e.response?.data?.message||`Login failed`,e}finally{r.value=!1}},l=async()=>{r.value=!0;try{t.value&&await Mg.post(`/api/v1/auth/logout`)}catch(e){console.error(`Logout error:`,e)}finally{e.value=null,t.value=null,n.value=null,localStorage.removeItem(`rps_auth_token`),localStorage.removeItem(`rps_refresh_token`),delete Mg.defaults.headers.common.Authorization,r.value=!1}},u=async()=>{if(!n.value)throw Error(`No refresh token available`);try{let e=await Mg.post(`/api/v1/auth/refresh`,{refresh_token:n.value}),{token:r,refresh_token:i}=e.data.data;t.value=r,n.value=i,localStorage.setItem(`rps_auth_token`,r),localStorage.setItem(`rps_refresh_token`,i),Mg.defaults.headers.common.Authorization=`Bearer ${r}`}catch(e){throw await l(),e}},d=async()=>{if(t.value)try{let t=await Mg.get(`/api/v1/auth/profile`);e.value=t.data.data}catch(e){console.error(`Failed to fetch profile:`,e),await l()}},f=async t=>{r.value=!0,i.value=null;try{let n=await Mg.put(`/api/v1/auth/profile`,t);e.value=n.data.data}catch(e){throw i.value=e.response?.data?.message||`Profile update failed`,e}finally{r.value=!1}},p=async e=>{r.value=!0,i.value=null;try{await Mg.put(`/api/v1/auth/change-password`,e)}catch(e){throw i.value=e.response?.data?.message||`Password change failed`,e}finally{r.value=!1}},m=async()=>{t.value&&(Mg.defaults.headers.common.Authorization=`Bearer ${t.value}`,await d())};return{user:e,token:t,loading:r,error:i,isAuthenticated:a,userRoles:o,hasRole:s,login:c,logout:l,refreshAuthToken:u,fetchProfile:d,updateProfile:f,changePassword:p,initialize:m}}),Pg=typeof document<`u`;function Fg(e){return typeof e==`object`||`displayName`in e||`props`in e||`__vccOpts`in e}function Ig(e){return e.__esModule||e[Symbol.toStringTag]===`Module`||e.default&&Fg(e.default)}const $=Object.assign;function Lg(e,t){let n={};for(let r in t){let i=t[r];n[r]=zg(i)?i.map(e):e(i)}return n}const Rg=()=>{},zg=Array.isArray,Bg=/#/g,Vg=/&/g,Hg=/\//g,Ug=/=/g,Wg=/\?/g,Gg=/\+/g,Kg=/%5B/g,qg=/%5D/g,Jg=/%5E/g,Yg=/%60/g,Xg=/%7B/g,Zg=/%7C/g,Qg=/%7D/g,$g=/%20/g;function e_(e){return encodeURI(``+e).replace(Zg,`|`).replace(Kg,`[`).replace(qg,`]`)}function t_(e){return e_(e).replace(Xg,`{`).replace(Qg,`}`).replace(Jg,`^`)}function n_(e){return e_(e).replace(Gg,`%2B`).replace($g,`+`).replace(Bg,`%23`).replace(Vg,`%26`).replace(Yg,"`").replace(Xg,`{`).replace(Qg,`}`).replace(Jg,`^`)}function r_(e){return n_(e).replace(Ug,`%3D`)}function i_(e){return e_(e).replace(Bg,`%23`).replace(Wg,`%3F`)}function a_(e){return e==null?``:i_(e).replace(Hg,`%2F`)}function o_(e){try{return decodeURIComponent(``+e)}catch{}return``+e}const s_=/\/$/,c_=e=>e.replace(s_,``);function l_(e,t,n=`/`){let r,i={},a=``,o=``,s=t.indexOf(`#`),c=t.indexOf(`?`);return s<c&&s>=0&&(c=-1),c>-1&&(r=t.slice(0,c),a=t.slice(c+1,s>-1?s:t.length),i=e(a)),s>-1&&(r||=t.slice(0,s),o=t.slice(s,t.length)),r=__(r??t,n),{fullPath:r+(a&&`?`)+a+o,path:r,query:i,hash:o_(o)}}function u_(e,t){let n=t.query?e(t.query):``;return t.path+(n&&`?`)+n+(t.hash||``)}function d_(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||`/`}function f_(e,t,n){let r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&p_(t.matched[r],n.matched[i])&&m_(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function p_(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function m_(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e)if(!h_(e[n],t[n]))return!1;return!0}function h_(e,t){return zg(e)?g_(e,t):zg(t)?g_(t,e):e===t}function g_(e,t){return zg(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):e.length===1&&e[0]===t}function __(e,t){if(e.startsWith(`/`))return e;if(!e)return t;let n=t.split(`/`),r=e.split(`/`),i=r[r.length-1];(i===`..`||i===`.`)&&r.push(``);let a=n.length-1,o,s;for(o=0;o<r.length;o++)if(s=r[o],s!==`.`)if(s===`..`)a>1&&a--;else break;return n.slice(0,a).join(`/`)+`/`+r.slice(o).join(`/`)}const v_={path:`/`,name:void 0,params:{},query:{},hash:``,fullPath:`/`,matched:[],meta:{},redirectedFrom:void 0};var y_;(function(e){e.pop=`pop`,e.push=`push`})(y_||={});var b_;(function(e){e.back=`back`,e.forward=`forward`,e.unknown=``})(b_||={});function x_(e){if(!e)if(Pg){let t=document.querySelector(`base`);e=t&&t.getAttribute(`href`)||`/`,e=e.replace(/^\w+:\/\/[^\/]+/,``)}else e=`/`;return e[0]!==`/`&&e[0]!==`#`&&(e=`/`+e),c_(e)}const S_=/^[^#]+#/;function C_(e,t){return e.replace(S_,`#`)+t}function w_(e,t){let n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const T_=()=>({left:window.scrollX,top:window.scrollY});function E_(e){let t;if(`el`in e){let n=e.el,r=typeof n==`string`&&n.startsWith(`#`),i=typeof n==`string`?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=w_(i,e)}else t=e;`scrollBehavior`in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left==null?window.scrollX:t.left,t.top==null?window.scrollY:t.top)}function D_(e,t){let n=history.state?history.state.position-t:-1;return n+e}const O_=new Map;function k_(e,t){O_.set(e,t)}function A_(e){let t=O_.get(e);return O_.delete(e),t}let j_=()=>location.protocol+`//`+location.host;function M_(e,t){let{pathname:n,search:r,hash:i}=t,a=e.indexOf(`#`);if(a>-1){let t=i.includes(e.slice(a))?e.slice(a).length:1,n=i.slice(t);return n[0]!==`/`&&(n=`/`+n),d_(n,``)}let o=d_(n,e);return o+r+i}function N_(e,t,n,r){let i=[],a=[],o=null,s=({state:a})=>{let s=M_(e,location),c=n.value,l=t.value,u=0;if(a){if(n.value=s,t.value=a,o&&o===c){o=null;return}u=l?a.position-l.position:0}else r(s);i.forEach(e=>{e(n.value,c,{delta:u,type:y_.pop,direction:u?u>0?b_.forward:b_.back:b_.unknown})})};function c(){o=n.value}function l(e){i.push(e);let t=()=>{let t=i.indexOf(e);t>-1&&i.splice(t,1)};return a.push(t),t}function u(){let{history:e}=window;e.state&&e.replaceState($({},e.state,{scroll:T_()}),``)}function d(){for(let e of a)e();a=[],window.removeEventListener(`popstate`,s),window.removeEventListener(`beforeunload`,u)}return window.addEventListener(`popstate`,s),window.addEventListener(`beforeunload`,u,{passive:!0}),{pauseListeners:c,listen:l,destroy:d}}function P_(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?T_():null}}function F_(e){let{history:t,location:n}=window,r={value:M_(e,n)},i={value:t.state};i.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(r,a,o){let s=e.indexOf(`#`),c=s>-1?(n.host&&document.querySelector(`base`)?e:e.slice(s))+r:j_()+e+r;try{t[o?`replaceState`:`pushState`](a,``,c),i.value=a}catch(e){console.error(e),n[o?`replace`:`assign`](c)}}function o(e,n){let o=$({},t.state,P_(i.value.back,e,i.value.forward,!0),n,{position:i.value.position});a(e,o,!0),r.value=e}function s(e,n){let o=$({},i.value,t.state,{forward:e,scroll:T_()});a(o.current,o,!0);let s=$({},P_(r.value,e,null),{position:o.position+1},n);a(e,s,!1),r.value=e}return{location:r,state:i,push:s,replace:o}}function I_(e){e=x_(e);let t=F_(e),n=N_(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}let i=$({location:``,base:e,go:r,createHref:C_.bind(null,e)},t,n);return Object.defineProperty(i,`location`,{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,`state`,{enumerable:!0,get:()=>t.state.value}),i}function L_(e){return typeof e==`string`||e&&typeof e==`object`}function R_(e){return typeof e==`string`||typeof e==`symbol`}const z_=Symbol(``);var B_;(function(e){e[e.aborted=4]=`aborted`,e[e.cancelled=8]=`cancelled`,e[e.duplicated=16]=`duplicated`})(B_||={});function V_(e,t){return $(Error(),{type:e,[z_]:!0},t)}function H_(e,t){return e instanceof Error&&z_ in e&&(t==null||!!(e.type&t))}const U_=`[^/]+?`,W_={sensitive:!1,strict:!1,start:!0,end:!0},G_=/[.+*?^${}()[\]/\\]/g;function K_(e,t){let n=$({},W_,t),r=[],i=n.start?`^`:``,a=[];for(let t of e){let e=t.length?[]:[90];n.strict&&!t.length&&(i+=`/`);for(let r=0;r<t.length;r++){let o=t[r],s=40+(n.sensitive?.25:0);if(o.type===0)r||(i+=`/`),i+=o.value.replace(G_,`\\$&`),s+=40;else if(o.type===1){let{value:e,repeatable:n,optional:c,regexp:l}=o;a.push({name:e,repeatable:n,optional:c});let u=l||U_;if(u!==U_){s+=10;try{`${u}`}catch(t){throw Error(`Invalid custom RegExp for param "${e}" (${u}): `+t.message)}}let d=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;r||(d=c&&t.length<2?`(?:/${d})`:`/`+d),c&&(d+=`?`),i+=d,s+=20,c&&(s+=-8),n&&(s+=-20),u===`.*`&&(s+=-50)}e.push(s)}r.push(e)}if(n.strict&&n.end){let e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(i+=`/?`),n.end?i+=`$`:n.strict&&!i.endsWith(`/`)&&(i+=`(?:/|$)`);let o=new RegExp(i,n.sensitive?``:`i`);function s(e){let t=e.match(o),n={};if(!t)return null;for(let e=1;e<t.length;e++){let r=t[e]||``,i=a[e-1];n[i.name]=r&&i.repeatable?r.split(`/`):r}return n}function c(t){let n=``,r=!1;for(let i of e){(!r||!n.endsWith(`/`))&&(n+=`/`),r=!1;for(let e of i)if(e.type===0)n+=e.value;else if(e.type===1){let{value:a,repeatable:o,optional:s}=e,c=a in t?t[a]:``;if(zg(c)&&!o)throw Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);let l=zg(c)?c.join(`/`):c;if(!l)if(s)i.length<2&&(n.endsWith(`/`)?n=n.slice(0,-1):r=!0);else throw Error(`Missing required param "${a}"`);n+=l}}return n||`/`}return{re:o,score:r,keys:a,parse:s,stringify:c}}function q_(e,t){let n=0;for(;n<e.length&&n<t.length;){let r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function J_(e,t){let n=0,r=e.score,i=t.score;for(;n<r.length&&n<i.length;){let e=q_(r[n],i[n]);if(e)return e;n++}if(Math.abs(i.length-r.length)===1){if(Y_(r))return 1;if(Y_(i))return-1}return i.length-r.length}function Y_(e){let t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const X_={type:0,value:``},Z_=/[a-zA-Z0-9_]/;function Q_(e){if(!e)return[[]];if(e===`/`)return[[X_]];if(!e.startsWith(`/`))throw Error(`Invalid path "${e}"`);function t(e){throw Error(`ERR (${n})/"${l}": ${e}`)}let n=0,r=n,i=[],a;function o(){a&&i.push(a),a=[]}let s=0,c,l=``,u=``;function d(){l&&(n===0?a.push({type:0,value:l}):n===1||n===2||n===3?(a.length>1&&(c===`*`||c===`+`)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:l,regexp:u,repeatable:c===`*`||c===`+`,optional:c===`*`||c===`?`})):t(`Invalid state to consume buffer`),l=``)}function f(){l+=c}for(;s<e.length;){if(c=e[s++],c===`\\`&&n!==2){r=n,n=4;continue}switch(n){case 0:c===`/`?(l&&d(),o()):c===`:`?(d(),n=1):f();break;case 4:f(),n=r;break;case 1:c===`(`?n=2:Z_.test(c)?f():(d(),n=0,c!==`*`&&c!==`?`&&c!==`+`&&s--);break;case 2:c===`)`?u[u.length-1]==`\\`?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:d(),n=0,c!==`*`&&c!==`?`&&c!==`+`&&s--,u=``;break;default:t(`Unknown state`);break}}return n===2&&t(`Unfinished custom RegExp for param "${l}"`),d(),o(),i}function $_(e,t,n){let r=K_(Q_(e.path),n),i=$(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function ev(e,t){let n=[],r=new Map;t=ov({strict:!1,end:!0,sensitive:!1},t);function i(e){return r.get(e)}function a(e,n,r){let i=!r,s=nv(e);s.aliasOf=r&&r.record;let l=ov(t,e),u=[s];if(`alias`in e){let t=typeof e.alias==`string`?[e.alias]:e.alias;for(let e of t)u.push(nv($({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s})))}let d,f;for(let t of u){let{path:u}=t;if(n&&u[0]!==`/`){let e=n.record.path,r=e[e.length-1]===`/`?``:`/`;t.path=n.record.path+(u&&r+u)}if(d=$_(t,n,l),r?r.alias.push(d):(f||=d,f!==d&&f.alias.push(d),i&&e.name&&!iv(d)&&o(e.name)),lv(d)&&c(d),s.children){let e=s.children;for(let t=0;t<e.length;t++)a(e[t],d,r&&r.children[t])}r||=d}return f?()=>{o(f)}:Rg}function o(e){if(R_(e)){let t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{let t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function s(){return n}function c(e){let t=sv(e,n);n.splice(t,0,e),e.record.name&&!iv(e)&&r.set(e.record.name,e)}function l(e,t){let i,a={},o,s;if(`name`in e&&e.name){if(i=r.get(e.name),!i)throw V_(1,{location:e});s=i.record.name,a=$(tv(t.params,i.keys.filter(e=>!e.optional).concat(i.parent?i.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&tv(e.params,i.keys.map(e=>e.name))),o=i.stringify(a)}else if(e.path!=null)o=e.path,i=n.find(e=>e.re.test(o)),i&&(a=i.parse(o),s=i.record.name);else{if(i=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!i)throw V_(1,{location:e,currentLocation:t});s=i.record.name,a=$({},t.params,e.params),o=i.stringify(a)}let c=[],l=i;for(;l;)c.unshift(l.record),l=l.parent;return{name:s,path:o,params:a,matched:c,meta:av(c)}}e.forEach(e=>a(e));function u(){n.length=0,r.clear()}return{addRoute:a,resolve:l,removeRoute:o,clearRoutes:u,getRoutes:s,getRecordMatcher:i}}function tv(e,t){let n={};for(let r of t)r in e&&(n[r]=e[r]);return n}function nv(e){let t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:rv(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:`components`in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,`mods`,{value:{}}),t}function rv(e){let t={},n=e.props||!1;if(`component`in e)t.default=n;else for(let r in e.components)t[r]=typeof n==`object`?n[r]:n;return t}function iv(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function av(e){return e.reduce((e,t)=>$(e,t.meta),{})}function ov(e,t){let n={};for(let r in e)n[r]=r in t?t[r]:e[r];return n}function sv(e,t){let n=0,r=t.length;for(;n!==r;){let i=n+r>>1,a=J_(e,t[i]);a<0?r=i:n=i+1}let i=cv(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function cv(e){let t=e;for(;t=t.parent;)if(lv(t)&&J_(e,t)===0)return t}function lv({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function uv(e){let t={};if(e===``||e===`?`)return t;let n=e[0]===`?`,r=(n?e.slice(1):e).split(`&`);for(let e=0;e<r.length;++e){let n=r[e].replace(Gg,` `),i=n.indexOf(`=`),a=o_(i<0?n:n.slice(0,i)),o=i<0?null:o_(n.slice(i+1));if(a in t){let e=t[a];zg(e)||(e=t[a]=[e]),e.push(o)}else t[a]=o}return t}function dv(e){let t=``;for(let n in e){let r=e[n];if(n=r_(n),r==null){r!==void 0&&(t+=(t.length?`&`:``)+n);continue}let i=zg(r)?r.map(e=>e&&n_(e)):[r&&n_(r)];i.forEach(e=>{e!==void 0&&(t+=(t.length?`&`:``)+n,e!=null&&(t+=`=`+e))})}return t}function fv(e){let t={};for(let n in e){let r=e[n];r!==void 0&&(t[n]=zg(r)?r.map(e=>e==null?null:``+e):r==null?r:``+r)}return t}const pv=Symbol(``),mv=Symbol(``),hv=Symbol(``),gv=Symbol(``),_v=Symbol(``);function vv(){let e=[];function t(t){return e.push(t),()=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function yv(e,t,n,r,i,a=e=>e()){let o=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,c)=>{let l=e=>{e===!1?c(V_(4,{from:n,to:t})):e instanceof Error?c(e):L_(e)?c(V_(2,{from:t,to:e})):(o&&r.enterCallbacks[i]===o&&typeof e==`function`&&o.push(e),s())},u=a(()=>e.call(r&&r.instances[i],t,n,l)),d=Promise.resolve(u);e.length<3&&(d=d.then(l)),d.catch(e=>c(e))})}function bv(e,t,n,r,i=e=>e()){let a=[];for(let o of e)for(let e in o.components){let s=o.components[e];if(t!==`beforeRouteEnter`&&!o.instances[e])continue;if(Fg(s)){let c=s.__vccOpts||s,l=c[t];l&&a.push(yv(l,n,r,o,e,i))}else{let c=s();a.push(()=>c.then(a=>{if(!a)throw Error(`Couldn't resolve component "${e}" at "${o.path}"`);let s=Ig(a)?a.default:a;o.mods[e]=a,o.components[e]=s;let c=s.__vccOpts||s,l=c[t];return l&&yv(l,n,r,o,e,i)()}))}}return a}function xv(e){let t=z(hv),n=z(gv),r=G(()=>{let n=I(e.to);return t.resolve(n)}),i=G(()=>{let{matched:e}=r.value,{length:t}=e,i=e[t-1],a=n.matched;if(!i||!a.length)return-1;let o=a.findIndex(p_.bind(null,i));if(o>-1)return o;let s=Dv(e[t-2]);return t>1&&Dv(i)===s&&a[a.length-1].path!==s?a.findIndex(p_.bind(null,e[t-2])):o}),a=G(()=>i.value>-1&&Ev(n.params,r.value.params)),o=G(()=>i.value>-1&&i.value===n.matched.length-1&&m_(n.params,r.value.params));function s(n={}){if(Tv(n)){let n=t[I(e.replace)?`replace`:`push`](I(e.to)).catch(Rg);return e.viewTransition&&typeof document<`u`&&`startViewTransition`in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}return{route:r,href:G(()=>r.value.href),isActive:a,isExactActive:o,navigate:s}}function Sv(e){return e.length===1?e[0]:e}const Cv=dr({name:`RouterLink`,compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:`page`},viewTransition:Boolean},useLink:xv,setup(e,{slots:t}){let n=Lt(xv(e)),{options:r}=z(hv),i=G(()=>({[Ov(e.activeClass,r.linkActiveClass,`router-link-active`)]:n.isActive,[Ov(e.exactActiveClass,r.linkExactActiveClass,`router-link-exact-active`)]:n.isExactActive}));return()=>{let r=t.default&&Sv(t.default(n));return e.custom?r:eo(`a`,{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},r)}}}),wv=Cv;function Tv(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){let t=e.currentTarget.getAttribute(`target`);if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ev(e,t){for(let n in t){let r=t[n],i=e[n];if(typeof r==`string`){if(r!==i)return!1}else if(!zg(i)||i.length!==r.length||r.some((e,t)=>e!==i[t]))return!1}return!0}function Dv(e){return e?e.aliasOf?e.aliasOf.path:e.path:``}const Ov=(e,t,n)=>e??t??n,kv=dr({name:`RouterView`,inheritAttrs:!1,props:{name:{type:String,default:`default`},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){let r=z(_v),i=G(()=>e.route||r.value),a=z(mv,0),o=G(()=>{let e=I(a),{matched:t}=i.value,n;for(;(n=t[e])&&!n.components;)e++;return e}),s=G(()=>i.value.matched[o.value]);pi(mv,G(()=>o.value+1)),pi(pv,s),pi(_v,i);let c=P();return B(()=>[c.value,s.value,e.name],([e,t,n],[r,i,a])=>{t&&(t.instances[n]=e,i&&i!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=i.leaveGuards),t.updateGuards.size||(t.updateGuards=i.updateGuards))),e&&t&&(!i||!p_(t,i)||!r)&&(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:`post`}),()=>{let r=i.value,a=e.name,o=s.value,l=o&&o.components[a];if(!l)return Av(n.default,{Component:l,route:r});let u=o.props[a],d=u?u===!0?r.params:typeof u==`function`?u(r):u:null,f=e=>{e.component.isUnmounted&&(o.instances[a]=null)},p=eo(l,$({},d,t,{onVnodeUnmounted:f,ref:c}));return Av(n.default,{Component:p,route:r})||p}}});function Av(e,t){if(!e)return null;let n=e(t);return n.length===1?n[0]:n}const jv=kv;function Mv(e){let t=ev(e.routes,e),n=e.parseQuery||uv,r=e.stringifyQuery||dv,i=e.history,a=vv(),o=vv(),s=vv(),c=F(v_),l=v_;Pg&&e.scrollBehavior&&`scrollRestoration`in history&&(history.scrollRestoration=`manual`);let u=Lg.bind(null,e=>``+e),d=Lg.bind(null,a_),f=Lg.bind(null,o_);function p(e,n){let r,i;return R_(e)?(r=t.getRecordMatcher(e),i=n):i=e,t.addRoute(i,r)}function m(e){let n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function h(){return t.getRoutes().map(e=>e.record)}function g(e){return!!t.getRecordMatcher(e)}function _(e,a){if(a=$({},a||c.value),typeof e==`string`){let r=l_(n,e,a.path),o=t.resolve({path:r.path},a),s=i.createHref(r.fullPath);return $(r,o,{params:f(o.params),hash:o_(r.hash),redirectedFrom:void 0,href:s})}let o;if(e.path!=null)o=$({},e,{path:l_(n,e.path,a.path).path});else{let t=$({},e.params);for(let e in t)t[e]??delete t[e];o=$({},e,{params:d(t)}),a.params=d(a.params)}let s=t.resolve(o,a),l=e.hash||``;s.params=u(f(s.params));let p=u_(r,$({},e,{hash:t_(l),path:s.path})),m=i.createHref(p);return $({fullPath:p,hash:l,query:r===dv?fv(e.query):e.query||{}},s,{redirectedFrom:void 0,href:m})}function v(e){return typeof e==`string`?l_(n,e,c.value.path):$({},e)}function y(e,t){if(l!==e)return V_(8,{from:t,to:e})}function b(e){return C(e)}function x(e){return b($(v(e),{replace:!0}))}function S(e){let t=e.matched[e.matched.length-1];if(t&&t.redirect){let{redirect:n}=t,r=typeof n==`function`?n(e):n;return typeof r==`string`&&(r=r.includes(`?`)||r.includes(`#`)?r=v(r):{path:r},r.params={}),$({query:e.query,hash:e.hash,params:r.path==null?e.params:{}},r)}}function C(e,t){let n=l=_(e),i=c.value,a=e.state,o=e.force,s=e.replace===!0,u=S(n);if(u)return C($(v(u),{state:typeof u==`object`?$({},a,u.state):a,force:o,replace:s}),t||n);let d=n;d.redirectedFrom=t;let f;return!o&&f_(r,i,n)&&(f=V_(16,{to:d,from:i}),ce(i,i,!0,!1)),(f?Promise.resolve(f):E(d,i)).catch(e=>H_(e)?H_(e,2)?e:O(e):oe(e,d,i)).then(e=>{if(e){if(H_(e,2))return C($({replace:s},v(e.to),{state:typeof e.to==`object`?$({},a,e.to.state):a,force:o}),t||d)}else e=te(d,i,!0,s,a);return ee(d,i,e),e})}function w(e,t){let n=y(e,t);return n?Promise.reject(n):Promise.resolve()}function T(e){let t=de.values().next().value;return t&&typeof t.runWithContext==`function`?t.runWithContext(e):e()}function E(e,t){let n,[r,i,s]=Nv(e,t);n=bv(r.reverse(),`beforeRouteLeave`,e,t);for(let i of r)i.leaveGuards.forEach(r=>{n.push(yv(r,e,t))});let c=w.bind(null,e,t);return n.push(c),fe(n).then(()=>{n=[];for(let r of a.list())n.push(yv(r,e,t));return n.push(c),fe(n)}).then(()=>{n=bv(i,`beforeRouteUpdate`,e,t);for(let r of i)r.updateGuards.forEach(r=>{n.push(yv(r,e,t))});return n.push(c),fe(n)}).then(()=>{n=[];for(let r of s)if(r.beforeEnter)if(zg(r.beforeEnter))for(let i of r.beforeEnter)n.push(yv(i,e,t));else n.push(yv(r.beforeEnter,e,t));return n.push(c),fe(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=bv(s,`beforeRouteEnter`,e,t,T),n.push(c),fe(n))).then(()=>{n=[];for(let r of o.list())n.push(yv(r,e,t));return n.push(c),fe(n)}).catch(e=>H_(e,8)?e:Promise.reject(e))}function ee(e,t,n){s.list().forEach(r=>T(()=>r(e,t,n)))}function te(e,t,n,r,a){let o=y(e,t);if(o)return o;let s=t===v_,l=Pg?history.state:{};n&&(r||s?i.replace(e.fullPath,$({scroll:s&&l&&l.scroll},a)):i.push(e.fullPath,a)),c.value=e,ce(e,t,n,s),O()}let D;function ne(){D||=i.listen((e,t,n)=>{if(!k.listening)return;let r=_(e),a=S(r);if(a){C($(a,{replace:!0,force:!0}),r).catch(Rg);return}l=r;let o=c.value;Pg&&k_(D_(o.fullPath,n.delta),T_()),E(r,o).catch(e=>H_(e,12)?e:H_(e,2)?(C($(v(e.to),{force:!0}),r).then(e=>{H_(e,20)&&!n.delta&&n.type===y_.pop&&i.go(-1,!1)}).catch(Rg),Promise.reject()):(n.delta&&i.go(-n.delta,!1),oe(e,r,o))).then(e=>{e||=te(r,o,!1),e&&(n.delta&&!H_(e,8)?i.go(-n.delta,!1):n.type===y_.pop&&H_(e,20)&&i.go(-1,!1)),ee(r,o,e)}).catch(Rg)})}let re=vv(),ie=vv(),ae;function oe(e,t,n){O(e);let r=ie.list();return r.length?r.forEach(r=>r(e,t,n)):console.error(e),Promise.reject(e)}function se(){return ae&&c.value!==v_?Promise.resolve():new Promise((e,t)=>{re.add([e,t])})}function O(e){return ae||(ae=!e,ne(),re.list().forEach(([t,n])=>e?n(e):t()),re.reset()),e}function ce(t,n,r,i){let{scrollBehavior:a}=e;if(!Pg||!a)return Promise.resolve();let o=!r&&A_(D_(t.fullPath,0))||(i||!r)&&history.state&&history.state.scroll||null;return Cn().then(()=>a(t,n,o)).then(e=>e&&E_(e)).catch(e=>oe(e,t,n))}let le=e=>i.go(e),ue,de=new Set,k={currentRoute:c,listening:!0,addRoute:p,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:g,getRoutes:h,resolve:_,options:e,push:b,replace:x,go:le,back:()=>le(-1),forward:()=>le(1),beforeEach:a.add,beforeResolve:o.add,afterEach:s.add,onError:ie.add,isReady:se,install(e){let t=this;e.component(`RouterLink`,wv),e.component(`RouterView`,jv),e.config.globalProperties.$router=t,Object.defineProperty(e.config.globalProperties,`$route`,{enumerable:!0,get:()=>I(c)}),Pg&&!ue&&c.value===v_&&(ue=!0,b(i.location).catch(e=>{}));let n={};for(let e in v_)Object.defineProperty(n,e,{get:()=>c.value[e],enumerable:!0});e.provide(hv,t),e.provide(gv,Rt(n)),e.provide(_v,c);let r=e.unmount;de.add(e),e.unmount=function(){de.delete(e),de.size<1&&(l=v_,D&&D(),D=null,c.value=v_,ue=!1,ae=!1),r()}}};function fe(e){return e.reduce((e,t)=>e.then(()=>T(t)),Promise.resolve())}return k}function Nv(e,t){let n=[],r=[],i=[],a=Math.max(t.matched.length,e.matched.length);for(let o=0;o<a;o++){let a=t.matched[o];a&&(e.matched.find(e=>p_(e,a))?r.push(a):n.push(a));let s=e.matched[o];s&&(t.matched.find(e=>p_(e,s))||i.push(s))}return[n,r,i]}function Pv(){return z(hv)}function Fv(e){return z(gv)}const Iv=J({tag:{type:[String,Object,Function],default:`div`}},`tag`),Lv=J({text:String,...ju(),...Iv()},`VToolbarTitle`),Rv=Y()({name:`VToolbarTitle`,props:Lv(),setup(e,t){let{slots:n}=t;return X(()=>{let t=!!(n.default||n.text||e.text);return U(e.tag,{class:A([`v-toolbar-title`,e.class]),style:k(e.style)},{default:()=>[t&&H(`div`,{class:`v-toolbar-title__placeholder`},[n.text?n.text():e.text,n.default?.()])]})}),{}}}),zv=J({disabled:Boolean,group:Boolean,hideOnLeave:Boolean,leaveAbsolute:Boolean,mode:String,origin:String},`transition`);function Bv(e,t,n){return Y()({name:e,props:zv({mode:n,origin:t}),setup(t,n){let{slots:r}=n,i={onBeforeEnter(e){t.origin&&(e.style.transformOrigin=t.origin)},onLeave(e){if(t.leaveAbsolute){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:i}=e;e._transitionInitialStyles={position:e.style.position,top:e.style.top,left:e.style.left,width:e.style.width,height:e.style.height},e.style.position=`absolute`,e.style.top=`${t}px`,e.style.left=`${n}px`,e.style.width=`${r}px`,e.style.height=`${i}px`}t.hideOnLeave&&e.style.setProperty(`display`,`none`,`important`)},onAfterLeave(e){if(t.leaveAbsolute&&e?._transitionInitialStyles){let{position:t,top:n,left:r,width:i,height:a}=e._transitionInitialStyles;delete e._transitionInitialStyles,e.style.position=t||``,e.style.top=n||``,e.style.left=r||``,e.style.width=i||``,e.style.height=a||``}}};return()=>{let n=t.group?_s:vo;return eo(n,{name:t.disabled?``:e,css:!t.disabled,...t.group?void 0:{mode:t.mode},...t.disabled?{}:i},r.default)}}})}function Vv(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:`in-out`;return Y()({name:e,props:{mode:{type:String,default:n},disabled:Boolean,group:Boolean},setup(n,r){let{slots:i}=r,a=n.group?_s:vo;return()=>eo(a,{name:n.disabled?``:e,css:!n.disabled,...n.disabled?{}:t},i.default)}})}function Hv(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:``,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=t?`width`:`height`,r=D(`offset-${n}`);return{onBeforeEnter(e){e._parent=e.parentNode,e._initialStyle={transition:e.style.transition,overflow:e.style.overflow,[n]:e.style[n]}},onEnter(t){let i=t._initialStyle;if(!i)return;t.style.setProperty(`transition`,`none`,`important`),t.style.overflow=`hidden`;let a=`${t[r]}px`;t.style[n]=`0`,t.offsetHeight,t.style.transition=i.transition,e&&t._parent&&t._parent.classList.add(e),requestAnimationFrame(()=>{t.style[n]=a})},onAfterEnter:a,onEnterCancelled:a,onLeave(e){e._initialStyle={transition:``,overflow:e.style.overflow,[n]:e.style[n]},e.style.overflow=`hidden`,e.style[n]=`${e[r]}px`,e.offsetHeight,requestAnimationFrame(()=>e.style[n]=`0`)},onAfterLeave:i,onLeaveCancelled:i};function i(t){e&&t._parent&&t._parent.classList.remove(e),a(t)}function a(e){if(!e._initialStyle)return;let t=e._initialStyle[n];e.style.overflow=e._initialStyle.overflow,t!=null&&(e.style[n]=t),delete e._initialStyle}}const Uv=J({target:[Object,Array]},`v-dialog-transition`),Wv=new WeakMap,Gv=Y()({name:`VDialogTransition`,props:Uv(),setup(e,t){let{slots:n}=t,r={onBeforeEnter(e){e.style.pointerEvents=`none`,e.style.visibility=`hidden`},async onEnter(t,n){await new Promise(e=>requestAnimationFrame(e)),await new Promise(e=>requestAnimationFrame(e)),t.style.visibility=``;let r=qv(e.target,t),{x:i,y:a,sx:o,sy:s,speed:c}=r;Wv.set(t,r);let l=Nl(t,[{transform:`translate(${i}px, ${a}px) scale(${o}, ${s})`,opacity:0},{}],{duration:225*c,easing:Ku});Kv(t)?.forEach(e=>{Nl(e,[{opacity:0},{opacity:0,offset:.33},{}],{duration:450*c,easing:Gu})}),l.finished.then(()=>n())},onAfterEnter(e){e.style.removeProperty(`pointer-events`)},onBeforeLeave(e){e.style.pointerEvents=`none`},async onLeave(t,n){await new Promise(e=>requestAnimationFrame(e));let r;r=!Wv.has(t)||Array.isArray(e.target)||e.target.offsetParent||e.target.getClientRects().length?qv(e.target,t):Wv.get(t);let{x:i,y:a,sx:o,sy:s,speed:c}=r,l=Nl(t,[{},{transform:`translate(${i}px, ${a}px) scale(${o}, ${s})`,opacity:0}],{duration:125*c,easing:qu});l.finished.then(()=>n()),Kv(t)?.forEach(e=>{Nl(e,[{},{opacity:0,offset:.2},{opacity:0}],{duration:250*c,easing:Gu})})},onAfterLeave(e){e.style.removeProperty(`pointer-events`)}};return()=>e.target?U(vo,W({name:`dialog-transition`},r,{css:!1}),n):U(vo,{name:`dialog-transition`},n)}});function Kv(e){let t=e.querySelector(`:scope > .v-card, :scope > .v-sheet, :scope > .v-list`)?.children;return t&&[...t]}function qv(e,t){let n=Al(e),r=Ml(t),[i,a]=getComputedStyle(t).transformOrigin.split(` `).map(e=>parseFloat(e)),[o,s]=getComputedStyle(t).getPropertyValue(`--v-overlay-anchor-origin`).split(` `),c=n.left+n.width/2;o===`left`||s===`left`?c-=n.width/2:(o===`right`||s===`right`)&&(c+=n.width/2);let l=n.top+n.height/2;o===`top`||s===`top`?l-=n.height/2:(o===`bottom`||s===`bottom`)&&(l+=n.height/2);let u=n.width/r.width,d=n.height/r.height,f=Math.max(1,u,d),p=u/f||0,m=d/f||0,h=r.width*r.height/(window.innerWidth*window.innerHeight),g=h>.12?Math.min(1.5,(h-.12)*10+1):1;return{x:c-(i+r.left),y:l-(a+r.top),sx:p,sy:m,speed:g}}Bv(`fab-transition`,`center center`,`out-in`),Bv(`dialog-bottom-transition`),Bv(`dialog-top-transition`);const Jv=Bv(`fade-transition`),Yv=Bv(`scale-transition`);Bv(`scroll-x-transition`),Bv(`scroll-x-reverse-transition`),Bv(`scroll-y-transition`),Bv(`scroll-y-reverse-transition`),Bv(`slide-x-transition`),Bv(`slide-x-reverse-transition`);const Xv=Bv(`slide-y-transition`);Bv(`slide-y-reverse-transition`);const Zv=Vv(`expand-transition`,Hv()),Qv=Vv(`expand-x-transition`,Hv(``,!0)),$v=J({defaults:Object,disabled:Boolean,reset:[Number,String],root:[Boolean,String],scoped:Boolean},`VDefaultsProvider`),ey=Y(!1)({name:`VDefaultsProvider`,props:$v(),setup(e,t){let{slots:n}=t,{defaults:r,disabled:i,reset:a,root:o,scoped:s}=$t(e);return Ru(r,{reset:a,root:o,scoped:s,disabled:i}),()=>n.default?.()}}),ty=J({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},`dimension`);function ny(e){let t=G(()=>{let t={},n=q(e.height),r=q(e.maxHeight),i=q(e.maxWidth),a=q(e.minHeight),o=q(e.minWidth),s=q(e.width);return n!=null&&(t.height=n),r!=null&&(t.maxHeight=r),i!=null&&(t.maxWidth=i),a!=null&&(t.minHeight=a),o!=null&&(t.minWidth=o),s!=null&&(t.width=s),t});return{dimensionStyles:t}}function ry(e){return{aspectStyles:G(()=>{let t=Number(e.aspectRatio);return t?{paddingBottom:String(1/t*100)+`%`}:void 0})}}const iy=J({aspectRatio:[String,Number],contentClass:null,inline:Boolean,...ju(),...ty()},`VResponsive`),ay=Y()({name:`VResponsive`,props:iy(),setup(e,t){let{slots:n}=t,{aspectStyles:r}=ry(e),{dimensionStyles:i}=ny(e);return X(()=>H(`div`,{class:A([`v-responsive`,{"v-responsive--inline":e.inline},e.class]),style:k([i.value,e.style])},[H(`div`,{class:`v-responsive__sizer`,style:k(r.value)},null),n.additional?.(),n.default&&H(`div`,{class:A([`v-responsive__content`,e.contentClass])},[n.default()])])),{}}});function oy(e){return tl(()=>{let t=Xt(e),n=[],r={};if(t.background)if(hu(t.background)){if(r.backgroundColor=t.background,!t.text&&gu(t.background)){let e=yu(t.background);if(e.a==null||e.a===1){let t=Au(e);r.color=t,r.caretColor=t}}}else n.push(`bg-${t.background}`);return t.text&&(hu(t.text)?(r.color=t.text,r.caretColor=t.text):n.push(`text-${t.text}`)),{colorClasses:n,colorStyles:r}})}function sy(e){let{colorClasses:t,colorStyles:n}=oy(()=>({text:Xt(e)}));return{textColorClasses:t,textColorStyles:n}}function cy(e){let{colorClasses:t,colorStyles:n}=oy(()=>({background:Xt(e)}));return{backgroundColorClasses:t,backgroundColorStyles:n}}const ly=J({rounded:{type:[Boolean,Number,String],default:void 0},tile:Boolean},`rounded`);function uy(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=G(()=>{let n=N(e)?e.value:e.rounded,r=N(e)?e.value:e.tile,i=[];if(n===!0||n===``)i.push(`${t}--rounded`);else if(typeof n==`string`||n===0)for(let e of String(n).split(` `))i.push(`rounded-${e}`);else (r||n===!1)&&i.push(`rounded-0`);return i});return{roundedClasses:n}}const dy=J({transition:{type:null,default:`fade-transition`,validator:e=>e!==!0}},`transition`),fy=(e,t)=>{let{slots:n}=t,{transition:r,disabled:i,group:a,...o}=e,{component:s=a?_s:vo,...c}=kc(r)?r:{},l;return l=kc(r)?W(c,yl({disabled:i,group:a}),o):W({name:i||!r?``:r},o),eo(s,l,n)};function py(e,t){if(!gc)return;let n=t.modifiers||{},r=t.value,{handler:i,options:a}=typeof r==`object`?r:{handler:r,options:{}},o=new IntersectionObserver(function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=arguments.length>1?arguments[1]:void 0,o=e._observe?.[t.instance.$.uid];if(!o)return;let s=r.some(e=>e.isIntersecting);i&&(!n.quiet||o.init)&&(!n.once||s||o.init)&&i(s,r,a),s&&n.once?my(e,t):o.init=!0},a);e._observe=Object(e._observe),e._observe[t.instance.$.uid]={init:!1,observer:o},o.observe(e)}function my(e,t){let n=e._observe?.[t.instance.$.uid];n&&(n.observer.unobserve(e),delete e._observe[t.instance.$.uid])}const hy={mounted:py,unmounted:my};var gy=hy;const _y=J({absolute:Boolean,alt:String,cover:Boolean,color:String,draggable:{type:[Boolean,String],default:void 0},eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:``},crossorigin:String,referrerpolicy:String,srcset:String,position:String,...iy(),...ju(),...ly(),...dy()},`VImg`),vy=Y()({name:`VImg`,directives:{vIntersect:gy},props:_y(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,t){let{emit:n,slots:r}=t,{backgroundColorClasses:i,backgroundColorStyles:a}=cy(()=>e.color),{roundedClasses:o}=uy(e),s=Mu(`VImg`),c=F(``),l=P(),u=F(e.eager?`loading`:`idle`),d=F(),f=F(),p=G(()=>e.src&&typeof e.src==`object`?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),m=G(()=>p.value.aspect||d.value/f.value||0);B(()=>e.src,()=>{h(u.value!==`idle`)}),B(m,(e,t)=>{!e&&t&&l.value&&b(l.value)}),Cr(()=>h());function h(t){if(!(e.eager&&t)&&!(gc&&!t&&!e.eager)){if(u.value=`loading`,p.value.lazySrc){let e=new Image;e.src=p.value.lazySrc,b(e,null)}p.value.src&&Cn(()=>{n(`loadstart`,l.value?.currentSrc||p.value.src),setTimeout(()=>{if(!s.isUnmounted)if(l.value?.complete){if(l.value.naturalWidth||_(),u.value===`error`)return;m.value||b(l.value,null),u.value===`loading`&&g()}else m.value||b(l.value),v()})})}}function g(){s.isUnmounted||(v(),b(l.value),u.value=`loaded`,n(`load`,l.value?.currentSrc||p.value.src))}function _(){s.isUnmounted||(u.value=`error`,n(`error`,l.value?.currentSrc||p.value.src))}function v(){let e=l.value;e&&(c.value=e.currentSrc||e.src)}let y=-1;Dr(()=>{clearTimeout(y)});function b(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100,n=()=>{if(clearTimeout(y),s.isUnmounted)return;let{naturalHeight:r,naturalWidth:i}=e;r||i?(d.value=i,f.value=r):!e.complete&&u.value===`loading`&&t!=null?y=window.setTimeout(n,t):(e.currentSrc.endsWith(`.svg`)||e.currentSrc.startsWith(`data:image/svg+xml`))&&(d.value=1,f.value=1)};n()}let x=L(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),S=()=>{if(!p.value.src||u.value===`idle`)return null;let t=H(`img`,{class:A([`v-img__img`,x.value]),style:{objectPosition:e.position},crossorigin:e.crossorigin,src:p.value.src,srcset:p.value.srcset,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable,sizes:e.sizes,ref:l,onLoad:g,onError:_},null),n=r.sources?.();return U(fy,{transition:e.transition,appear:!0},{default:()=>[Fn(n?H(`picture`,{class:`v-img__picture`},[n,t]):t,[[Io,u.value===`loaded`]])]})},C=()=>U(fy,{transition:e.transition},{default:()=>[p.value.lazySrc&&u.value!==`loaded`&&H(`img`,{class:A([`v-img__img`,`v-img__img--preload`,x.value]),style:{objectPosition:e.position},crossorigin:e.crossorigin,src:p.value.lazySrc,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable},null)]}),w=()=>r.placeholder?U(fy,{transition:e.transition,appear:!0},{default:()=>[(u.value===`loading`||u.value===`error`&&!r.error)&&H(`div`,{class:`v-img__placeholder`},[r.placeholder()])]}):null,T=()=>r.error?U(fy,{transition:e.transition,appear:!0},{default:()=>[u.value===`error`&&H(`div`,{class:`v-img__error`},[r.error()])]}):null,E=()=>e.gradient?H(`div`,{class:`v-img__gradient`,style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,ee=F(!1);{let e=B(m,t=>{t&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{ee.value=!0})}),e())})}return X(()=>{let t=ay.filterProps(e);return Fn(U(ay,W({class:[`v-img`,{"v-img--absolute":e.absolute,"v-img--booting":!ee.value},i.value,o.value,e.class],style:[{width:q(e.width===`auto`?d.value:e.width)},a.value,e.style]},t,{aspectRatio:m.value,"aria-label":e.alt,role:e.alt?`img`:void 0}),{additional:()=>H(V,null,[U(S,null,null),U(C,null,null),U(E,null,null),U(w,null,null),U(T,null,null)]),default:r.default}),[[gy,{handler:h,options:e.options},null,{once:!0}]])}),{currentSrc:c,image:l,state:u,naturalWidth:d,naturalHeight:f}}}),yy=J({border:[Boolean,Number,String]},`border`);function by(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=G(()=>{let n=e.border;return n===!0||n===``?`${t}--border`:typeof n==`string`||n===0?String(n).split(` `).map(e=>`border-${e}`):[]});return{borderClasses:n}}const xy=J({elevation:{type:[Number,String],validator(e){let t=parseInt(e);return!isNaN(t)&&t>=0&&t<=24}}},`elevation`);function Sy(e){let t=L(()=>{let t=N(e)?e.value:e.elevation;return t==null?[]:[`elevation-${t}`]});return{elevationClasses:t}}const Cy=[null,`prominent`,`default`,`comfortable`,`compact`],wy=J({absolute:Boolean,collapse:Boolean,color:String,density:{type:String,default:`default`,validator:e=>Cy.includes(e)},extended:{type:Boolean,default:null},extensionHeight:{type:[Number,String],default:48},flat:Boolean,floating:Boolean,height:{type:[Number,String],default:64},image:String,title:String,...yy(),...ju(),...xy(),...ly(),...Iv({tag:`header`}),...Kf()},`VToolbar`),Ty=Y()({name:`VToolbar`,props:wy(),setup(e,t){let{slots:n}=t,{backgroundColorClasses:r,backgroundColorStyles:i}=cy(()=>e.color),{borderClasses:a}=by(e),{elevationClasses:o}=Sy(e),{roundedClasses:s}=uy(e),{themeClasses:c}=ip(e),{rtlClasses:l}=hd(),u=F(e.extended===null?!!n.extension?.():e.extended),d=G(()=>parseInt(Number(e.height)+(e.density===`prominent`?Number(e.height):0)-(e.density===`comfortable`?8:0)-(e.density===`compact`?16:0),10)),f=G(()=>u.value?parseInt(Number(e.extensionHeight)+(e.density===`prominent`?Number(e.extensionHeight):0)-(e.density===`comfortable`?4:0)-(e.density===`compact`?8:0),10):0);return Ru({VBtn:{variant:`text`}}),X(()=>{let t=!!(e.title||n.title),p=!!(n.image||e.image),m=n.extension?.();return u.value=e.extended===null?!!m:e.extended,U(e.tag,{class:A([`v-toolbar`,{"v-toolbar--absolute":e.absolute,"v-toolbar--collapse":e.collapse,"v-toolbar--flat":e.flat,"v-toolbar--floating":e.floating,[`v-toolbar--density-${e.density}`]:!0},r.value,a.value,o.value,s.value,c.value,l.value,e.class]),style:k([i.value,e.style])},{default:()=>[p&&H(`div`,{key:`image`,class:`v-toolbar__image`},[n.image?U(ey,{key:`image-defaults`,disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},n.image):U(vy,{key:`image-img`,cover:!0,src:e.image},null)]),U(ey,{defaults:{VTabs:{height:q(d.value)}}},{default:()=>[H(`div`,{class:`v-toolbar__content`,style:{height:q(d.value)}},[n.prepend&&H(`div`,{class:`v-toolbar__prepend`},[n.prepend?.()]),t&&U(Rv,{key:`title`,text:e.title},{text:n.title}),n.default?.(),n.append&&H(`div`,{class:`v-toolbar__append`},[n.append?.()])])]}),U(ey,{defaults:{VTabs:{height:q(f.value)}}},{default:()=>[U(Zv,null,{default:()=>[u.value&&H(`div`,{class:`v-toolbar__extension`,style:{height:q(f.value)}},[m])]})]})]})}),{contentHeight:d,extensionHeight:f}}}),Ey=J({scrollTarget:{type:String},scrollThreshold:{type:[String,Number],default:300}},`scroll`);function Dy(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{canScroll:n}=t,r=0,i=0,a=P(null),o=F(0),s=F(0),c=F(0),l=F(!1),u=F(!1),d=G(()=>Number(e.scrollThreshold)),f=G(()=>Wc((d.value-o.value)/d.value||0)),p=()=>{let e=a.value;if(!e||n&&!n.value)return;r=o.value,o.value=`window`in e?e.pageYOffset:e.scrollTop;let t=e instanceof Window?document.documentElement.scrollHeight:e.scrollHeight;if(i!==t){i=t;return}u.value=o.value<r,c.value=Math.abs(o.value-d.value)};return B(u,()=>{s.value=s.value||o.value}),B(l,()=>{s.value=0}),wr(()=>{B(()=>e.scrollTarget,e=>{let t=e?document.querySelector(e):window;if(!t){tu(`Unable to locate element with identifier ${e}`);return}t!==a.value&&(a.value?.removeEventListener(`scroll`,p),a.value=t,a.value.addEventListener(`scroll`,p,{passive:!0}))},{immediate:!0})}),Dr(()=>{a.value?.removeEventListener(`scroll`,p)}),n&&B(n,p,{immediate:!0}),{scrollThreshold:d,currentScroll:o,currentThreshold:c,isScrollActive:l,scrollRatio:f,isScrollingUp:u,savedScroll:s}}function Oy(){let e=F(!1);wr(()=>{window.requestAnimationFrame(()=>{e.value=!0})});let t=L(()=>e.value?void 0:{transition:`none !important`});return{ssrBootStyles:t,isBooted:zt(e)}}const ky=J({scrollBehavior:String,modelValue:{type:Boolean,default:!0},location:{type:String,default:`top`,validator:e=>[`top`,`bottom`].includes(e)},...wy(),...dp(),...Ey(),height:{type:[Number,String],default:64}},`VAppBar`),Ay=Y()({name:`VAppBar`,props:ky(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,r=P(),i=$u(e,`modelValue`),a=G(()=>{let t=new Set(e.scrollBehavior?.split(` `)??[]);return{hide:t.has(`hide`),fullyHide:t.has(`fully-hide`),inverted:t.has(`inverted`),collapse:t.has(`collapse`),elevate:t.has(`elevate`),fadeImage:t.has(`fade-image`)}}),o=G(()=>{let e=a.value;return e.hide||e.fullyHide||e.inverted||e.collapse||e.elevate||e.fadeImage||!i.value}),{currentScroll:s,scrollThreshold:c,isScrollingUp:l,scrollRatio:u}=Dy(e,{canScroll:o}),d=L(()=>a.value.hide||a.value.fullyHide),f=G(()=>e.collapse||a.value.collapse&&(a.value.inverted?u.value>0:u.value===0)),p=G(()=>e.flat||a.value.fullyHide&&!i.value||a.value.elevate&&(a.value.inverted?s.value>0:s.value===0)),m=G(()=>a.value.fadeImage?a.value.inverted?1-u.value:u.value:void 0),h=G(()=>{if(a.value.hide&&a.value.inverted)return 0;let e=r.value?.contentHeight??0,t=r.value?.extensionHeight??0;return d.value?s.value<c.value||a.value.fullyHide?e+t:e:e+t});hc(()=>!!e.scrollBehavior,()=>{Gi(()=>{d.value?a.value.inverted?i.value=s.value>c.value:i.value=l.value||s.value<c.value:i.value=!0})});let{ssrBootStyles:g}=Oy(),{layoutItemStyles:_}=pp({id:e.name,order:G(()=>parseInt(e.order,10)),position:L(()=>e.location),layoutSize:h,elementSize:F(void 0),active:i,absolute:L(()=>e.absolute)});return X(()=>{let t=Ty.filterProps(e);return U(Ty,W({ref:r,class:[`v-app-bar`,{"v-app-bar--bottom":e.location===`bottom`},e.class],style:[{..._.value,"--v-toolbar-image-opacity":m.value,height:void 0,...g.value},e.style]},t,{collapse:f.value,flat:p.value}),n)}),{}}}),jy=[null,`default`,`comfortable`,`compact`],My=J({density:{type:String,default:`default`,validator:e=>jy.includes(e)}},`density`);function Ny(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=L(()=>`${t}--density-${e.density}`);return{densityClasses:n}}const Py=[`elevated`,`flat`,`tonal`,`outlined`,`text`,`plain`];function Fy(e,t){return H(V,null,[e&&H(`span`,{key:`overlay`,class:A(`${t}__overlay`)},null),H(`span`,{key:`underlay`,class:A(`${t}__underlay`)},null)])}const Iy=J({color:String,variant:{type:String,default:`elevated`,validator:e=>Py.includes(e)}},`variant`);function Ly(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=L(()=>{let{variant:n}=Xt(e);return`${t}--variant-${n}`}),{colorClasses:r,colorStyles:i}=oy(()=>{let{variant:t,color:n}=Xt(e);return{[[`elevated`,`flat`].includes(t)?`background`:`text`]:n}});return{colorClasses:r,colorStyles:i,variantClasses:n}}const Ry=J({baseColor:String,divided:Boolean,direction:{type:String,default:`horizontal`},...yy(),...ju(),...My(),...xy(),...ly(),...Iv(),...Kf(),...Iy()},`VBtnGroup`),zy=Y()({name:`VBtnGroup`,props:Ry(),setup(e,t){let{slots:n}=t,{themeClasses:r}=ip(e),{densityClasses:i}=Ny(e),{borderClasses:a}=by(e),{elevationClasses:o}=Sy(e),{roundedClasses:s}=uy(e);Ru({VBtn:{height:L(()=>e.direction===`horizontal`?`auto`:null),baseColor:L(()=>e.baseColor),color:L(()=>e.color),density:L(()=>e.density),flat:!0,variant:L(()=>e.variant)}}),X(()=>U(e.tag,{class:A([`v-btn-group`,`v-btn-group--${e.direction}`,{"v-btn-group--divided":e.divided},r.value,a.value,i.value,o.value,s.value,e.class]),style:k(e.style)},n))}}),By=J({modelValue:{type:null,default:void 0},multiple:Boolean,mandatory:[Boolean,String],max:Number,selectedClass:String,disabled:Boolean},`group`),Vy=J({value:null,disabled:Boolean,selectedClass:String},`group-item`);function Hy(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,r=Mu(`useGroupItem`);if(!r)throw Error(`[Vuetify] useGroupItem composable must be used inside a component setup function`);let i=fr();pi(Symbol.for(`${t.description}:id`),i);let a=z(t,null);if(!a){if(!n)return a;throw Error(`[Vuetify] Could not find useGroup injection with symbol ${t.description}`)}let o=L(()=>e.value),s=G(()=>!!(a.disabled.value||e.disabled));a.register({id:i,value:o,disabled:s},r),Dr(()=>{a.unregister(i)});let c=G(()=>a.isSelected(i)),l=G(()=>a.items.value[0].id===i),u=G(()=>a.items.value[a.items.value.length-1].id===i),d=G(()=>c.value&&[a.selectedClass.value,e.selectedClass]);return B(c,e=>{r.emit(`group:selected`,{value:e})},{flush:`sync`}),{id:i,isSelected:c,isFirst:l,isLast:u,toggle:()=>a.select(i,!c.value),select:e=>a.select(i,e),selectedClass:d,value:o,disabled:s,group:a}}function Uy(e,t){let n=!1,r=Lt([]),i=$u(e,`modelValue`,[],e=>e==null?[]:Gy(r,Hc(e)),t=>{let n=Ky(r,t);return e.multiple?n:n[0]}),a=Mu(`useGroup`);function o(e,n){let i=e,o=Symbol.for(`${t.description}:id`),s=Zc(o,a?.vnode),c=s.indexOf(n);I(i.value)??(i.value=c,i.useIndexAsValue=!0),c>-1?r.splice(c,0,i):r.push(i)}function s(e){if(n)return;c();let t=r.findIndex(t=>t.id===e);r.splice(t,1)}function c(){let t=r.find(e=>!e.disabled);t&&e.mandatory===`force`&&!i.value.length&&(i.value=[t.id])}wr(()=>{c()}),Dr(()=>{n=!0}),Er(()=>{for(let e=0;e<r.length;e++)r[e].useIndexAsValue&&(r[e].value=e)});function l(t,n){let a=r.find(e=>e.id===t);if(!(n&&a?.disabled))if(e.multiple){let r=i.value.slice(),a=r.findIndex(e=>e===t),o=~a;if(n??=!o,o&&e.mandatory&&r.length<=1||!o&&e.max!=null&&r.length+1>e.max)return;a<0&&n?r.push(t):a>=0&&!n&&r.splice(a,1),i.value=r}else{let r=i.value.includes(t);if(e.mandatory&&r||!r&&!n)return;i.value=n??!r?[t]:[]}}function u(t){if(e.multiple&&tu(`This method is not supported when using "multiple" prop`),i.value.length){let e=i.value[0],n=r.findIndex(t=>t.id===e),a=(n+t)%r.length,o=r[a];for(;o.disabled&&a!==n;)a=(a+t)%r.length,o=r[a];if(o.disabled)return;i.value=[r[a].id]}else{let e=r.find(e=>!e.disabled);e&&(i.value=[e.id])}}let d={register:o,unregister:s,selected:i,select:l,disabled:L(()=>e.disabled),prev:()=>u(r.length-1),next:()=>u(1),isSelected:e=>i.value.includes(e),selectedClass:L(()=>e.selectedClass),items:L(()=>r),getItemIndex:e=>Wy(r,e)};return pi(t,d),d}function Wy(e,t){let n=Gy(e,[t]);return n.length?e.findIndex(e=>e.id===n[0]):-1}function Gy(e,t){let n=[];return t.forEach(t=>{let r=e.find(e=>Tc(t,e.value)),i=e[t];r?.value==null?i?.useIndexAsValue&&n.push(i.id):n.push(r.id)}),n}function Ky(e,t){let n=[];return t.forEach(t=>{let r=e.findIndex(e=>e.id===t);if(~r){let t=e[r];n.push(t.value==null?r:t.value)}}),n}const qy=Symbol.for(`vuetify:v-btn-toggle`),Jy=J({...Ry(),...By()},`VBtnToggle`),Yy=Y()({name:`VBtnToggle`,props:Jy(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,{isSelected:r,next:i,prev:a,select:o,selected:s}=Uy(e,qy);return X(()=>{let t=zy.filterProps(e);return U(zy,W({class:[`v-btn-toggle`,e.class]},t,{style:e.style}),{default:()=>[n.default?.({isSelected:r,next:i,prev:a,select:o,selected:s})]})}),{next:i,prev:a,select:o}}}),Xy=[`x-small`,`small`,`default`,`large`,`x-large`],Zy=J({size:{type:[String,Number],default:`default`}},`size`);function Qy(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu();return tl(()=>{let n=e.size,r,i;return nl(Xy,n)?r=`${t}--size-${n}`:n&&(i={width:q(n),height:q(n)}),{sizeClasses:r,sizeStyles:i}})}const $y=J({color:String,disabled:Boolean,start:Boolean,end:Boolean,icon:If,opacity:[String,Number],...ju(),...Zy(),...Iv({tag:`i`}),...Kf()},`VIcon`),eb=Y()({name:`VIcon`,props:$y(),setup(e,t){let{attrs:n,slots:r}=t,i=F(),{themeClasses:a}=ap(),{iconData:o}=Wf(()=>i.value||e.icon),{sizeClasses:s}=Qy(e),{textColorClasses:c,textColorStyles:l}=sy(()=>e.color);return X(()=>{let t=r.default?.();t&&(i.value=Yc(t).filter(e=>e.type===sa&&e.children&&typeof e.children==`string`)[0]?.children);let u=!!(n.onClick||n.onClickOnce);return U(o.value.component,{tag:e.tag,icon:o.value.icon,class:A([`v-icon`,`notranslate`,a.value,s.value,c.value,{"v-icon--clickable":u,"v-icon--disabled":e.disabled,"v-icon--start":e.start,"v-icon--end":e.end},e.class]),style:k([{"--v-icon-opacity":e.opacity},s.value?void 0:{fontSize:q(e.size),height:q(e.size),width:q(e.size)},l.value,e.style]),role:u?`button`:void 0,"aria-hidden":!u,tabindex:u?e.disabled?-1:0:void 0},{default:()=>[t]})}),{}}});function tb(e,t){let n=P(),r=F(!1);if(gc){let i=new IntersectionObserver(t=>{e?.(t,i),r.value=!!t.find(e=>e.isIntersecting)},t);De(()=>{i.disconnect()}),B(n,(e,t)=>{t&&(i.unobserve(t),r.value=!1),e&&i.observe(e)},{flush:`post`})}return{intersectionRef:n,isIntersecting:r}}const nb=J({bgColor:String,color:String,indeterminate:[Boolean,String],modelValue:{type:[Number,String],default:0},rotate:{type:[Number,String],default:0},width:{type:[Number,String],default:4},...ju(),...Zy(),...Iv({tag:`div`}),...Kf()},`VProgressCircular`),rb=Y()({name:`VProgressCircular`,props:nb(),setup(e,t){let{slots:n}=t,r=20,i=2*Math.PI*r,a=P(),{themeClasses:o}=ip(e),{sizeClasses:s,sizeStyles:c}=Qy(e),{textColorClasses:l,textColorStyles:u}=sy(()=>e.color),{textColorClasses:d,textColorStyles:f}=sy(()=>e.bgColor),{intersectionRef:p,isIntersecting:m}=tb(),{resizeRef:h,contentRect:g}=op(),_=L(()=>Wc(parseFloat(e.modelValue),0,100)),v=L(()=>Number(e.width)),y=L(()=>c.value?Number(e.size):g.value?g.value.width:Math.max(v.value,32)),b=L(()=>r/(1-v.value/y.value)*2),x=L(()=>v.value/y.value*b.value),S=L(()=>q((100-_.value)/100*i));return Gi(()=>{p.value=a.value,h.value=a.value}),X(()=>U(e.tag,{ref:a,class:A([`v-progress-circular`,{"v-progress-circular--indeterminate":!!e.indeterminate,"v-progress-circular--visible":m.value,"v-progress-circular--disable-shrink":e.indeterminate===`disable-shrink`},o.value,s.value,l.value,e.class]),style:k([c.value,u.value,e.style]),role:`progressbar`,"aria-valuemin":`0`,"aria-valuemax":`100`,"aria-valuenow":e.indeterminate?void 0:_.value},{default:()=>[H(`svg`,{style:{transform:`rotate(calc(-90deg + ${Number(e.rotate)}deg))`},xmlns:`http://www.w3.org/2000/svg`,viewBox:`0 0 ${b.value} ${b.value}`},[H(`circle`,{class:A([`v-progress-circular__underlay`,d.value]),style:k(f.value),fill:`transparent`,cx:`50%`,cy:`50%`,r,"stroke-width":x.value,"stroke-dasharray":i,"stroke-dashoffset":0},null),H(`circle`,{class:`v-progress-circular__overlay`,fill:`transparent`,cx:`50%`,cy:`50%`,r,"stroke-width":x.value,"stroke-dasharray":i,"stroke-dashoffset":S.value},null)]),n.default&&H(`div`,{class:`v-progress-circular__content`},[n.default({value:_.value})])]})),{}}}),ib={center:`center`,top:`bottom`,bottom:`top`,left:`right`,right:`left`},ab=J({location:String},`location`);function ob(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2?arguments[2]:void 0,{isRtl:r}=hd(),i=G(()=>{if(!e.location)return{};let{side:i,align:a}=Sl(e.location.split(` `).length>1?e.location:`${e.location} center`,r.value);function o(e){return n?n(e):0}let s={};return i!==`center`&&(t?s[ib[i]]=`calc(100% - ${o(i)}px)`:s[i]=0),a===`center`?(i===`center`?s.top=s.left=`50%`:s[{top:`left`,bottom:`left`,left:`top`,right:`top`}[i]]=`50%`,s.transform={top:`translateX(-50%)`,bottom:`translateX(-50%)`,left:`translateY(-50%)`,right:`translateY(-50%)`,center:`translate(-50%, -50%)`}[i]):t?s[ib[a]]=`calc(100% - ${o(a)}px)`:s[a]=0,s});return{locationStyles:i}}const sb=J({absolute:Boolean,active:{type:Boolean,default:!0},bgColor:String,bgOpacity:[Number,String],bufferValue:{type:[Number,String],default:0},bufferColor:String,bufferOpacity:[Number,String],clickable:Boolean,color:String,height:{type:[Number,String],default:4},indeterminate:Boolean,max:{type:[Number,String],default:100},modelValue:{type:[Number,String],default:0},opacity:[Number,String],reverse:Boolean,stream:Boolean,striped:Boolean,roundedBar:Boolean,...ju(),...ab({location:`top`}),...ly(),...Iv(),...Kf()},`VProgressLinear`),cb=Y()({name:`VProgressLinear`,props:sb(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,r=$u(e,`modelValue`),{isRtl:i,rtlClasses:a}=hd(),{themeClasses:o}=ip(e),{locationStyles:s}=ob(e),{textColorClasses:c,textColorStyles:l}=sy(()=>e.color),{backgroundColorClasses:u,backgroundColorStyles:d}=cy(()=>e.bgColor||e.color),{backgroundColorClasses:f,backgroundColorStyles:p}=cy(()=>e.bufferColor||e.bgColor||e.color),{backgroundColorClasses:m,backgroundColorStyles:h}=cy(()=>e.color),{roundedClasses:g}=uy(e),{intersectionRef:_,isIntersecting:v}=tb(),y=G(()=>parseFloat(e.max)),b=G(()=>parseFloat(e.height)),x=G(()=>Wc(parseFloat(e.bufferValue)/y.value*100,0,100)),S=G(()=>Wc(parseFloat(r.value)/y.value*100,0,100)),C=G(()=>i.value!==e.reverse),w=G(()=>e.indeterminate?`fade-transition`:`slide-x-transition`),T=K&&window.matchMedia?.(`(forced-colors: active)`).matches;function E(e){if(!_.value)return;let{left:t,right:n,width:i}=_.value.getBoundingClientRect(),a=C.value?i-e.clientX+(n-i):e.clientX-t;r.value=Math.round(a/i*y.value)}return X(()=>U(e.tag,{ref:_,class:A([`v-progress-linear`,{"v-progress-linear--absolute":e.absolute,"v-progress-linear--active":e.active&&v.value,"v-progress-linear--reverse":C.value,"v-progress-linear--rounded":e.rounded,"v-progress-linear--rounded-bar":e.roundedBar,"v-progress-linear--striped":e.striped},g.value,o.value,a.value,e.class]),style:k([{bottom:e.location===`bottom`?0:void 0,top:e.location===`top`?0:void 0,height:e.active?q(b.value):0,"--v-progress-linear-height":q(b.value),...e.absolute?s.value:{}},e.style]),role:`progressbar`,"aria-hidden":e.active?`false`:`true`,"aria-valuemin":`0`,"aria-valuemax":e.max,"aria-valuenow":e.indeterminate?void 0:Math.min(parseFloat(r.value),y.value),onClick:e.clickable&&E},{default:()=>[e.stream&&H(`div`,{key:`stream`,class:A([`v-progress-linear__stream`,c.value]),style:{...l.value,[C.value?`left`:`right`]:q(-b.value),borderTop:`${q(b.value/2)} dotted`,opacity:parseFloat(e.bufferOpacity),top:`calc(50% - ${q(b.value/4)})`,width:q(100-x.value,`%`),"--v-progress-linear-stream-to":q(b.value*(C.value?1:-1))}},null),H(`div`,{class:A([`v-progress-linear__background`,T?void 0:u.value]),style:k([d.value,{opacity:parseFloat(e.bgOpacity),width:e.stream?0:void 0}])},null),H(`div`,{class:A([`v-progress-linear__buffer`,T?void 0:f.value]),style:k([p.value,{opacity:parseFloat(e.bufferOpacity),width:q(x.value,`%`)}])},null),U(vo,{name:w.value},{default:()=>[e.indeterminate?H(`div`,{class:`v-progress-linear__indeterminate`},[[`long`,`short`].map(e=>H(`div`,{key:e,class:A([`v-progress-linear__indeterminate`,e,T?void 0:m.value]),style:k(h.value)},null))]):H(`div`,{class:A([`v-progress-linear__determinate`,T?void 0:m.value]),style:k([h.value,{width:q(S.value,`%`)}])},null)]}),n.default&&H(`div`,{class:`v-progress-linear__content`},[n.default({value:S.value,buffer:x.value})])]})),{}}}),lb=J({loading:[Boolean,String]},`loader`);function ub(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=L(()=>({[`${t}--loading`]:e.loading}));return{loaderClasses:n}}function db(e,t){let{slots:n}=t;return H(`div`,{class:A(`${e.name}__loader`)},[n.default?.({color:e.color,isActive:e.active})||U(cb,{absolute:e.absolute,active:e.active,color:e.color,height:`2`,indeterminate:!0},null)])}const fb=[`static`,`relative`,`fixed`,`absolute`,`sticky`],pb=J({position:{type:String,validator:e=>fb.includes(e)}},`position`);function mb(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nu(),n=L(()=>e.position?`${t}--${e.position}`:void 0);return{positionClasses:n}}function hb(){let e=Mu(`useRoute`);return G(()=>e?.proxy?.$route)}function gb(){return Mu(`useRouter`)?.proxy?.$router}function _b(e,t){let n=Ir(`RouterLink`),r=L(()=>!!(e.href||e.to)),i=G(()=>r?.value||al(t,`click`)||al(e,`click`));if(typeof n==`string`||!(`useLink`in n)){let t=L(()=>e.href);return{isLink:r,isClickable:i,href:t,linkProps:Lt({href:t})}}let a=n.useLink({to:L(()=>e.to||``),replace:L(()=>e.replace)}),o=G(()=>e.to?a:void 0),s=hb(),c=G(()=>o.value?e.exact?s.value?o.value.isExactActive?.value&&Tc(o.value.route.value.query,s.value.query):o.value.isExactActive?.value??!1:o.value.isActive?.value??!1:!1),l=G(()=>e.to?o.value?.route.value.href:e.href);return{isLink:r,isClickable:i,isActive:c,route:o.value?.route,navigate:o.value?.navigate,href:l,linkProps:Lt({href:l,"aria-current":L(()=>c.value?`page`:void 0)})}}const vb=J({href:String,replace:Boolean,to:[String,Object],exact:Boolean},`router`);let yb=!1;function bb(e,t){let n=!1,r,i;K&&e?.beforeEach&&(Cn(()=>{window.addEventListener(`popstate`,a),r=e.beforeEach((e,r,i)=>{yb?n?t(i):i():setTimeout(()=>n?t(i):i()),yb=!0}),i=e?.afterEach(()=>{yb=!1})}),De(()=>{window.removeEventListener(`popstate`,a),r?.(),i?.()}));function a(e){e.state?.replaced||(n=!0,setTimeout(()=>n=!1))}}function xb(e,t){B(()=>e.isActive?.value,n=>{e.isLink.value&&n!=null&&t&&Cn(()=>{t(n)})},{immediate:!0})}const Sb=Symbol(`rippleStop`),Cb=80;function wb(e,t){e.style.transform=t,e.style.webkitTransform=t}function Tb(e){return e.constructor.name===`TouchEvent`}function Eb(e){return e.constructor.name===`KeyboardEvent`}const Db=function(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=0,i=0;if(!Eb(e)){let n=t.getBoundingClientRect(),a=Tb(e)?e.touches[e.touches.length-1]:e;r=a.clientX-n.left,i=a.clientY-n.top}let a=0,o=.3;t._ripple?.circle?(o=.15,a=t.clientWidth/2,a=n.center?a:a+Math.sqrt((r-a)**2+(i-a)**2)/4):a=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2;let s=`${(t.clientWidth-a*2)/2}px`,c=`${(t.clientHeight-a*2)/2}px`,l=n.center?s:`${r-a}px`,u=n.center?c:`${i-a}px`;return{radius:a,scale:o,x:l,y:u,centerX:s,centerY:c}},Ob={show(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t?._ripple?.enabled)return;let r=document.createElement(`span`),i=document.createElement(`span`);r.appendChild(i),r.className=`v-ripple__container`,n.class&&(r.className+=` ${n.class}`);let{radius:a,scale:o,x:s,y:c,centerX:l,centerY:u}=Db(e,t,n),d=`${a*2}px`;i.className=`v-ripple__animation`,i.style.width=d,i.style.height=d,t.appendChild(r);let f=window.getComputedStyle(t);f&&f.position===`static`&&(t.style.position=`relative`,t.dataset.previousPosition=`static`),i.classList.add(`v-ripple__animation--enter`),i.classList.add(`v-ripple__animation--visible`),wb(i,`translate(${s}, ${c}) scale3d(${o},${o},${o})`),i.dataset.activated=String(performance.now()),requestAnimationFrame(()=>{requestAnimationFrame(()=>{i.classList.remove(`v-ripple__animation--enter`),i.classList.add(`v-ripple__animation--in`),wb(i,`translate(${l}, ${u}) scale3d(1,1,1)`)})})},hide(e){if(!e?._ripple?.enabled)return;let t=e.getElementsByClassName(`v-ripple__animation`);if(t.length===0)return;let n=t[t.length-1];if(n.dataset.isHiding)return;n.dataset.isHiding=`true`;let r=performance.now()-Number(n.dataset.activated),i=Math.max(250-r,0);setTimeout(()=>{n.classList.remove(`v-ripple__animation--in`),n.classList.add(`v-ripple__animation--out`),setTimeout(()=>{let t=e.getElementsByClassName(`v-ripple__animation`);t.length===1&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),n.parentNode?.parentNode===e&&e.removeChild(n.parentNode)},300)},i)}};function kb(e){return e===void 0||!!e}function Ab(e){let t={},n=e.currentTarget;if(!(!n?._ripple||n._ripple.touched||e[Sb])){if(e[Sb]=!0,Tb(e))n._ripple.touched=!0,n._ripple.isTouch=!0;else if(n._ripple.isTouch)return;if(t.center=n._ripple.centered||Eb(e),n._ripple.class&&(t.class=n._ripple.class),Tb(e)){if(n._ripple.showTimerCommit)return;n._ripple.showTimerCommit=()=>{Ob.show(e,n,t)},n._ripple.showTimer=window.setTimeout(()=>{n?._ripple?.showTimerCommit&&(n._ripple.showTimerCommit(),n._ripple.showTimerCommit=null)},Cb)}else Ob.show(e,n,t)}}function jb(e){e[Sb]=!0}function Mb(e){let t=e.currentTarget;if(t?._ripple){if(window.clearTimeout(t._ripple.showTimer),e.type===`touchend`&&t._ripple.showTimerCommit){t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null,t._ripple.showTimer=window.setTimeout(()=>{Mb(e)});return}window.setTimeout(()=>{t._ripple&&(t._ripple.touched=!1)}),Ob.hide(t)}}function Nb(e){let t=e.currentTarget;t?._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}let Pb=!1;function Fb(e,t){!Pb&&t.includes(e.key)&&(Pb=!0,Ab(e))}function Ib(e){Pb=!1,Mb(e)}function Lb(e){Pb&&(Pb=!1,Mb(e))}function Rb(e,t,n){let{value:r,modifiers:i}=t,a=kb(r);a||Ob.hide(e),e._ripple=e._ripple??{},e._ripple.enabled=a,e._ripple.centered=i.center,e._ripple.circle=i.circle;let o=kc(r)?r:{};o.class&&(e._ripple.class=o.class);let s=o.keys??[`Enter`,`Space`];if(e._ripple.keyDownHandler=e=>Fb(e,s),a&&!n){if(i.stop){e.addEventListener(`touchstart`,jb,{passive:!0}),e.addEventListener(`mousedown`,jb);return}e.addEventListener(`touchstart`,Ab,{passive:!0}),e.addEventListener(`touchend`,Mb,{passive:!0}),e.addEventListener(`touchmove`,Nb,{passive:!0}),e.addEventListener(`touchcancel`,Mb),e.addEventListener(`mousedown`,Ab),e.addEventListener(`mouseup`,Mb),e.addEventListener(`mouseleave`,Mb),e.addEventListener(`keydown`,e=>Fb(e,s)),e.addEventListener(`keyup`,Ib),e.addEventListener(`blur`,Lb),e.addEventListener(`dragstart`,Mb,{passive:!0})}else !a&&n&&zb(e)}function zb(e){e.removeEventListener(`mousedown`,Ab),e.removeEventListener(`touchstart`,Ab),e.removeEventListener(`touchend`,Mb),e.removeEventListener(`touchmove`,Nb),e.removeEventListener(`touchcancel`,Mb),e.removeEventListener(`mouseup`,Mb),e.removeEventListener(`mouseleave`,Mb),e._ripple?.keyDownHandler&&e.removeEventListener(`keydown`,e._ripple.keyDownHandler),e.removeEventListener(`keyup`,Ib),e.removeEventListener(`dragstart`,Mb),e.removeEventListener(`blur`,Lb)}function Bb(e,t){Rb(e,t,!1)}function Vb(e){zb(e),delete e._ripple}function Hb(e,t){if(t.value===t.oldValue)return;let n=kb(t.oldValue);Rb(e,t,n)}const Ub={mounted:Bb,unmounted:Vb,updated:Hb};var Wb=Ub;const Gb=J({active:{type:Boolean,default:void 0},activeColor:String,baseColor:String,symbol:{type:null,default:qy},flat:Boolean,icon:[Boolean,String,Function,Object],prependIcon:If,appendIcon:If,block:Boolean,readonly:Boolean,slim:Boolean,stacked:Boolean,ripple:{type:[Boolean,Object],default:!0},text:{type:[String,Number,Boolean],default:void 0},...yy(),...ju(),...My(),...ty(),...xy(),...Vy(),...lb(),...ab(),...pb(),...ly(),...vb(),...Zy(),...Iv({tag:`button`}),...Kf(),...Iy({variant:`elevated`})},`VBtn`),Kb=Y()({name:`VBtn`,props:Gb(),emits:{"group:selected":e=>!0},setup(e,t){let{attrs:n,slots:r}=t,{themeClasses:i}=ip(e),{borderClasses:a}=by(e),{densityClasses:o}=Ny(e),{dimensionStyles:s}=ny(e),{elevationClasses:c}=Sy(e),{loaderClasses:l}=ub(e),{locationStyles:u}=ob(e),{positionClasses:d}=mb(e),{roundedClasses:f}=uy(e),{sizeClasses:p,sizeStyles:m}=Qy(e),h=Hy(e,e.symbol,!1),g=_b(e,n),_=G(()=>e.active===void 0?g.isLink.value?g.isActive?.value:h?.isSelected.value:e.active),v=L(()=>_.value?e.activeColor??e.color:e.color),y=G(()=>{let t=h?.isSelected.value&&(!g.isLink.value||g.isActive?.value)||!h||g.isActive?.value;return{color:t?v.value??e.baseColor:e.baseColor,variant:e.variant}}),{colorClasses:b,colorStyles:x,variantClasses:S}=Ly(y),C=G(()=>h?.disabled.value||e.disabled),w=L(()=>e.variant===`elevated`&&!(e.disabled||e.flat||e.border)),T=G(()=>{if(!(e.value===void 0||typeof e.value==`symbol`))return Object(e.value)===e.value?JSON.stringify(e.value,null,0):e.value});function E(e){C.value||g.isLink.value&&(e.metaKey||e.ctrlKey||e.shiftKey||e.button!==0||n.target===`_blank`)||(g.navigate?.(e),h?.toggle())}return xb(g,h?.select),X(()=>{let t=g.isLink.value?`a`:e.tag,n=!!(e.prependIcon||r.prepend),v=!!(e.appendIcon||r.append),y=!!(e.icon&&e.icon!==!0);return Fn(U(t,W({type:t===`a`?void 0:`button`,class:[`v-btn`,h?.selectedClass.value,{"v-btn--active":_.value,"v-btn--block":e.block,"v-btn--disabled":C.value,"v-btn--elevated":w.value,"v-btn--flat":e.flat,"v-btn--icon":!!e.icon,"v-btn--loading":e.loading,"v-btn--readonly":e.readonly,"v-btn--slim":e.slim,"v-btn--stacked":e.stacked},i.value,a.value,b.value,o.value,c.value,l.value,d.value,f.value,p.value,S.value,e.class],style:[x.value,s.value,u.value,m.value,e.style],"aria-busy":e.loading?!0:void 0,disabled:C.value||void 0,tabindex:e.loading||e.readonly?-1:void 0,onClick:E,value:T.value},g.linkProps),{default:()=>[Fy(!0,`v-btn`),!e.icon&&n&&H(`span`,{key:`prepend`,class:`v-btn__prepend`},[r.prepend?U(ey,{key:`prepend-defaults`,disabled:!e.prependIcon,defaults:{VIcon:{icon:e.prependIcon}}},r.prepend):U(eb,{key:`prepend-icon`,icon:e.prependIcon},null)]),H(`span`,{class:`v-btn__content`,"data-no-activator":``},[!r.default&&y?U(eb,{key:`content-icon`,icon:e.icon},null):U(ey,{key:`content-defaults`,disabled:!y,defaults:{VIcon:{icon:e.icon}}},{default:()=>[r.default?.()??be(e.text)]})]),!e.icon&&v&&H(`span`,{key:`append`,class:`v-btn__append`},[r.append?U(ey,{key:`append-defaults`,disabled:!e.appendIcon,defaults:{VIcon:{icon:e.appendIcon}}},r.append):U(eb,{key:`append-icon`,icon:e.appendIcon},null)]),!!e.loading&&H(`span`,{key:`loader`,class:`v-btn__loader`},[r.loader?.()??U(rb,{color:typeof e.loading==`boolean`?void 0:e.loading,indeterminate:!0,width:`2`},null)])]}),[[Wb,!C.value&&e.ripple,``,{center:!!e.icon}]])}),{group:h}}}),qb=J({...Gb({icon:`$menu`,variant:`text`})},`VAppBarNavIcon`),Jb=Y()({name:`VAppBarNavIcon`,props:qb(),setup(e,t){let{slots:n}=t;return X(()=>U(Kb,W(e,{class:[`v-app-bar-nav-icon`]}),n)),{}}}),Yb=J({start:Boolean,end:Boolean,icon:If,image:String,text:String,...yy(),...ju(),...My(),...ly(),...Zy(),...Iv(),...Kf(),...Iy({variant:`flat`})},`VAvatar`),Xb=Y()({name:`VAvatar`,props:Yb(),setup(e,t){let{slots:n}=t,{themeClasses:r}=ip(e),{borderClasses:i}=by(e),{colorClasses:a,colorStyles:o,variantClasses:s}=Ly(e),{densityClasses:c}=Ny(e),{roundedClasses:l}=uy(e),{sizeClasses:u,sizeStyles:d}=Qy(e);return X(()=>U(e.tag,{class:A([`v-avatar`,{"v-avatar--start":e.start,"v-avatar--end":e.end},r.value,i.value,a.value,c.value,l.value,u.value,s.value,e.class]),style:k([o.value,d.value,e.style])},{default:()=>[n.default?U(ey,{key:`content-defaults`,defaults:{VImg:{cover:!0,src:e.image},VIcon:{icon:e.icon}}},{default:()=>[n.default()]}):e.image?U(vy,{key:`image`,src:e.image,alt:``,cover:!0},null):e.icon?U(eb,{key:`icon`,icon:e.icon},null):e.text,Fy(!1,`v-avatar`)]})),{}}}),Zb=J({color:String,inset:Boolean,length:[Number,String],opacity:[Number,String],thickness:[Number,String],vertical:Boolean,...ju(),...Kf()},`VDivider`),Qb=Y()({name:`VDivider`,props:Zb(),setup(e,t){let{attrs:n,slots:r}=t,{themeClasses:i}=ip(e),{textColorClasses:a,textColorStyles:o}=sy(()=>e.color),s=G(()=>{let t={};return e.length&&(t[e.vertical?`height`:`width`]=q(e.length)),e.thickness&&(t[e.vertical?`borderRightWidth`:`borderTopWidth`]=q(e.thickness)),t});return X(()=>{let t=H(`hr`,{class:A([{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},i.value,a.value,e.class]),style:k([s.value,o.value,{"--v-border-opacity":e.opacity},e.style]),"aria-orientation":!n.role||n.role===`separator`?e.vertical?`vertical`:`horizontal`:void 0,role:`${n.role||`separator`}`},null);return r.default?H(`div`,{class:A([`v-divider__wrapper`,{"v-divider__wrapper--vertical":e.vertical,"v-divider__wrapper--inset":e.inset}])},[t,H(`div`,{class:`v-divider__content`},[r.default()]),t]):t}),{}}}),$b=J({fluid:{type:Boolean,default:!1},...ju(),...ty(),...Iv()},`VContainer`),ex=Y()({name:`VContainer`,props:$b(),setup(e,t){let{slots:n}=t,{rtlClasses:r}=hd(),{dimensionStyles:i}=ny(e);return X(()=>U(e.tag,{class:A([`v-container`,{"v-container--fluid":e.fluid},r.value,e.class]),style:k([i.value,e.style])},n)),{}}}),tx=Uu(`v-spacer`,`div`,`VSpacer`);Symbol.for(`vuetify:depth`);const nx=Symbol.for(`vuetify:list`);function rx(){let{filterable:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{filterable:!1},t=z(nx,{filterable:!1,hasPrepend:F(!1),updateHasPrepend:()=>null}),n={filterable:t.filterable||e,hasPrepend:F(!1),updateHasPrepend:e=>{e&&(n.hasPrepend.value=e)}};return pi(nx,n),t}function ix(){return z(nx,null)}const ax=e=>{let t={activate:t=>{let{id:n,value:r,activated:i}=t;return n=M(n),e&&!r&&i.size===1&&i.has(n)||(r?i.add(n):i.delete(n)),i},in:(e,n,r)=>{let i=new Set;if(e!=null)for(let a of Hc(e))i=t.activate({id:a,value:!0,activated:new Set(i),children:n,parents:r});return i},out:e=>Array.from(e)};return t},ox=e=>{let t=ax(e),n={activate:e=>{let{activated:n,id:r,...i}=e;r=M(r);let a=n.has(r)?new Set([r]):new Set;return t.activate({...i,id:r,activated:a})},in:(e,n,r)=>{let i=new Set;if(e!=null){let a=Hc(e);a.length&&(i=t.in(a.slice(0,1),n,r))}return i},out:(e,n,r)=>t.out(e,n,r)};return n},sx=e=>{let t=ax(e),n={activate:e=>{let{id:n,activated:r,children:i,...a}=e;return n=M(n),i.has(n)?r:t.activate({id:n,activated:r,children:i,...a})},in:t.in,out:t.out};return n},cx=e=>{let t=ox(e),n={activate:e=>{let{id:n,activated:r,children:i,...a}=e;return n=M(n),i.has(n)?r:t.activate({id:n,activated:r,children:i,...a})},in:t.in,out:t.out};return n},lx={open:e=>{let{id:t,value:n,opened:r,parents:i}=e;if(n){let e=new Set;e.add(t);let n=i.get(t);for(;n!=null;)e.add(n),n=i.get(n);return e}else return r.delete(t),r},select:()=>null},ux={open:e=>{let{id:t,value:n,opened:r,parents:i}=e;if(n){let e=i.get(t);for(r.add(t);e!=null&&e!==t;)r.add(e),e=i.get(e);return r}else r.delete(t);return r},select:()=>null},dx={open:ux.open,select:e=>{let{id:t,value:n,opened:r,parents:i}=e;if(!n)return r;let a=[],o=i.get(t);for(;o!=null;)a.push(o),o=i.get(o);return new Set(a)}},fx=e=>{let t={select:t=>{let{id:n,value:r,selected:i}=t;if(n=M(n),e&&!r){let e=Array.from(i.entries()).reduce((e,t)=>{let[n,r]=t;return r===`on`&&e.push(n),e},[]);if(e.length===1&&e[0]===n)return i}return i.set(n,r?`on`:`off`),i},in:(e,n,r,i)=>{let a=new Map;for(let o of e||[])t.select({id:o,value:!0,selected:a,children:n,parents:r,disabled:i});return a},out:e=>{let t=[];for(let[n,r]of e.entries())r===`on`&&t.push(n);return t}};return t},px=e=>{let t=fx(e),n={select:e=>{let{selected:n,id:r,...i}=e;r=M(r);let a=n.has(r)?new Map([[r,n.get(r)]]):new Map;return t.select({...i,id:r,selected:a})},in:(e,n,r,i)=>e?.length?t.in(e.slice(0,1),n,r,i):new Map,out:(e,n,r)=>t.out(e,n,r)};return n},mx=e=>{let t=fx(e),n={select:e=>{let{id:n,selected:r,children:i,...a}=e;return n=M(n),i.has(n)?r:t.select({id:n,selected:r,children:i,...a})},in:t.in,out:t.out};return n},hx=e=>{let t=px(e),n={select:e=>{let{id:n,selected:r,children:i,...a}=e;return n=M(n),i.has(n)?r:t.select({id:n,selected:r,children:i,...a})},in:t.in,out:t.out};return n},gx=e=>{let t={select:t=>{let{id:n,value:r,selected:i,children:a,parents:o,disabled:s}=t;n=M(n);let c=new Map(i),l=[n];for(;l.length;){let e=l.shift();s.has(e)||i.set(M(e),r?`on`:`off`),a.has(e)&&l.push(...a.get(e))}let u=M(o.get(n));for(;u;){let e=!0,t=!0;for(let n of a.get(u)){let r=M(n);if(s.has(r))continue;if(i.get(r)!==`on`&&(e=!1),i.has(r)&&i.get(r)!==`off`&&(t=!1),!e&&!t)break}i.set(u,e?`on`:t?`off`:`indeterminate`),u=M(o.get(u))}if(e&&!r){let e=Array.from(i.entries()).reduce((e,t)=>{let[n,r]=t;return r===`on`&&e.push(n),e},[]);if(e.length===0)return c}return i},in:(e,n,r,i)=>{let a=new Map;for(let o of e||[])a=t.select({id:o,value:!0,selected:a,children:n,parents:r,disabled:i});return a},out:(e,t)=>{let n=[];for(let[r,i]of e.entries())i===`on`&&!t.has(r)&&n.push(r);return n}};return t},_x=e=>{let t=gx(e),n={select:t.select,in:t.in,out:(e,t,n)=>{let r=[];for(let[t,i]of e.entries())if(i===`on`){if(n.has(t)){let r=n.get(t);if(e.get(r)===`on`)continue}r.push(t)}return r}};return n},vx=Symbol.for(`vuetify:nested`),yx={id:F(),root:{register:()=>null,unregister:()=>null,children:P(new Map),parents:P(new Map),disabled:P(new Set),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:P(!1),selectable:P(!1),opened:P(new Set),activated:P(new Set),selected:P(new Map),selectedValues:P([]),getPath:()=>[]}},bx=J({activatable:Boolean,selectable:Boolean,activeStrategy:[String,Function,Object],selectStrategy:[String,Function,Object],openStrategy:[String,Object],opened:null,activated:null,selected:null,mandatory:Boolean},`nested`),xx=e=>{let t=!1,n=F(new Map),r=F(new Map),i=F(new Set),a=$u(e,`opened`,e.opened,e=>new Set(Array.isArray(e)?e.map(e=>M(e)):e),e=>[...e.values()]),o=G(()=>{if(typeof e.activeStrategy==`object`)return e.activeStrategy;if(typeof e.activeStrategy==`function`)return e.activeStrategy(e.mandatory);switch(e.activeStrategy){case`leaf`:return sx(e.mandatory);case`single-leaf`:return cx(e.mandatory);case`independent`:return ax(e.mandatory);case`single-independent`:default:return ox(e.mandatory)}}),s=G(()=>{if(typeof e.selectStrategy==`object`)return e.selectStrategy;if(typeof e.selectStrategy==`function`)return e.selectStrategy(e.mandatory);switch(e.selectStrategy){case`single-leaf`:return hx(e.mandatory);case`leaf`:return mx(e.mandatory);case`independent`:return fx(e.mandatory);case`single-independent`:return px(e.mandatory);case`trunk`:return _x(e.mandatory);case`classic`:default:return gx(e.mandatory)}}),c=G(()=>{if(typeof e.openStrategy==`object`)return e.openStrategy;switch(e.openStrategy){case`list`:return dx;case`single`:return lx;case`multiple`:default:return ux}}),l=$u(e,`activated`,e.activated,e=>o.value.in(e,n.value,r.value),e=>o.value.out(e,n.value,r.value)),u=$u(e,`selected`,e.selected,e=>s.value.in(e,n.value,r.value,i.value),e=>s.value.out(e,n.value,r.value));Dr(()=>{t=!0});function d(e){let t=[],n=M(e);for(;n!=null;)t.unshift(n),n=r.value.get(n);return t}let f=Mu(`nested`),p=new Set,m={id:F(),root:{opened:a,activatable:L(()=>e.activatable),selectable:L(()=>e.selectable),activated:l,selected:u,selectedValues:G(()=>{let e=[];for(let[t,n]of u.value.entries())n===`on`&&e.push(t);return e}),register:(e,t,a,o)=>{if(p.has(e)){let n=d(e).map(String).join(` -> `),r=d(t).concat(e).map(String).join(` -> `);nu(`Multiple nodes with the same ID\n\t${n}\n\t${r}`);return}else p.add(e);t&&e!==t&&r.value.set(e,t),a&&i.value.add(e),o&&n.value.set(e,[]),t!=null&&n.value.set(t,[...n.value.get(t)||[],e])},unregister:e=>{if(t)return;p.delete(e),n.value.delete(e),i.value.delete(e);let a=r.value.get(e);if(a){let t=n.value.get(a)??[];n.value.set(a,t.filter(t=>t!==e))}r.value.delete(e)},open:(e,t,i)=>{f.emit(`click:open`,{id:e,value:t,path:d(e),event:i});let o=c.value.open({id:e,value:t,opened:new Set(a.value),children:n.value,parents:r.value,event:i});o&&(a.value=o)},openOnSelect:(e,t,i)=>{let o=c.value.select({id:e,value:t,selected:new Map(u.value),opened:new Set(a.value),children:n.value,parents:r.value,event:i});o&&(a.value=o)},select:(e,t,a)=>{f.emit(`click:select`,{id:e,value:t,path:d(e),event:a});let o=s.value.select({id:e,value:t,selected:new Map(u.value),children:n.value,parents:r.value,disabled:i.value,event:a});o&&(u.value=o),m.root.openOnSelect(e,t,a)},activate:(t,i,a)=>{if(!e.activatable)return m.root.select(t,!0,a);f.emit(`click:activate`,{id:t,value:i,path:d(t),event:a});let s=o.value.activate({id:t,value:i,activated:new Set(l.value),children:n.value,parents:r.value,event:a});if(s.size!==l.value.size)l.value=s;else{for(let e of s)if(!l.value.has(e)){l.value=s;return}for(let e of l.value)if(!s.has(e)){l.value=s;return}}},children:n,parents:r,disabled:i,getPath:d}};return pi(vx,m),m.root},Sx=(e,t,n)=>{let r=z(vx,yx),i=Symbol(`nested item`),a=G(()=>M(Xt(e))??i),o={...r,id:a,open:(e,t)=>r.root.open(a.value,e,t),openOnSelect:(e,t)=>r.root.openOnSelect(a.value,e,t),isOpen:G(()=>r.root.opened.value.has(a.value)),parent:G(()=>r.root.parents.value.get(a.value)),activate:(e,t)=>r.root.activate(a.value,e,t),isActivated:G(()=>r.root.activated.value.has(a.value)),select:(e,t)=>r.root.select(a.value,e,t),isSelected:G(()=>r.root.selected.value.get(a.value)===`on`),isIndeterminate:G(()=>r.root.selected.value.get(a.value)===`indeterminate`),isLeaf:G(()=>!r.root.children.value.get(a.value)),isGroupActivator:r.isGroupActivator};return Cr(()=>{r.isGroupActivator||r.root.register(a.value,r.id.value,Xt(t),n)}),Dr(()=>{r.isGroupActivator||r.root.unregister(a.value)}),n&&pi(vx,o),o},Cx=()=>{let e=z(vx,yx);pi(vx,{...e,isGroupActivator:!0})},wx=Vu({name:`VListGroupActivator`,setup(e,t){let{slots:n}=t;return Cx(),()=>n.default?.()}}),Tx=J({activeColor:String,baseColor:String,color:String,collapseIcon:{type:If,default:`$collapse`},disabled:Boolean,expandIcon:{type:If,default:`$expand`},rawId:[String,Number],prependIcon:If,appendIcon:If,fluid:Boolean,subgroup:Boolean,title:String,value:null,...ju(),...Iv()},`VListGroup`),Ex=Y()({name:`VListGroup`,props:Tx(),setup(e,t){let{slots:n}=t,{isOpen:r,open:i,id:a}=Sx(()=>e.value,()=>e.disabled,!0),o=G(()=>`v-list-group--id-${String(e.rawId??a.value)}`),s=ix(),{isBooted:c}=Oy();function l(e){[`INPUT`,`TEXTAREA`].includes(e.target?.tagName)||i(!r.value,e)}let u=G(()=>({onClick:l,class:`v-list-group__header`,id:o.value})),d=G(()=>r.value?e.collapseIcon:e.expandIcon),f=G(()=>({VListItem:{activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&d.value,appendIcon:e.appendIcon||!e.subgroup&&d.value,title:e.title,value:e.value}}));return X(()=>U(e.tag,{class:A([`v-list-group`,{"v-list-group--prepend":s?.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":r.value},e.class]),style:k(e.style)},{default:()=>[n.activator&&U(ey,{defaults:f.value},{default:()=>[U(wx,null,{default:()=>[n.activator({props:u.value,isOpen:r.value})]})]}),U(fy,{transition:{component:Zv},disabled:!c.value},{default:()=>[Fn(H(`div`,{class:`v-list-group__items`,role:`group`,"aria-labelledby":o.value},[n.default?.()]),[[Io,r.value]])]})]})),{isOpen:r}}}),Dx=J({opacity:[Number,String],...ju(),...Iv()},`VListItemSubtitle`),Ox=Y()({name:`VListItemSubtitle`,props:Dx(),setup(e,t){let{slots:n}=t;return X(()=>U(e.tag,{class:A([`v-list-item-subtitle`,e.class]),style:k([{"--v-list-item-subtitle-opacity":e.opacity},e.style])},n)),{}}}),kx=Uu(`v-list-item-title`),Ax=J({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:If,baseColor:String,disabled:Boolean,lines:[Boolean,String],link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:If,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},value:null,onClick:il(),onClickOnce:il(),...yy(),...ju(),...My(),...ty(),...xy(),...ly(),...vb(),...Iv(),...Kf(),...Iy({variant:`text`})},`VListItem`),jx=Y()({name:`VListItem`,directives:{vRipple:Wb},props:Ax(),emits:{click:e=>!0},setup(e,t){let{attrs:n,slots:r,emit:i}=t,a=_b(e,n),o=G(()=>e.value===void 0?a.href.value:e.value),{activate:s,isActivated:c,select:l,isOpen:u,isSelected:d,isIndeterminate:f,isGroupActivator:p,root:m,parent:h,openOnSelect:g,id:_}=Sx(o,()=>e.disabled,!1),v=ix(),y=G(()=>e.active!==!1&&(e.active||a.isActive?.value||(m.activatable.value?c.value:d.value))),b=L(()=>e.link!==!1&&a.isLink.value),x=G(()=>!!v&&(m.selectable.value||m.activatable.value||e.value!=null)),S=G(()=>!e.disabled&&e.link!==!1&&(e.link||a.isClickable.value||x.value)),C=L(()=>e.rounded||e.nav),w=L(()=>e.color??e.activeColor),T=L(()=>({color:y.value?w.value??e.baseColor:e.baseColor,variant:e.variant}));B(()=>a.isActive?.value,e=>{e&&E()}),Cr(()=>{a.isActive?.value&&E()});function E(){h.value!=null&&m.open(h.value,!0),g(!0)}let{themeClasses:ee}=ip(e),{borderClasses:te}=by(e),{colorClasses:D,colorStyles:ne,variantClasses:re}=Ly(T),{densityClasses:ie}=Ny(e),{dimensionStyles:ae}=ny(e),{elevationClasses:oe}=Sy(e),{roundedClasses:se}=uy(C),O=L(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),ce=L(()=>e.ripple!==void 0&&e.ripple&&v?.filterable?{keys:[`Enter`]}:e.ripple),le=G(()=>({isActive:y.value,select:l,isOpen:u.value,isSelected:d.value,isIndeterminate:f.value}));function ue(t){i(`click`,t),![`INPUT`,`TEXTAREA`].includes(t.target?.tagName)&&S.value&&(a.navigate?.(t),!p&&(m.activatable.value?s(!c.value,t):(m.selectable.value||e.value!=null)&&l(!d.value,t)))}function de(e){let t=e.target;[`INPUT`,`TEXTAREA`].includes(t.tagName)||(e.key===`Enter`||e.key===` `&&!v?.filterable)&&(e.preventDefault(),e.stopPropagation(),e.target.dispatchEvent(new MouseEvent(`click`,e)))}return X(()=>{let t=b.value?`a`:e.tag,n=r.title||e.title!=null,i=r.subtitle||e.subtitle!=null,o=!!(e.appendAvatar||e.appendIcon),s=!!(o||r.append),l=!!(e.prependAvatar||e.prependIcon),u=!!(l||r.prepend);return v?.updateHasPrepend(u),e.activeColor&&ru(`active-color`,[`color`,`base-color`]),Fn(U(t,W({class:[`v-list-item`,{"v-list-item--active":y.value,"v-list-item--disabled":e.disabled,"v-list-item--link":S.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!u&&v?.hasPrepend.value,"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&y.value},ee.value,te.value,D.value,ie.value,oe.value,O.value,se.value,re.value,e.class],style:[ne.value,ae.value,e.style],tabindex:S.value?v?-2:0:void 0,"aria-selected":x.value?m.activatable.value?c.value:m.selectable.value?d.value:y.value:void 0,onClick:ue,onKeydown:S.value&&!b.value&&de},a.linkProps),{default:()=>[Fy(S.value||y.value,`v-list-item`),u&&H(`div`,{key:`prepend`,class:`v-list-item__prepend`},[r.prepend?U(ey,{key:`prepend-defaults`,disabled:!l,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>[r.prepend?.(le.value)]}):H(V,null,[e.prependAvatar&&U(Xb,{key:`prepend-avatar`,density:e.density,image:e.prependAvatar},null),e.prependIcon&&U(eb,{key:`prepend-icon`,density:e.density,icon:e.prependIcon},null)]),H(`div`,{class:`v-list-item__spacer`},null)]),H(`div`,{class:`v-list-item__content`,"data-no-activator":``},[n&&U(kx,{key:`title`},{default:()=>[r.title?.({title:e.title})??be(e.title)]}),i&&U(Ox,{key:`subtitle`},{default:()=>[r.subtitle?.({subtitle:e.subtitle})??be(e.subtitle)]}),r.default?.(le.value)]),s&&H(`div`,{key:`append`,class:`v-list-item__append`},[r.append?U(ey,{key:`append-defaults`,disabled:!o,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>[r.append?.(le.value)]}):H(V,null,[e.appendIcon&&U(eb,{key:`append-icon`,density:e.density,icon:e.appendIcon},null),e.appendAvatar&&U(Xb,{key:`append-avatar`,density:e.density,image:e.appendAvatar},null)]),H(`div`,{class:`v-list-item__spacer`},null)])]}),[[Wb,S.value&&ce.value]])}),{activate:s,isActivated:c,isGroupActivator:p,isSelected:d,list:v,select:l,root:m,id:_,link:a}}}),Mx=J({color:String,inset:Boolean,sticky:Boolean,title:String,...ju(),...Iv()},`VListSubheader`),Nx=Y()({name:`VListSubheader`,props:Mx(),setup(e,t){let{slots:n}=t,{textColorClasses:r,textColorStyles:i}=sy(()=>e.color);return X(()=>{let t=!!(n.default||e.title);return U(e.tag,{class:A([`v-list-subheader`,{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},r.value,e.class]),style:k([{textColorStyles:i},e.style])},{default:()=>[t&&H(`div`,{class:`v-list-subheader__text`},[n.default?.()??e.title])]})}),{}}}),Px=J({items:Array,returnObject:Boolean},`VListChildren`),Fx=Y()({name:`VListChildren`,props:Px(),setup(e,t){let{slots:n}=t;return rx(),()=>n.default?.()??e.items?.map(t=>{let{children:r,props:i,type:a,raw:o}=t;if(a===`divider`)return n.divider?.({props:i})??U(Qb,i,null);if(a===`subheader`)return n.subheader?.({props:i})??U(Nx,i,null);let s={subtitle:n.subtitle?e=>n.subtitle?.({...e,item:o}):void 0,prepend:n.prepend?e=>n.prepend?.({...e,item:o}):void 0,append:n.append?e=>n.append?.({...e,item:o}):void 0,title:n.title?e=>n.title?.({...e,item:o}):void 0},c=Ex.filterProps(i);return r?U(Ex,W(c,{value:e.returnObject?o:i?.value,rawId:i?.value}),{activator:t=>{let{props:r}=t,a={...i,...r,value:e.returnObject?o:i.value};return n.header?n.header({props:a}):U(jx,a,s)},default:()=>U(Fx,{items:r,returnObject:e.returnObject},n)}):n.item?n.item({props:i}):U(jx,W(i,{value:e.returnObject?o:i.value}),s)})}}),Ix=J({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:`title`},itemValue:{type:[String,Array,Function],default:`value`},itemChildren:{type:[Boolean,String,Array,Function],default:`children`},itemProps:{type:[Boolean,String,Array,Function],default:`props`},itemType:{type:[Boolean,String,Array,Function],default:`type`},returnObject:Boolean,valueComparator:Function},`list-items`),Lx=new Set([`item`,`divider`,`subheader`]);function Rx(e,t){let n=Dc(t,e.itemTitle,t),r=Dc(t,e.itemValue,n),i=Dc(t,e.itemChildren),a=e.itemProps===!0?typeof t==`object`&&t&&!Array.isArray(t)?`children`in t?Lc(t,[`children`]):t:void 0:Dc(t,e.itemProps),o=Dc(t,e.itemType,`item`);Lx.has(o)||(o=`item`);let s={title:n,value:r,...a};return{type:o,title:String(s.title??``),value:s.value,props:s,children:o===`item`&&Array.isArray(i)?zx(e,i):void 0,raw:t}}Rx.neededProps=[`itemTitle`,`itemValue`,`itemChildren`,`itemProps`,`itemType`];function zx(e,t){let n=Fc(e,Rx.neededProps),r=[];for(let e of t)r.push(Rx(n,e));return r}function Bx(e){let t=G(()=>zx(e,e.items)),n=G(()=>t.value.some(e=>e.value===null)),r=F(new Map),i=F([]);Gi(()=>{let e=t.value,n=new Map,a=[];for(let t=0;t<e.length;t++){let r=e[t];if(_l(r.value)||r.value===null){let e=n.get(r.value);e||(e=[],n.set(r.value,e)),e.push(r)}else a.push(r)}r.value=n,i.value=a});function a(a){let o=r.value,s=t.value,c=i.value,l=n.value,u=e.returnObject,d=!!e.valueComparator,f=e.valueComparator||Tc,p=Fc(e,Rx.neededProps),m=[];main:for(let e of a){if(!l&&e===null)continue;if(u&&typeof e==`string`){m.push(Rx(p,e));continue}let t=o.get(e);if(d||!t){for(let t of d?s:c)if(f(e,t.value)){m.push(t);continue main}m.push(Rx(p,e));continue}m.push(...t)}return m}function o(t){return e.returnObject?t.map(e=>{let{raw:t}=e;return t}):t.map(e=>{let{value:t}=e;return t})}return{items:t,transformIn:a,transformOut:o}}const Vx=new Set([`item`,`divider`,`subheader`]);function Hx(e,t){let n=_l(t)?t:Dc(t,e.itemTitle),r=_l(t)?t:Dc(t,e.itemValue,void 0),i=Dc(t,e.itemChildren),a=e.itemProps===!0?Lc(t,[`children`]):Dc(t,e.itemProps),o=Dc(t,e.itemType,`item`);Vx.has(o)||(o=`item`);let s={title:n,value:r,...a};return{type:o,title:s.title,value:s.value,props:s,children:o===`item`&&i?Ux(e,i):void 0,raw:t}}function Ux(e,t){let n=[];for(let r of t)n.push(Hx(e,r));return n}function Wx(e){let t=G(()=>Ux(e,e.items));return{items:t}}const Gx=J({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,filterable:Boolean,expandIcon:If,collapseIcon:If,lines:{type:[Boolean,String],default:`one`},slim:Boolean,nav:Boolean,"onClick:open":il(),"onClick:select":il(),"onUpdate:opened":il(),...bx({selectStrategy:`single-leaf`,openStrategy:`list`}),...yy(),...ju(),...My(),...ty(),...xy(),...Ix(),...ly(),...Iv(),...Kf(),...Iy({variant:`text`})},`VList`),Kx=Y()({name:`VList`,props:Gx(),emits:{"update:selected":e=>!0,"update:activated":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:activate":e=>!0,"click:select":e=>!0},setup(e,t){let{slots:n}=t,{items:r}=Wx(e),{themeClasses:i}=ip(e),{backgroundColorClasses:a,backgroundColorStyles:o}=cy(()=>e.bgColor),{borderClasses:s}=by(e),{densityClasses:c}=Ny(e),{dimensionStyles:l}=ny(e),{elevationClasses:u}=Sy(e),{roundedClasses:d}=uy(e),{children:f,open:p,parents:m,select:h,getPath:g}=xx(e),_=L(()=>e.lines?`v-list--${e.lines}-line`:void 0),v=L(()=>e.activeColor),y=L(()=>e.baseColor),b=L(()=>e.color);rx({filterable:e.filterable}),Ru({VListGroup:{activeColor:v,baseColor:y,color:b,expandIcon:L(()=>e.expandIcon),collapseIcon:L(()=>e.collapseIcon)},VListItem:{activeClass:L(()=>e.activeClass),activeColor:v,baseColor:y,color:b,density:L(()=>e.density),disabled:L(()=>e.disabled),lines:L(()=>e.lines),nav:L(()=>e.nav),slim:L(()=>e.slim),variant:L(()=>e.variant)}});let x=F(!1),S=P();function C(e){x.value=!0}function w(e){x.value=!1}function T(e){!x.value&&!(e.relatedTarget&&S.value?.contains(e.relatedTarget))&&te()}function E(e){let t=e.target;if(!(!S.value||t.tagName===`INPUT`&&[`Home`,`End`].includes(e.key)||t.tagName===`TEXTAREA`)){if(e.key===`ArrowDown`)te(`next`);else if(e.key===`ArrowUp`)te(`prev`);else if(e.key===`Home`)te(`first`);else if(e.key===`End`)te(`last`);else return;e.preventDefault()}}function ee(e){x.value=!0}function te(e){if(S.value)return ll(S.value,e)}return X(()=>U(e.tag,{ref:S,class:A([`v-list`,{"v-list--disabled":e.disabled,"v-list--nav":e.nav,"v-list--slim":e.slim},i.value,a.value,s.value,c.value,u.value,_.value,d.value,e.class]),style:k([o.value,l.value,e.style]),tabindex:e.disabled?-1:0,role:`listbox`,"aria-activedescendant":void 0,onFocusin:C,onFocusout:w,onFocus:T,onKeydown:E,onMousedown:ee},{default:()=>[U(Fx,{items:r.value,returnObject:e.returnObject},n)]})),{open:p,select:h,focus:te,children:f,parents:m,getPath:g}}}),qx=J({scrollable:Boolean,...ju(),...ty(),...Iv({tag:`main`})},`VMain`),Jx=Y()({name:`VMain`,props:qx(),setup(e,t){let{slots:n}=t,{dimensionStyles:r}=ny(e),{mainStyles:i}=fp(),{ssrBootStyles:a}=Oy();return X(()=>U(e.tag,{class:A([`v-main`,{"v-main--scrollable":e.scrollable},e.class]),style:k([i.value,a.value,r.value,e.style])},{default:()=>[e.scrollable?H(`div`,{class:`v-main__scroller`},[n.default?.()]):n.default?.()]})),{}}});function Yx(e,t){return{x:e.x+t.x,y:e.y+t.y}}function Xx(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Zx(e,t){if(e.side===`top`||e.side===`bottom`){let{side:n,align:r}=e,i=r===`left`?0:r===`center`?t.width/2:r===`right`?t.width:r,a=n===`top`?0:n===`bottom`?t.height:n;return Yx({x:i,y:a},t)}else if(e.side===`left`||e.side===`right`){let{side:n,align:r}=e,i=n===`left`?0:n===`right`?t.width:n,a=r===`top`?0:r===`center`?t.height/2:r===`bottom`?t.height:r;return Yx({x:i,y:a},t)}return Yx({x:t.width/2,y:t.height/2},t)}const Qx={static:tS,connected:rS},$x=J({locationStrategy:{type:[String,Function],default:`static`,validator:e=>typeof e==`function`||e in Qx},location:{type:String,default:`bottom`},origin:{type:String,default:`auto`},offset:[Number,String,Array]},`VOverlay-location-strategies`);function eS(e,t){let n=P({}),r=P();K&&hc(()=>!!(t.isActive.value&&e.locationStrategy),s=>{B(()=>e.locationStrategy,s),De(()=>{window.removeEventListener(`resize`,i),visualViewport?.removeEventListener(`resize`,a),visualViewport?.removeEventListener(`scroll`,o),r.value=void 0}),window.addEventListener(`resize`,i,{passive:!0}),visualViewport?.addEventListener(`resize`,a,{passive:!0}),visualViewport?.addEventListener(`scroll`,o,{passive:!0}),typeof e.locationStrategy==`function`?r.value=e.locationStrategy(t,e,n)?.updateLocation:r.value=Qx[e.locationStrategy](t,e,n)?.updateLocation});function i(e){r.value?.(e)}function a(e){r.value?.(e)}function o(e){r.value?.(e)}return{contentStyles:n,updateLocation:r}}function tS(){}function nS(e,t){let n=Ml(e);return t?n.x+=parseFloat(e.style.right||0):n.x-=parseFloat(e.style.left||0),n.y-=parseFloat(e.style.top||0),n}function rS(e,t,n){let r=Array.isArray(e.target.value)||Qu(e.target.value);r&&Object.assign(n.value,{position:`fixed`,top:0,[e.isRtl.value?`right`:`left`]:0});let{preferredAnchor:i,preferredOrigin:a}=tl(()=>{let n=Sl(t.location,e.isRtl.value),r=t.origin===`overlap`?n:t.origin===`auto`?wl(n):Sl(t.origin,e.isRtl.value);return n.side===r.side&&n.align===Tl(r).align?{preferredAnchor:El(n),preferredOrigin:El(r)}:{preferredAnchor:n,preferredOrigin:r}}),[o,s,c,l]=[`minWidth`,`minHeight`,`maxWidth`,`maxHeight`].map(e=>G(()=>{let n=parseFloat(t[e]);return isNaN(n)?1/0:n})),u=G(()=>{if(Array.isArray(t.offset))return t.offset;if(typeof t.offset==`string`){let e=t.offset.split(` `).map(parseFloat);return e.length<2&&e.push(0),e}return typeof t.offset==`number`?[t.offset,0]:[0,0]}),d=!1,f=-1,p=new el(4),m=new ResizeObserver(()=>{if(!d)return;if(requestAnimationFrame(e=>{e!==f&&p.clear(),requestAnimationFrame(e=>{f=e})}),p.isFull){let e=p.values();if(Tc(e.at(-1),e.at(-3))&&!Tc(e.at(-1),e.at(-2)))return}let e=g();e&&p.push(e.flipped)});B([e.target,e.contentEl],(e,t)=>{let[n,r]=e,[i,a]=t;i&&!Array.isArray(i)&&m.unobserve(i),n&&!Array.isArray(n)&&m.observe(n),a&&m.unobserve(a),r&&m.observe(r)},{immediate:!0}),De(()=>{m.disconnect()});let h=new Ol({x:0,y:0,width:0,height:0});function g(){if(d=!1,requestAnimationFrame(()=>d=!0),!e.target.value||!e.contentEl.value)return;(Array.isArray(e.target.value)||e.target.value.offsetParent||e.target.value.getClientRects().length)&&(h=Al(e.target.value));let t=nS(e.contentEl.value,e.isRtl.value),r=Yu(e.contentEl.value),f=12;r.length||(r.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(t.x-=parseFloat(document.documentElement.style.getPropertyValue(`--v-body-scroll-x`)||0),t.y-=parseFloat(document.documentElement.style.getPropertyValue(`--v-body-scroll-y`)||0)));let p=r.reduce((e,t)=>{let n=jl(t);return e?new Ol({x:Math.max(e.left,n.left),y:Math.max(e.top,n.top),width:Math.min(e.right,n.right)-Math.max(e.left,n.left),height:Math.min(e.bottom,n.bottom)-Math.max(e.top,n.top)}):n},void 0);p.x+=f,p.y+=f,p.width-=f*2,p.height-=f*2;let m={anchor:i.value,origin:a.value};function g(e){let n=new Ol(t),r=Zx(e.anchor,h),i=Zx(e.origin,n),{x:a,y:o}=Xx(r,i);switch(e.anchor.side){case`top`:o-=u.value[0];break;case`bottom`:o+=u.value[0];break;case`left`:a-=u.value[0];break;case`right`:a+=u.value[0];break}switch(e.anchor.align){case`top`:o-=u.value[1];break;case`bottom`:o+=u.value[1];break;case`left`:a-=u.value[1];break;case`right`:a+=u.value[1];break}n.x+=a,n.y+=o,n.width=Math.min(n.width,c.value),n.height=Math.min(n.height,l.value);let s=kl(n,p);return{overflows:s,x:a,y:o}}let _=0,v=0,y={x:0,y:0},b={x:!1,y:!1},x=-1;for(;;){if(x++>10){nu(`Infinite loop detected in connectedLocationStrategy`);break}let{x:e,y:n,overflows:r}=g(m);_+=e,v+=n,t.x+=e,t.y+=n;{let e=Dl(m.anchor),t=r.x.before||r.x.after,n=r.y.before||r.y.after,i=!1;if([`x`,`y`].forEach(a=>{if(a===`x`&&t&&!b.x||a===`y`&&n&&!b.y){let t={anchor:{...m.anchor},origin:{...m.origin}},n=a===`x`?e===`y`?Tl:wl:e===`y`?wl:Tl;t.anchor=n(t.anchor),t.origin=n(t.origin);let{overflows:o}=g(t);(o[a].before<=r[a].before&&o[a].after<=r[a].after||o[a].before+o[a].after<(r[a].before+r[a].after)/2)&&(m=t,i=b[a]=!0)}}),i)continue}r.x.before&&(_+=r.x.before,t.x+=r.x.before),r.x.after&&(_-=r.x.after,t.x-=r.x.after),r.y.before&&(v+=r.y.before,t.y+=r.y.before),r.y.after&&(v-=r.y.after,t.y-=r.y.after);{let e=kl(t,p);y.x=p.width-e.x.before-e.x.after,y.y=p.height-e.y.before-e.y.after,_+=e.x.before,t.x+=e.x.before,v+=e.y.before,t.y+=e.y.before}break}let S=Dl(m.anchor);return Object.assign(n.value,{"--v-overlay-anchor-origin":`${m.anchor.side} ${m.anchor.align}`,transformOrigin:`${m.origin.side} ${m.origin.align}`,top:q(iS(v)),left:e.isRtl.value?void 0:q(iS(_)),right:e.isRtl.value?q(iS(-_)):void 0,minWidth:q(S===`y`?Math.min(o.value,h.width):o.value),maxWidth:q(aS(Wc(y.x,o.value===1/0?0:o.value,c.value))),maxHeight:q(aS(Wc(y.y,s.value===1/0?0:s.value,l.value)))}),{available:y,contentBox:t,flipped:b}}return B(()=>[i.value,a.value,t.offset,t.minWidth,t.minHeight,t.maxWidth,t.maxHeight],()=>g()),Cn(()=>{let e=g();if(!e)return;let{available:t,contentBox:n}=e;n.height>t.y&&requestAnimationFrame(()=>{g(),requestAnimationFrame(()=>{g()})})}),{updateLocation:g}}function iS(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function aS(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let oS=!0;const sS=[];function cS(e){!oS||sS.length?(sS.push(e),uS()):(oS=!1,e(),uS())}let lS=-1;function uS(){cancelAnimationFrame(lS),lS=requestAnimationFrame(()=>{let e=sS.shift();e&&e(),sS.length?uS():oS=!0})}const dS={none:null,close:mS,block:hS,reposition:gS},fS=J({scrollStrategy:{type:[String,Function],default:`block`,validator:e=>typeof e==`function`||e in dS}},`VOverlay-scroll-strategies`);function pS(e,t){if(!K)return;let n;Gi(async()=>{n?.stop(),t.isActive.value&&e.scrollStrategy&&(n=Te(),await new Promise(e=>setTimeout(e)),n.active&&n.run(()=>{typeof e.scrollStrategy==`function`?e.scrollStrategy(t,e,n):dS[e.scrollStrategy]?.(t,e,n)}))}),De(()=>{n?.stop()})}function mS(e){function t(t){e.isActive.value=!1}_S(e.targetEl.value??e.contentEl.value,t)}function hS(e,t){let n=e.root.value?.offsetParent,r=[...new Set([...Yu(e.targetEl.value,t.contained?n:void 0),...Yu(e.contentEl.value,t.contained?n:void 0)])].filter(e=>!e.classList.contains(`v-overlay-scroll-blocked`)),i=window.innerWidth-document.documentElement.offsetWidth,a=(e=>Xu(e)&&e)(n||document.documentElement);a&&e.root.value.classList.add(`v-overlay--scroll-blocked`),r.forEach((e,t)=>{e.style.setProperty(`--v-body-scroll-x`,q(-e.scrollLeft)),e.style.setProperty(`--v-body-scroll-y`,q(-e.scrollTop)),e!==document.documentElement&&e.style.setProperty(`--v-scrollbar-offset`,q(i)),e.classList.add(`v-overlay-scroll-blocked`)}),De(()=>{r.forEach((e,t)=>{let n=parseFloat(e.style.getPropertyValue(`--v-body-scroll-x`)),r=parseFloat(e.style.getPropertyValue(`--v-body-scroll-y`)),i=e.style.scrollBehavior;e.style.scrollBehavior=`auto`,e.style.removeProperty(`--v-body-scroll-x`),e.style.removeProperty(`--v-body-scroll-y`),e.style.removeProperty(`--v-scrollbar-offset`),e.classList.remove(`v-overlay-scroll-blocked`),e.scrollLeft=-n,e.scrollTop=-r,e.style.scrollBehavior=i}),a&&e.root.value.classList.remove(`v-overlay--scroll-blocked`)})}function gS(e,t,n){let r=!1,i=-1,a=-1;function o(t){cS(()=>{let n=performance.now();e.updateLocation.value?.(t);let i=performance.now()-n;r=i/(1e3/60)>2})}a=(typeof requestIdleCallback>`u`?e=>e():requestIdleCallback)(()=>{n.run(()=>{_S(e.targetEl.value??e.contentEl.value,e=>{r?(cancelAnimationFrame(i),i=requestAnimationFrame(()=>{i=requestAnimationFrame(()=>{o(e)})})):o(e)})})}),De(()=>{typeof cancelIdleCallback<`u`&&cancelIdleCallback(a),cancelAnimationFrame(i)})}function _S(e,t){let n=[document,...Yu(e)];n.forEach(e=>{e.addEventListener(`scroll`,t,{passive:!0})}),De(()=>{n.forEach(e=>{e.removeEventListener(`scroll`,t)})})}const vS=Symbol.for(`vuetify:v-menu`),yS=J({closeDelay:[Number,String],openDelay:[Number,String]},`delay`);function bS(e,t){let n=()=>{};function r(r){n?.();let i=Number(r?e.openDelay:e.closeDelay);return new Promise(e=>{n=pl(i,()=>{t?.(r),e(r)})})}function i(){return r(!0)}function a(){return r(!1)}return{clearDelay:n,runOpenDelay:i,runCloseDelay:a}}const xS=J({target:[String,Object],activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...yS()},`VOverlay-activator`);function SS(e,t){let{isActive:n,isTop:r,contentEl:i}=t,a=Mu(`useActivator`),o=P(),s=!1,c=!1,l=!0,u=G(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),d=G(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!u.value),{runOpenDelay:f,runCloseDelay:p}=bS(e,t=>{t===(e.openOnHover&&s||u.value&&c)&&!(e.openOnHover&&n.value&&!r.value)&&(n.value!==t&&(l=!0),n.value=t)}),m=P(),h={onClick:e=>{e.stopPropagation(),o.value=e.currentTarget||e.target,n.value||(m.value=[e.clientX,e.clientY]),n.value=!n.value},onMouseenter:e=>{e.sourceCapabilities?.firesTouchEvents||(s=!0,o.value=e.currentTarget||e.target,f())},onMouseleave:e=>{s=!1,p()},onFocus:e=>{dl(e.target,`:focus-visible`)!==!1&&(c=!0,e.stopPropagation(),o.value=e.currentTarget||e.target,f())},onBlur:e=>{c=!1,e.stopPropagation(),p()}},g=G(()=>{let t={};return d.value&&(t.onClick=h.onClick),e.openOnHover&&(t.onMouseenter=h.onMouseenter,t.onMouseleave=h.onMouseleave),u.value&&(t.onFocus=h.onFocus,t.onBlur=h.onBlur),t}),_=G(()=>{let t={};if(e.openOnHover&&(t.onMouseenter=()=>{s=!0,f()},t.onMouseleave=()=>{s=!1,p()}),u.value&&(t.onFocusin=()=>{c=!0,f()},t.onFocusout=()=>{c=!1,p()}),e.closeOnContentClick){let e=z(vS,null);t.onClick=()=>{n.value=!1,e?.closeParents()}}return t}),v=G(()=>{let t={};return e.openOnHover&&(t.onMouseenter=()=>{l&&(s=!0,l=!1,f())},t.onMouseleave=()=>{s=!1,p()}),t});B(r,t=>{t&&(e.openOnHover&&!s&&(!u.value||!c)||u.value&&!c&&(!e.openOnHover||!s))&&!i.value?.contains(document.activeElement)&&(n.value=!1)}),B(n,e=>{e||setTimeout(()=>{m.value=void 0})},{flush:`post`});let y=hl();Gi(()=>{y.value&&Cn(()=>{o.value=y.el})});let b=hl(),x=G(()=>e.target===`cursor`&&m.value?m.value:b.value?b.el:wS(e.target,a)||o.value),S=G(()=>Array.isArray(x.value)?void 0:x.value),C;return B(()=>!!e.activator,t=>{t&&K?(C=Te(),C.run(()=>{CS(e,a,{activatorEl:o,activatorEvents:g})})):C&&C.stop()},{flush:`post`,immediate:!0}),De(()=>{C?.stop()}),{activatorEl:o,activatorRef:y,target:x,targetEl:S,targetRef:b,activatorEvents:g,contentEvents:_,scrimEvents:v}}function CS(e,t,n){let{activatorEl:r,activatorEvents:i}=n;B(()=>e.activator,(e,t)=>{if(t&&e!==t){let e=s(t);e&&o(e)}e&&Cn(()=>a())},{immediate:!0}),B(()=>e.activatorProps,()=>{a()}),De(()=>{o()});function a(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:s(),n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;t&&Fl(t,W(i.value,n))}function o(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:s(),n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;t&&Il(t,W(i.value,n))}function s(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator,i=wS(n,t);return r.value=i?.nodeType===Node.ELEMENT_NODE?i:void 0,r.value}}function wS(e,t){if(!e)return;let n;if(e===`parent`){let e=t?.proxy?.$el?.parentNode;for(;e?.hasAttribute(`data-no-activator`);)e=e.parentNode;n=e}else n=typeof e==`string`?document.querySelector(e):`$el`in e?e.$el:e;return n}function TS(){if(!K)return F(!1);let{ssr:e}=wf();if(e){let e=F(!1);return wr(()=>{e.value=!0}),e}else return F(!0)}const ES=J({eager:Boolean},`lazy`);function DS(e,t){let n=F(!1),r=L(()=>n.value||e.eager||t.value);B(t,()=>n.value=!0);function i(){e.eager||(n.value=!1)}return{isBooted:n,hasContent:r,onAfterLeave:i}}function OS(){let e=Mu(`useScopeId`),t=e.vnode.scopeId;return{scopeId:t?{[t]:``}:void 0}}const kS=Symbol.for(`vuetify:stack`),AS=Lt([]);function jS(e,t,n){let r=Mu(`useStack`),i=!n,a=z(kS,void 0),o=Lt({activeChildren:new Set});pi(kS,o);let s=F(Number(Xt(t)));hc(e,()=>{let e=AS.at(-1)?.[1];s.value=e?e+10:Number(Xt(t)),i&&AS.push([r.uid,s.value]),a?.activeChildren.add(r.uid),De(()=>{if(i){let e=M(AS).findIndex(e=>e[0]===r.uid);AS.splice(e,1)}a?.activeChildren.delete(r.uid)})});let c=F(!0);i&&Gi(()=>{let e=AS.at(-1)?.[0]===r.uid;setTimeout(()=>c.value=e)});let l=L(()=>!o.activeChildren.size);return{globalTop:zt(c),localTop:l,stackStyles:L(()=>({zIndex:s.value}))}}function MS(e){let t=G(()=>{let t=e();if(t===!0||!K)return;let n=t===!1?document.body:typeof t==`string`?document.querySelector(t):t;if(n==null){no(`Unable to locate target ${t}`);return}let r=[...n.children].find(e=>e.matches(`.v-overlay-container`));return r||(r=document.createElement(`div`),r.className=`v-overlay-container`,n.appendChild(r)),r});return{teleportTarget:t}}function NS(){return!0}function PS(e,t,n){if(!e||FS(e,n)===!1)return!1;let r=Wu(t);if(typeof ShadowRoot<`u`&&r instanceof ShadowRoot&&r.host===e.target)return!1;let i=(typeof n.value==`object`&&n.value.include||(()=>[]))();return i.push(t),!i.some(t=>t?.contains(e.target))}function FS(e,t){let n=typeof t.value==`object`&&t.value.closeConditional||NS;return n(e)}function IS(e,t,n){let r=typeof n.value==`function`?n.value:n.value.handler;e.shadowTarget=e.target,t._clickOutside.lastMousedownWasOutside&&PS(e,t,n)&&setTimeout(()=>{FS(e,n)&&r&&r(e)},0)}function LS(e,t){let n=Wu(e);t(document),typeof ShadowRoot<`u`&&n instanceof ShadowRoot&&t(n)}const RS={mounted(e,t){let n=n=>IS(n,e,t),r=n=>{e._clickOutside.lastMousedownWasOutside=PS(n,e,t)};LS(e,e=>{e.addEventListener(`click`,n,!0),e.addEventListener(`mousedown`,r,!0)}),e._clickOutside||={lastMousedownWasOutside:!1},e._clickOutside[t.instance.$.uid]={onClick:n,onMousedown:r}},beforeUnmount(e,t){e._clickOutside&&(LS(e,n=>{if(!n||!e._clickOutside?.[t.instance.$.uid])return;let{onClick:r,onMousedown:i}=e._clickOutside[t.instance.$.uid];n.removeEventListener(`click`,r,!0),n.removeEventListener(`mousedown`,i,!0)}),delete e._clickOutside[t.instance.$.uid])}};var zS=RS;function BS(e){let{modelValue:t,color:n,...r}=e;return U(vo,{name:`fade-transition`,appear:!0},{default:()=>[e.modelValue&&H(`div`,W({class:[`v-overlay__scrim`,e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},r),null)]})}const VS=J({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,opacity:[Number,String],noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...xS(),...ju(),...ty(),...ES(),...$x(),...fS(),...Kf(),...dy()},`VOverlay`),HS=Y()({name:`VOverlay`,directives:{vClickOutside:zS},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...VS()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,keydown:e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,t){let{slots:n,attrs:r,emit:i}=t,a=Mu(`VOverlay`),o=P(),s=P(),c=P(),l=$u(e,`modelValue`),u=G({get:()=>l.value,set:t=>{t&&e.disabled||(l.value=t)}}),{themeClasses:d}=ip(e),{rtlClasses:f,isRtl:p}=hd(),{hasContent:m,onAfterLeave:h}=DS(e,u),g=cy(()=>typeof e.scrim==`string`?e.scrim:null),{globalTop:_,localTop:v,stackStyles:y}=jS(u,()=>e.zIndex,e._disableGlobalStack),{activatorEl:b,activatorRef:x,target:S,targetEl:C,targetRef:w,activatorEvents:T,contentEvents:E,scrimEvents:ee}=SS(e,{isActive:u,isTop:v,contentEl:c}),{teleportTarget:te}=MS(()=>{let t=e.attach||e.contained;if(t)return t;let n=b?.value?.getRootNode()||a.proxy?.$el?.getRootNode();return n instanceof ShadowRoot?n:!1}),{dimensionStyles:D}=ny(e),ne=TS(),{scopeId:re}=OS();B(()=>e.disabled,e=>{e&&(u.value=!1)});let{contentStyles:ie,updateLocation:ae}=eS(e,{isRtl:p,contentEl:c,target:S,isActive:u});pS(e,{root:o,contentEl:c,targetEl:C,isActive:u,updateLocation:ae});function oe(t){i(`click:outside`,t),e.persistent?de():u.value=!1}function se(t){return u.value&&_.value&&(!e.scrim||t.target===s.value||t instanceof MouseEvent&&t.shadowTarget===s.value)}K&&B(u,e=>{e?window.addEventListener(`keydown`,O):window.removeEventListener(`keydown`,O)},{immediate:!0}),Dr(()=>{K&&window.removeEventListener(`keydown`,O)});function O(t){t.key===`Escape`&&_.value&&(c.value?.contains(document.activeElement)||i(`keydown`,t),e.persistent?de():(u.value=!1,c.value?.contains(document.activeElement)&&b.value?.focus()))}function ce(e){e.key===`Escape`&&!_.value||i(`keydown`,e)}let le=gb();hc(()=>e.closeOnBack,()=>{bb(le,t=>{_.value&&u.value?(t(!1),e.persistent?de():u.value=!1):t()})});let ue=P();B(()=>u.value&&(e.absolute||e.contained)&&te.value==null,e=>{if(e){let e=Ju(o.value);e&&e!==document.scrollingElement&&(ue.value=e.scrollTop)}});function de(){e.noClickAnimation||c.value&&Nl(c.value,[{transformOrigin:`center`},{transform:`scale(1.03)`},{transformOrigin:`center`}],{duration:150,easing:Gu})}function k(){i(`afterEnter`)}function fe(){h(),i(`afterLeave`)}return X(()=>H(V,null,[n.activator?.({isActive:u.value,targetRef:w,props:W({ref:x},T.value,e.activatorProps)}),ne.value&&m.value&&U(qn,{disabled:!te.value,to:te.value},{default:()=>[H(`div`,W({class:[`v-overlay`,{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":u.value,"v-overlay--contained":e.contained},d.value,f.value,e.class],style:[y.value,{"--v-overlay-opacity":e.opacity,top:q(ue.value)},e.style],ref:o,onKeydown:ce},re,r),[U(BS,W({color:g,modelValue:u.value&&!!e.scrim,ref:s},ee.value),null),U(fy,{appear:!0,persisted:!0,transition:e.transition,target:S.value,onAfterEnter:k,onAfterLeave:fe},{default:()=>[Fn(H(`div`,W({ref:c,class:[`v-overlay__content`,e.contentClass],style:[D.value,ie.value]},E.value,e.contentProps),[n.default?.({isActive:u})]),[[Io,u.value],[zS,{handler:oe,closeConditional:se,include:()=>[b.value]}]])]})])]})])),{activatorEl:b,scrimEl:s,target:S,animateClick:de,contentEl:c,globalTop:_,localTop:v,updateLocation:ae}}}),US=Symbol(`Forwarded refs`);function WS(e,t){let n=e;for(;n;){let e=Reflect.getOwnPropertyDescriptor(n,t);if(e)return e;n=Object.getPrototypeOf(n)}}function GS(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e[US]=n,new Proxy(e,{get(e,t){if(Reflect.has(e,t))return Reflect.get(e,t);if(!(typeof t==`symbol`||t.startsWith(`$`)||t.startsWith(`__`))){for(let e of n)if(e.value&&Reflect.has(e.value,t)){let n=Reflect.get(e.value,t);return typeof n==`function`?n.bind(e.value):n}}},has(e,t){if(Reflect.has(e,t))return!0;if(typeof t==`symbol`||t.startsWith(`$`)||t.startsWith(`__`))return!1;for(let e of n)if(e.value&&Reflect.has(e.value,t))return!0;return!1},set(e,t,r){if(Reflect.has(e,t))return Reflect.set(e,t,r);if(typeof t==`symbol`||t.startsWith(`$`)||t.startsWith(`__`))return!1;for(let e of n)if(e.value&&Reflect.has(e.value,t))return Reflect.set(e.value,t,r);return!1},getOwnPropertyDescriptor(e,t){let r=Reflect.getOwnPropertyDescriptor(e,t);if(r)return r;if(!(typeof t==`symbol`||t.startsWith(`$`)||t.startsWith(`__`))){for(let e of n){if(!e.value)continue;let n=WS(e.value,t)??(`_`in e.value?WS(e.value._?.setupState,t):void 0);if(n)return n}for(let e of n){let n=e.value&&e.value[US];if(!n)continue;let r=n.slice();for(;r.length;){let e=r.shift(),n=WS(e.value,t);if(n)return n;let i=e.value&&e.value[US];i&&r.push(...i)}}}}})}const KS=J({id:String,submenu:Boolean,disableInitialFocus:Boolean,...Lc(VS({closeDelay:250,closeOnContentClick:!0,locationStrategy:`connected`,location:void 0,openDelay:300,scrim:!1,scrollStrategy:`reposition`,transition:{component:Gv}}),[`absolute`])},`VMenu`),qS=Y()({name:`VMenu`,props:KS(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,r=$u(e,`modelValue`),{scopeId:i}=OS(),{isRtl:a}=hd(),o=fr(),s=L(()=>e.id||`v-menu-${o}`),c=P(),l=z(vS,null),u=F(new Set);pi(vS,{register(){u.value.add(o)},unregister(){u.value.delete(o)},closeParents(t){setTimeout(()=>{!u.value.size&&!e.persistent&&(t==null||c.value?.contentEl&&!ml(t,c.value.contentEl))&&(r.value=!1,l?.closeParents())},40)}}),Dr(()=>{l?.unregister(),document.removeEventListener(`focusin`,d)}),vr(()=>r.value=!1);async function d(e){let t=e.relatedTarget,n=e.target;if(await Cn(),r.value&&t!==n&&c.value?.contentEl&&c.value?.globalTop&&![document,c.value.contentEl].includes(n)&&!c.value.contentEl.contains(n)){let e=sl(c.value.contentEl);e[0]?.focus()}}B(r,t=>{t?(l?.register(),K&&!e.disableInitialFocus&&document.addEventListener(`focusin`,d,{once:!0})):(l?.unregister(),K&&document.removeEventListener(`focusin`,d))},{immediate:!0});function f(e){l?.closeParents(e)}function p(t){if(!e.disabled)if(t.key===`Tab`||t.key===`Enter`&&!e.closeOnContentClick){if(t.key===`Enter`&&(t.target instanceof HTMLTextAreaElement||t.target instanceof HTMLInputElement&&t.target.closest(`form`)))return;t.key===`Enter`&&t.preventDefault();let e=cl(sl(c.value?.contentEl,!1),t.shiftKey?`prev`:`next`,e=>e.tabIndex>=0);e||(r.value=!1,c.value?.activatorEl?.focus())}else e.submenu&&t.key===(a.value?`ArrowRight`:`ArrowLeft`)&&(r.value=!1,c.value?.activatorEl?.focus())}function m(t){if(e.disabled)return;let n=c.value?.contentEl;n&&r.value?t.key===`ArrowDown`?(t.preventDefault(),t.stopImmediatePropagation(),ll(n,`next`)):t.key===`ArrowUp`?(t.preventDefault(),t.stopImmediatePropagation(),ll(n,`prev`)):e.submenu&&(t.key===(a.value?`ArrowRight`:`ArrowLeft`)?r.value=!1:t.key===(a.value?`ArrowLeft`:`ArrowRight`)&&(t.preventDefault(),ll(n,`first`))):(e.submenu?t.key===(a.value?`ArrowLeft`:`ArrowRight`):[`ArrowDown`,`ArrowUp`].includes(t.key))&&(r.value=!0,t.preventDefault(),setTimeout(()=>setTimeout(()=>m(t))))}let h=G(()=>W({"aria-haspopup":`menu`,"aria-expanded":String(r.value),"aria-controls":s.value,onKeydown:m},e.activatorProps));return X(()=>{let t=HS.filterProps(e);return U(HS,W({ref:c,id:s.value,class:[`v-menu`,e.class],style:e.style},t,{modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,absolute:!0,activatorProps:h.value,location:e.location??(e.submenu?`end`:`bottom`),"onClick:outside":f,onKeydown:p},i),{activator:n.activator,default:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return U(ey,{root:`VMenu`},{default:()=>[n.default?.(...t)]})}})}),GS({id:s,ΨopenChildren:u},c)}});function JS(e){let{rootEl:t,isSticky:n,layoutItemStyles:r}=e,i=F(!1),a=F(0),o=G(()=>{let e=typeof i.value==`boolean`?`top`:i.value;return[n.value?{top:`auto`,bottom:`auto`,height:void 0}:void 0,i.value?{[e]:q(a.value)}:{top:r.value.top}]});wr(()=>{B(n,e=>{e?window.addEventListener(`scroll`,c,{passive:!0}):window.removeEventListener(`scroll`,c)},{immediate:!0})}),Dr(()=>{window.removeEventListener(`scroll`,c)});let s=0;function c(){let e=s>window.scrollY?`up`:`down`,n=t.value.getBoundingClientRect(),o=parseFloat(r.value.top??0),c=window.scrollY-Math.max(0,a.value-o),l=n.height+Math.max(a.value,o)-window.scrollY-window.innerHeight,u=parseFloat(getComputedStyle(t.value).getPropertyValue(`--v-body-scroll-y`))||0;n.height<window.innerHeight-o?(i.value=`top`,a.value=o):e===`up`&&i.value===`bottom`||e===`down`&&i.value===`top`?(a.value=window.scrollY+n.top-u,i.value=!0):e===`down`&&l<=0?(a.value=0,i.value=`bottom`):e===`up`&&c<=0&&(u?i.value!==`top`&&(a.value=-c+u+o,i.value=`top`):(a.value=n.top+c,i.value=`top`)),s=window.scrollY}return{isStuck:i,stickyStyles:o}}const YS=100,XS=20;function ZS(e){let t=1.41421356237;return(e<0?-1:1)*Math.sqrt(Math.abs(e))*t}function QS(e){if(e.length<2)return 0;if(e.length===2)return e[1].t===e[0].t?0:(e[1].d-e[0].d)/(e[1].t-e[0].t);let t=0;for(let n=e.length-1;n>0;n--){if(e[n].t===e[n-1].t)continue;let r=ZS(t),i=(e[n].d-e[n-1].d)/(e[n].t-e[n-1].t);t+=(i-r)*Math.abs(i),n===e.length-1&&(t*=.5)}return ZS(t)*1e3}function $S(){let e={};function t(t){Array.from(t.changedTouches).forEach(n=>{let r=e[n.identifier]??(e[n.identifier]=new el(XS));r.push([t.timeStamp,n])})}function n(t){Array.from(t.changedTouches).forEach(t=>{delete e[t.identifier]})}function r(t){let n=e[t]?.values().reverse();if(!n)throw Error(`No samples for touch id ${t}`);let r=n[0],i=[],a=[];for(let e of n){if(r[0]-e[0]>YS)break;i.push({t:e[0],d:e[1].clientX}),a.push({t:e[0],d:e[1].clientY})}return{x:QS(i),y:QS(a),get direction(){let{x:e,y:t}=this,[n,r]=[Math.abs(e),Math.abs(t)];return n>r&&e>=0?`right`:n>r&&e<=0?`left`:r>n&&t>=0?`down`:r>n&&t<=0?`up`:eC()}}}return{addMovement:t,endTouch:n,getVelocity:r}}function eC(){throw Error()}function tC(e){let{el:t,isActive:n,isTemporary:r,width:i,touchless:a,position:o}=e;wr(()=>{window.addEventListener(`touchstart`,v,{passive:!0}),window.addEventListener(`touchmove`,y,{passive:!1}),window.addEventListener(`touchend`,b,{passive:!0})}),Dr(()=>{window.removeEventListener(`touchstart`,v),window.removeEventListener(`touchmove`,y),window.removeEventListener(`touchend`,b)});let s=G(()=>[`left`,`right`].includes(o.value)),{addMovement:c,endTouch:l,getVelocity:u}=$S(),d=!1,f=F(!1),p=F(0),m=F(0),h;function g(e,t){return(o.value===`left`?e:o.value===`right`?document.documentElement.clientWidth-e:o.value===`top`?e:o.value===`bottom`?document.documentElement.clientHeight-e:nC())-(t?i.value:0)}function _(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,n=o.value===`left`?(e-m.value)/i.value:o.value===`right`?(document.documentElement.clientWidth-e-m.value)/i.value:o.value===`top`?(e-m.value)/i.value:o.value===`bottom`?(document.documentElement.clientHeight-e-m.value)/i.value:nC();return t?Wc(n):n}function v(e){if(a.value)return;let t=e.changedTouches[0].clientX,u=e.changedTouches[0].clientY,f=25,v=o.value===`left`?t<f:o.value===`right`?t>document.documentElement.clientWidth-f:o.value===`top`?u<f:o.value===`bottom`?u>document.documentElement.clientHeight-f:nC(),y=n.value&&(o.value===`left`?t<i.value:o.value===`right`?t>document.documentElement.clientWidth-i.value:o.value===`top`?u<i.value:o.value===`bottom`?u>document.documentElement.clientHeight-i.value:nC());(v||y||n.value&&r.value)&&(h=[t,u],m.value=g(s.value?t:u,n.value),p.value=_(s.value?t:u),d=m.value>-20&&m.value<80,l(e),c(e))}function y(e){let t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(d){if(!e.cancelable){d=!1;return}let r=Math.abs(t-h[0]),i=Math.abs(n-h[1]),a=s.value?r>i&&r>3:i>r&&i>3;a?(f.value=!0,d=!1):(s.value?i:r)>3&&(d=!1)}if(!f.value)return;e.preventDefault(),c(e);let r=_(s.value?t:n,!1);p.value=Math.max(0,Math.min(1,r)),r>1?m.value=g(s.value?t:n,!0):r<0&&(m.value=g(s.value?t:n,!1))}function b(e){if(d=!1,!f.value)return;c(e),f.value=!1;let t=u(e.changedTouches[0].identifier),r=Math.abs(t.x),i=Math.abs(t.y),a=s.value?r>i&&r>400:i>r&&i>3;a?n.value=t.direction===({left:`right`,right:`left`,top:`down`,bottom:`up`}[o.value]||nC()):n.value=p.value>.5}let x=G(()=>f.value?{transform:o.value===`left`?`translateX(calc(-100% + ${p.value*i.value}px))`:o.value===`right`?`translateX(calc(100% - ${p.value*i.value}px))`:o.value===`top`?`translateY(calc(-100% + ${p.value*i.value}px))`:o.value===`bottom`?`translateY(calc(100% - ${p.value*i.value}px))`:nC(),transition:`none`}:void 0);return hc(f,()=>{let e=t.value?.style.transform??null,n=t.value?.style.transition??null;Gi(()=>{t.value?.style.setProperty(`transform`,x.value?.transform||`none`),t.value?.style.setProperty(`transition`,x.value?.transition||null)}),De(()=>{t.value?.style.setProperty(`transform`,e),t.value?.style.setProperty(`transition`,n)})}),{isDragging:f,dragProgress:p,dragStyles:x}}function nC(){throw Error()}const rC=[`start`,`end`,`left`,`right`,`top`,`bottom`],iC=J({color:String,disableResizeWatcher:Boolean,disableRouteWatcher:Boolean,expandOnHover:Boolean,floating:Boolean,modelValue:{type:Boolean,default:null},permanent:Boolean,rail:{type:Boolean,default:null},railWidth:{type:[Number,String],default:56},scrim:{type:[Boolean,String],default:!0},image:String,temporary:Boolean,persistent:Boolean,touchless:Boolean,width:{type:[Number,String],default:256},location:{type:String,default:`start`,validator:e=>rC.includes(e)},sticky:Boolean,...yy(),...ju(),...yS(),...Cf({mobile:null}),...xy(),...dp(),...ly(),...Iv({tag:`nav`}),...Kf()},`VNavigationDrawer`),aC=Y()({name:`VNavigationDrawer`,props:iC(),emits:{"update:modelValue":e=>!0,"update:rail":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:i}=t,{isRtl:a}=hd(),{themeClasses:o}=ip(e),{borderClasses:s}=by(e),{backgroundColorClasses:c,backgroundColorStyles:l}=cy(()=>e.color),{elevationClasses:u}=Sy(e),{displayClasses:d,mobile:f}=wf(e),{roundedClasses:p}=uy(e),m=gb(),h=$u(e,`modelValue`,null,e=>!!e),{ssrBootStyles:g}=Oy(),{scopeId:_}=OS(),v=P(),y=F(!1),{runOpenDelay:b,runCloseDelay:x}=bS(e,e=>{y.value=e}),S=G(()=>e.rail&&e.expandOnHover&&y.value?Number(e.width):Number(e.rail?e.railWidth:e.width)),C=G(()=>Cl(e.location,a.value)),w=L(()=>e.persistent),T=G(()=>!e.permanent&&(f.value||e.temporary)),E=G(()=>e.sticky&&!T.value&&C.value!==`bottom`);hc(()=>e.expandOnHover&&e.rail!=null,()=>{B(y,e=>r(`update:rail`,!e))}),hc(()=>!e.disableResizeWatcher,()=>{B(T,t=>!e.permanent&&Cn(()=>h.value=!t))}),hc(()=>!e.disableRouteWatcher&&!!m,()=>{B(m.currentRoute,()=>T.value&&(h.value=!1))}),B(()=>e.permanent,e=>{e&&(h.value=!0)}),e.modelValue==null&&!T.value&&(h.value=e.permanent||!f.value);let{isDragging:ee,dragProgress:te}=tC({el:v,isActive:h,isTemporary:T,width:S,touchless:L(()=>e.touchless),position:C}),D=G(()=>{let t=T.value?0:e.rail&&e.expandOnHover?Number(e.railWidth):S.value;return ee.value?t*te.value:t}),{layoutItemStyles:ne,layoutItemScrimStyles:re}=pp({id:e.name,order:G(()=>parseInt(e.order,10)),position:C,layoutSize:D,elementSize:S,active:zt(h),disableTransitions:L(()=>ee.value),absolute:G(()=>e.absolute||E.value&&typeof ie.value!=`string`)}),{isStuck:ie,stickyStyles:ae}=JS({rootEl:v,isSticky:E,layoutItemStyles:ne}),oe=cy(()=>typeof e.scrim==`string`?e.scrim:null),se=G(()=>({...ee.value?{opacity:te.value*.2,transition:`none`}:void 0,...re.value}));return Ru({VList:{bgColor:`transparent`}}),X(()=>{let t=i.image||e.image;return H(V,null,[U(e.tag,W({ref:v,onMouseenter:b,onMouseleave:x,class:[`v-navigation-drawer`,`v-navigation-drawer--${C.value}`,{"v-navigation-drawer--expand-on-hover":e.expandOnHover,"v-navigation-drawer--floating":e.floating,"v-navigation-drawer--is-hovering":y.value,"v-navigation-drawer--rail":e.rail,"v-navigation-drawer--temporary":T.value,"v-navigation-drawer--persistent":w.value,"v-navigation-drawer--active":h.value,"v-navigation-drawer--sticky":E.value},o.value,c.value,s.value,d.value,u.value,p.value,e.class],style:[l.value,ne.value,g.value,ae.value,e.style]},_,n),{default:()=>[t&&H(`div`,{key:`image`,class:`v-navigation-drawer__img`},[i.image?U(ey,{key:`image-defaults`,disabled:!e.image,defaults:{VImg:{alt:``,cover:!0,height:`inherit`,src:e.image}}},i.image):U(vy,{key:`image-img`,alt:``,cover:!0,height:`inherit`,src:e.image},null)]),i.prepend&&H(`div`,{class:`v-navigation-drawer__prepend`},[i.prepend?.()]),H(`div`,{class:`v-navigation-drawer__content`},[i.default?.()]),i.append&&H(`div`,{class:`v-navigation-drawer__append`},[i.append?.()])]}),U(vo,{name:`fade-transition`},{default:()=>[T.value&&(ee.value||h.value)&&!!e.scrim&&H(`div`,W({class:[`v-navigation-drawer__scrim`,oe.backgroundColorClasses.value],style:[se.value,oe.backgroundColorStyles.value],onClick:()=>{w.value||(h.value=!1)}},_),null)]})])}),{isStuck:ie}}}),oC={key:1};var sC=dr({__name:`AppLayout`,setup(e){let t=ap(),n=Ng(),r=Pv(),i=P(!0),a=P(!1),o=G(()=>`RPS Management System`),s=G(()=>t.global.name.value===`dark`),c=G(()=>{let e=[{title:`Dashboard`,icon:`mdi-view-dashboard`,to:`/dashboard`,value:`dashboard`,roles:[]},{title:`Users`,icon:`mdi-account-group`,to:`/users`,value:`users`,roles:[`admin`]},{title:`Faculties`,icon:`mdi-domain`,to:`/faculties`,value:`faculties`,roles:[`admin`,`dekan`,`wakil_dekan`]},{title:`Study Programs`,icon:`mdi-school`,to:`/study-programs`,value:`study-programs`,roles:[`admin`,`dekan`,`wakil_dekan`,`kepala_prodi`]},{title:`Courses`,icon:`mdi-book-open-page-variant`,to:`/courses`,value:`courses`,roles:[]},{title:`CPL Management`,icon:`mdi-target`,to:`/cpl`,value:`cpl`,roles:[`admin`,`dekan`,`wakil_dekan`,`kepala_prodi`]},{title:`CPMK Management`,icon:`mdi-bullseye-arrow`,to:`/cpmk`,value:`cpmk`,roles:[]},{title:`Assessments`,icon:`mdi-clipboard-check`,to:`/assessments`,value:`assessments`,roles:[]},{title:`Reports`,icon:`mdi-chart-line`,to:`/reports`,value:`reports`,roles:[]}];return e.filter(e=>e.roles.length===0?!0:e.roles.some(e=>n.userRoles.includes(e)))}),l=()=>{t.global.name.value=t.global.current.value.dark?`light`:`dark`},u=async()=>{await n.logout(),r.push(`/login`)};return wr(()=>{let e=localStorage.getItem(`theme`);if(e)t.global.name.value=e;else{let e=window.matchMedia(`(prefers-color-scheme: dark)`).matches;t.global.name.value=e?`dark`:`light`}}),t.global.name.value&&localStorage.setItem(`theme`,t.global.name.value),(e,t)=>{let r=Pr(`router-view`),d=Pr(`v-list-item-prepend`);return fa(),_a(`div`,null,[I(n).isAuthenticated?(fa(),_a(`div`,oC,[U(Ay,{elevation:2,color:`primary`,dark:``,app:``},{default:R(()=>[U(Jb,{onClick:t[0]||=e=>i.value=!i.value}),U(Rv,{class:`text-h6 font-weight-bold`},{default:R(()=>[Ea(be(o.value),1)]),_:1}),U(tx),U(Kb,{icon:``},{default:R(()=>[U(eb,null,{default:R(()=>t[6]||=[Ea(`mdi-bell`,-1)]),_:1,__:[6]})]),_:1}),U(Kb,{icon:``,onClick:l},{default:R(()=>[U(eb,null,{default:R(()=>[Ea(be(s.value?`mdi-weather-sunny`:`mdi-weather-night`),1)]),_:1})]),_:1}),U(qS,{"offset-y":``},{activator:R(({props:e})=>[U(Kb,W({icon:``},e),{default:R(()=>[U(Xb,{size:`32`},{default:R(()=>[I(n).user?.avatar?(fa(),va(vy,{key:0,src:I(n).user.avatar,alt:I(n).user?.full_name},null,8,[`src`,`alt`])):(fa(),va(eb,{key:1},{default:R(()=>t[7]||=[Ea(`mdi-account`,-1)]),_:1,__:[7]}))]),_:1})]),_:2},1040)]),default:R(()=>[U(Kx,null,{default:R(()=>[U(jx,null,{default:R(()=>[U(kx,null,{default:R(()=>[Ea(be(I(n).user?.full_name),1)]),_:1}),U(Ox,null,{default:R(()=>[Ea(be(I(n).user?.email),1)]),_:1})]),_:1}),U(Qb),U(jx,{onClick:t[1]||=t=>e.$router.push(`/profile`)},{default:R(()=>[U(d,null,{default:R(()=>[U(eb,null,{default:R(()=>t[8]||=[Ea(`mdi-account-circle`,-1)]),_:1,__:[8]})]),_:1}),U(kx,null,{default:R(()=>t[9]||=[Ea(`Profile`,-1)]),_:1,__:[9]})]),_:1}),U(jx,{onClick:u},{default:R(()=>[U(d,null,{default:R(()=>[U(eb,null,{default:R(()=>t[10]||=[Ea(`mdi-logout`,-1)]),_:1,__:[10]})]),_:1}),U(kx,null,{default:R(()=>t[11]||=[Ea(`Logout`,-1)]),_:1,__:[11]})]),_:1})]),_:1})]),_:1})]),_:1}),U(aC,{modelValue:i.value,"onUpdate:modelValue":t[3]||=e=>i.value=e,app:``,rail:a.value,onClick:t[4]||=e=>a.value=!1},{default:R(()=>[U(Kx,null,{default:R(()=>[U(jx,{"prepend-avatar":`/logo.png`,title:I(n).user?.full_name,subtitle:I(n).user?.roles.join(`, `)},{append:R(()=>[U(Kb,{icon:`mdi-chevron-left`,variant:`text`,onClick:t[2]||=ks(e=>a.value=!a.value,[`stop`])})]),_:1},8,[`title`,`subtitle`])]),_:1}),U(Qb),U(Kx,{density:`compact`,nav:``},{default:R(()=>[(fa(!0),_a(V,null,zr(c.value,e=>(fa(),va(jx,{key:e.title,to:e.to,"prepend-icon":e.icon,title:e.title,value:e.value,color:`primary`},null,8,[`to`,`prepend-icon`,`title`,`value`]))),128))]),_:1})]),_:1},8,[`modelValue`,`rail`]),U(Jx,null,{default:R(()=>[U(ex,{fluid:``,class:`pa-4`},{default:R(()=>[U(r)]),_:1})]),_:1}),U(HS,{modelValue:I(n).loading,"onUpdate:modelValue":t[5]||=e=>I(n).loading=e,class:`align-center justify-center`},{default:R(()=>[U(rb,{color:`primary`,indeterminate:``,size:`64`})]),_:1},8,[`modelValue`])])):(fa(),va(r,{key:0}))])}}}),cC=(e,t)=>{let n=e.__vccOpts||e;for(let[e,r]of t)n[e]=r;return n},lC=cC(sC,[[`__scopeId`,`data-v-07185656`]]);const uC=J({...ju(),...up({fullHeight:!0}),...Kf()},`VApp`),dC=Y()({name:`VApp`,props:uC(),setup(e,t){let{slots:n}=t,r=ip(e),{layoutClasses:i,getLayoutItem:a,items:o,layoutRef:s}=hp(e),{rtlClasses:c}=hd();return X(()=>H(`div`,{ref:s,class:A([`v-application`,r.themeClasses.value,i.value,c.value,e.class]),style:k([e.style])},[H(`div`,{class:`v-application__wrap`},[n.default?.()])])),{getLayoutItem:a,items:o,theme:r}}});var fC=dr({__name:`App`,setup(e){let t=Ng();return wr(async()=>{await t.initialize()}),(e,t)=>(fa(),va(dC,null,{default:R(()=>[U(lC)]),_:1}))}}),pC=fC;const mC=`modulepreload`,hC=function(e){return`/`+e},gC={},_C=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){let e=document.getElementsByTagName(`link`),i=document.querySelector(`meta[property=csp-nonce]`),a=i?.nonce||i?.getAttribute(`nonce`);function o(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:`fulfilled`,value:e}),e=>({status:`rejected`,reason:e}))))}r=o(t.map(t=>{if(t=hC(t,n),t in gC)return;gC[t]=!0;let r=t.endsWith(`.css`),i=r?`[rel="stylesheet"]`:``,o=!!n;if(o)for(let n=e.length-1;n>=0;n--){let i=e[n];if(i.href===t&&(!r||i.rel===`stylesheet`))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;let s=document.createElement(`link`);if(s.rel=r?`stylesheet`:mC,r||(s.as=`script`),s.crossOrigin=``,s.href=t,a&&s.setAttribute(`nonce`,a),document.head.appendChild(s),r)return new Promise((e,n)=>{s.addEventListener(`load`,e),s.addEventListener(`error`,()=>n(Error(`Unable to preload CSS for ${t}`)))})}))}function i(e){let t=new Event(`vite:preloadError`,{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then(t=>{for(let e of t||[]){if(e.status!==`rejected`)continue;i(e.reason)}return e().catch(i)})},vC=Mv({history:I_(`/`),routes:[{path:`/`,redirect:`/dashboard`},{path:`/login`,name:`Login`,component:()=>_C(()=>import(`./LoginView-BJxKou1C.js`),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])),meta:{requiresGuest:!0}},{path:`/dashboard`,name:`Dashboard`,component:()=>_C(()=>import(`./DashboardView-D5UIV0lu.js`),__vite__mapDeps([10,1,2,11,12,8,13,14,15,16])),meta:{requiresAuth:!0}},{path:`/users`,name:`Users`,component:()=>_C(()=>import(`./UsersView-C-S8yKJ5.js`),__vite__mapDeps([17,1,2,3,4,5,6,11,12,18,8,19,20,21,7,22,23,24,25,15])),meta:{requiresAuth:!0,roles:[`admin`]}},{path:`/faculties`,name:`Faculties`,component:()=>_C(()=>import(`./FacultiesView-BAvwq3Li.js`),__vite__mapDeps([26,1,2])),meta:{requiresAuth:!0,roles:[`admin`,`dekan`,`wakil_dekan`]}},{path:`/study-programs`,name:`StudyPrograms`,component:()=>_C(()=>import(`./StudyProgramsView-C36Uyo4w.js`),__vite__mapDeps([27,1,2])),meta:{requiresAuth:!0,roles:[`admin`,`dekan`,`wakil_dekan`,`kepala_prodi`]}},{path:`/courses`,name:`Courses`,component:()=>_C(()=>import(`./CoursesView-C7heoylY.js`),__vite__mapDeps([28,1,2,3,4,5,6,11,12,18,8,19,20,21,7,22,23,24,25,29,30,13,14,31,15,32])),meta:{requiresAuth:!0}},{path:`/courses/:id`,name:`CourseDetail`,component:()=>_C(()=>import(`./CourseDetailView-BuBillj2.js`),__vite__mapDeps([33,1,2,3,4,5,6,11,12,7,8,19,20,24,25,34,35,29,30,13,14,21,22,31,15,32,36])),meta:{requiresAuth:!0}},{path:`/cpl`,name:`CPL`,component:()=>_C(()=>import(`./CPLView-45YpSa3z.js`),__vite__mapDeps([37,1,2,3,4,5,6,11,12,18,8,19,20,21,7,22,23,24,25,29,30,15])),meta:{requiresAuth:!0,roles:[`admin`,`dekan`,`wakil_dekan`,`kepala_prodi`]}},{path:`/cpmk`,name:`CPMK`,component:()=>_C(()=>import(`./CPMKView-D8k2q2uK.js`),__vite__mapDeps([38,1,2,3,4,5,6,11,12,18,8,19,20,21,7,22,23,24,25,29,30,15,39])),meta:{requiresAuth:!0}},{path:`/assessments`,name:`Assessments`,component:()=>_C(()=>import(`./AssessmentsView-CA6HoVnu.js`),__vite__mapDeps([40,1,2,3,4,5,6,11,12,18,8,19,20,21,7,22,23,24,25,34,35,29,30,15])),meta:{requiresAuth:!0}},{path:`/reports`,name:`Reports`,component:()=>_C(()=>import(`./ReportsView-BdVqN3KQ.js`),__vite__mapDeps([41,1,2,3,4,5,6,11,12,8,19,20,24,25,34,35,15])),meta:{requiresAuth:!0}},{path:`/profile`,name:`Profile`,component:()=>_C(()=>import(`./ProfileView-oWoJGH0s.js`),__vite__mapDeps([42,1,2,11,12,4,5,7,8,24,25,43])),meta:{requiresAuth:!0}},{path:`/:pathMatch(.*)*`,name:`NotFound`,component:()=>_C(()=>import(`./NotFoundView-BT7fsKGJ.js`),__vite__mapDeps([44,8,45]))}]});vC.beforeEach((e,t,n)=>{let r=Ng();if(e.meta.requiresAuth&&!r.isAuthenticated){n(`/login`);return}if(e.meta.requiresGuest&&r.isAuthenticated){n(`/dashboard`);return}if(e.meta.roles&&r.isAuthenticated){let t=r.user?.roles||[],i=Array.isArray(e.meta.roles)?e.meta.roles:[],a=i.some(e=>t.includes(e));if(!a){n(`/dashboard`);return}}n()});var yC=vC;const bC=gp({theme:{defaultTheme:`light`,themes:{light:{colors:{primary:`#1976D2`,secondary:`#424242`,accent:`#82B1FF`,error:`#FF5252`,info:`#2196F3`,success:`#4CAF50`,warning:`#FFC107`}},dark:{colors:{primary:`#2196F3`,secondary:`#424242`,accent:`#FF4081`,error:`#FF5252`,info:`#2196F3`,success:`#4CAF50`,warning:`#FB8C00`}}}},icons:{defaultSet:`mdi`,aliases:Pf,sets:{mdi:Ff}}}),xC=Ns(pC);xC.use(ec()),xC.use(yC),xC.use(bC),xC.mount(`#app`);export{gy as $,mb as A,db as B,lb as C,ub as D,cb as E,ab as F,ob as G,rb as H,eb as I,Zy as J,Qy as K,Yy as L,Vy as M,By as N,Uy as O,Hy as P,Fy as Q,Iy as R,Ly as S,My as T,Ny as U,Oy as V,xy as W,Sy as X,yy as Y,by as Z,vy as _,kc as a$,fy as a1,dy as a2,ly as a3,uy as a4,cy as a5,sy as a6,ty as a7,ny as a8,ey as a9,Uu as aA,Hu as aB,Y as aC,Ru as aD,Mu as aE,Nu as aF,ju as aG,J as aH,nu as aI,tu as aJ,Nl as aK,Ml as aL,il as aM,ol as aN,vl as aO,gl as aP,Wc as aQ,q as aR,Oc as aS,Uc as aT,Tc as aU,fl as aV,Vc as aW,sl as aX,Ec as aY,Dc as aZ,ul as a_,Qv as aa,Jv as ab,Yv as ac,Xv as ad,Gv as ae,Iv as af,Fv as ag,Pv as ah,Ng as ai,Mg as aj,sp as ak,fp as al,op as am,Kf as an,ip as ao,If as ap,Mf as aq,hf as ar,Cf as as,wf as at,fd as au,hd as av,$u as aw,X as ax,Ju as ay,Gu as az,cC as b,zc as b0,Mc as b1,Nc as b2,dl as b3,Lc as b4,Fc as b5,jc as b6,Hc as b7,K as b8,hc as b9,Vr as bA,Pr as bB,fr as bC,B as bD,Gi as bE,R as bF,Fn as bG,Gt as bH,De as bI,Lt as bJ,P as bK,F as bL,L as bM,$t as bN,I as bO,ie as bP,A as bQ,k as bR,be as bS,Es as ba,Io as bb,ks as bc,V as bd,Ta as be,G as bf,H as bg,va as bh,Da as bi,_a as bj,Br as bk,Ea as bl,U as bm,dr as bn,eo as bo,z as bp,W as bq,Cn as br,Cr as bs,Dr as bt,Tr as bu,wr as bv,Or as bw,fa as bx,pi as by,zr as bz,qS as c,GS as d,HS as e,VS as f,OS as g,ES as h,DS as i,Kx as j,Ix as k,Bx as l,Nx as m,jx as n,kx as o,Ox as p,tx as q,ex as r,Qb as s,Xb as t,Kb as u,Gb as v,Wb as w,vb as x,_b as y,pb as z};