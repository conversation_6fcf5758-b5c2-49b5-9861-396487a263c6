import{M as e,O as t,P as n,T as r,U as i,V as a,a$ as o,a1 as s,a5 as c,a6 as l,aC as u,aD as d,aG as f,aH as p,aK as m,aR as h,af as g,an as _,ao as v,au as y,av as b,aw as x,ax as S,az as C,b2 as w,b4 as T,bD as ee,bG as E,bK as D,bL as O,bM as k,bQ as A,bR as j,bb as te,bd as M,bf as N,bg as P,bm as F,bp as I,bq as L,br as ne,by as re,d as R,g as ie,h as ae,i as oe,u as z,v as se}from"./index-BSnscBhv.js";import{c as B,d as ce}from"./VChip-CBN0Kf2u.js";const V=Symbol.for(`vuetify:v-tabs`),le=p({fixed:Boolean,sliderColor:String,hideSlider:Boolean,direction:{type:String,default:`horizontal`},...T(se({selectedClass:`v-tab--selected`,variant:`text`}),[`active`,`block`,`flat`,`location`,`position`,`symbol`])},`VTab`),H=u()({name:`VTab`,props:le(),setup(e,t){let{slots:n,attrs:r}=t,{textColorClasses:i,textColorStyles:a}=l(()=>e.sliderColor),o=D(),s=D(),c=N(()=>e.direction===`horizontal`),u=N(()=>o.value?.group?.isSelected.value??!1);function d(e){let{value:t}=e;if(t){let e=o.value?.$el.parentElement?.querySelector(`.v-tab--selected .v-tab__slider`),t=s.value;if(!e||!t)return;let n=getComputedStyle(e).color,r=e.getBoundingClientRect(),i=t.getBoundingClientRect(),a=c.value?`x`:`y`,l=c.value?`X`:`Y`,u=c.value?`right`:`bottom`,d=c.value?`width`:`height`,f=r[a],p=i[a],h=f>p?r[u]-i[u]:r[a]-i[a],g=Math.sign(h)>0?c.value?`right`:`bottom`:Math.sign(h)<0?c.value?`left`:`top`:`center`,_=Math.abs(h)+(Math.sign(h)<0?r[d]:i[d]),v=_/Math.max(r[d],i[d])||0,y=r[d]/i[d]||0,b=1.5;m(t,{backgroundColor:[n,`currentcolor`],transform:[`translate${l}(${h}px) scale${l}(${y})`,`translate${l}(${h/b}px) scale${l}(${(v-1)/b+1})`,`none`],transformOrigin:[,,,].fill(g)},{duration:225,easing:C})}}return S(()=>{let t=z.filterProps(e);return F(z,L({symbol:V,ref:o,class:[`v-tab`,e.class],style:e.style,tabindex:u.value?0:-1,role:`tab`,"aria-selected":String(u.value),active:!1},t,r,{block:e.fixed,maxWidth:e.fixed?300:void 0,"onGroup:selected":d}),{...n,default:()=>P(M,null,[n.default?.()??e.text,!e.hideSlider&&P(`div`,{ref:s,class:A([`v-tab__slider`,i.value]),style:j(a.value)},null)])})}),R({},o)}}),U=e=>{let{touchstartX:t,touchendX:n,touchstartY:r,touchendY:i}=e,a=.5,o=16;e.offsetX=n-t,e.offsetY=i-r,Math.abs(e.offsetY)<a*Math.abs(e.offsetX)&&(e.left&&n<t-o&&e.left(e),e.right&&n>t+o&&e.right(e)),Math.abs(e.offsetX)<a*Math.abs(e.offsetY)&&(e.up&&i<r-o&&e.up(e),e.down&&i>r+o&&e.down(e))};function ue(e,t){let n=e.changedTouches[0];t.touchstartX=n.clientX,t.touchstartY=n.clientY,t.start?.({originalEvent:e,...t})}function de(e,t){let n=e.changedTouches[0];t.touchendX=n.clientX,t.touchendY=n.clientY,t.end?.({originalEvent:e,...t}),U(t)}function fe(e,t){let n=e.changedTouches[0];t.touchmoveX=n.clientX,t.touchmoveY=n.clientY,t.move?.({originalEvent:e,...t})}function pe(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t={touchstartX:0,touchstartY:0,touchendX:0,touchendY:0,touchmoveX:0,touchmoveY:0,offsetX:0,offsetY:0,left:e.left,right:e.right,up:e.up,down:e.down,start:e.start,move:e.move,end:e.end};return{touchstart:e=>ue(e,t),touchend:e=>de(e,t),touchmove:e=>fe(e,t)}}function me(e,t){let n=t.value,r=n?.parent?e.parentElement:e,i=n?.options??{passive:!0},a=t.instance?.$.uid;if(!r||!a)return;let o=pe(t.value);r._touchHandlers=r._touchHandlers??Object.create(null),r._touchHandlers[a]=o,w(o).forEach(e=>{r.addEventListener(e,o[e],i)})}function he(e,t){let n=t.value?.parent?e.parentElement:e,r=t.instance?.$.uid;if(!n?._touchHandlers||!r)return;let i=n._touchHandlers[r];w(i).forEach(e=>{n.removeEventListener(e,i[e])}),delete n._touchHandlers[r]}const ge={mounted:me,unmounted:he};var W=ge;const G=Symbol.for(`vuetify:v-window`),K=Symbol.for(`vuetify:v-window-group`),q=p({continuous:Boolean,nextIcon:{type:[Boolean,String,Function,Object],default:`$next`},prevIcon:{type:[Boolean,String,Function,Object],default:`$prev`},reverse:Boolean,showArrows:{type:[Boolean,String],validator:e=>typeof e==`boolean`||e===`hover`},verticalArrows:[Boolean,String],touch:{type:[Object,Boolean],default:void 0},direction:{type:String,default:`horizontal`},modelValue:null,disabled:Boolean,selectedClass:{type:String,default:`v-window-item--active`},mandatory:{type:[Boolean,String],default:`force`},...f(),...g(),..._()},`VWindow`),J=u()({name:`VWindow`,directives:{vTouch:W},props:q(),emits:{"update:modelValue":e=>!0},setup(e,n){let{slots:r}=n,{themeClasses:i}=v(e),{isRtl:a}=b(),{t:o}=y(),s=t(e,K),c=D(),l=N(()=>a.value?!e.reverse:e.reverse),u=O(!1),d=N(()=>{let t=e.direction===`vertical`?`y`:`x`,n=l.value?!u.value:u.value,r=n?`-reverse`:``;return`v-window-${t}${r}-transition`}),f=O(0),p=D(void 0),m=N(()=>s.items.value.findIndex(e=>s.selected.value.includes(e.id)));ee(m,(e,t)=>{let n=s.items.value.length,r=n-1;n<=2?u.value=e<t:e===r&&t===0?u.value=!0:e===0&&t===r?u.value=!1:u.value=e<t}),re(G,{transition:d,isReversed:u,transitionCount:f,transitionHeight:p,rootRef:c});let h=k(()=>e.continuous||m.value!==0),g=k(()=>e.continuous||m.value!==s.items.value.length-1);function _(){h.value&&s.prev()}function x(){g.value&&s.next()}let C=N(()=>{let t=[],n={icon:a.value?e.nextIcon:e.prevIcon,class:`v-window__${l.value?`right`:`left`}`,onClick:s.prev,"aria-label":o(`$vuetify.carousel.prev`)};t.push(h.value?r.prev?r.prev({props:n}):F(z,n,null):P(`div`,null,null));let i={icon:a.value?e.prevIcon:e.nextIcon,class:`v-window__${l.value?`left`:`right`}`,onClick:s.next,"aria-label":o(`$vuetify.carousel.next`)};return t.push(g.value?r.next?r.next({props:i}):F(z,i,null):P(`div`,null,null)),t}),w=N(()=>{if(e.touch===!1)return e.touch;let t={left:()=>{l.value?_():x()},right:()=>{l.value?x():_()},start:e=>{let{originalEvent:t}=e;t.stopPropagation()}};return{...t,...e.touch===!0?{}:e.touch}});return S(()=>E(F(e.tag,{ref:c,class:A([`v-window`,{"v-window--show-arrows-on-hover":e.showArrows===`hover`,"v-window--vertical-arrows":!!e.verticalArrows},i.value,e.class]),style:j(e.style)},{default:()=>[P(`div`,{class:`v-window__container`,style:{height:p.value}},[r.default?.({group:s}),e.showArrows!==!1&&P(`div`,{class:A([`v-window__controls`,{"v-window__controls--left":e.verticalArrows===`left`||e.verticalArrows===!0},{"v-window__controls--right":e.verticalArrows===`right`}])},[C.value])]),r.additional?.({group:s})]}),[[W,w.value]])),{group:s}}}),_e=p({...T(q(),[`continuous`,`nextIcon`,`prevIcon`,`showArrows`,`touch`,`mandatory`])},`VTabsWindow`),Y=u()({name:`VTabsWindow`,props:_e(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,r=I(V,null),i=x(e,`modelValue`),a=N({get(){return i.value!=null||!r?i.value:r.items.value.find(e=>r.selected.value.includes(e.id))?.value},set(e){i.value=e}});return S(()=>{let t=J.filterProps(e);return F(J,L({_as:`VTabsWindow`},t,{modelValue:a.value,"onUpdate:modelValue":e=>a.value=e,class:[`v-tabs-window`,e.class],style:e.style,mandatory:!1,touch:!1}),n)}),{}}}),X=p({reverseTransition:{type:[Boolean,String],default:void 0},transition:{type:[Boolean,String],default:void 0},...f(),...e(),...ae()},`VWindowItem`),Z=u()({name:`VWindowItem`,directives:{vTouch:W},props:X(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:r}=t,i=I(G),o=n(e,K),{isBooted:c}=a();if(!i||!o)throw Error(`[Vuetify] VWindowItem must be used inside VWindow`);let l=O(!1),u=N(()=>c.value&&(i.isReversed.value?e.reverseTransition!==!1:e.transition!==!1));function d(){!l.value||!i||(l.value=!1,i.transitionCount.value>0&&(--i.transitionCount.value,i.transitionCount.value===0&&(i.transitionHeight.value=void 0)))}function f(){l.value||!i||(l.value=!0,i.transitionCount.value===0&&(i.transitionHeight.value=h(i.rootRef.value?.clientHeight)),i.transitionCount.value+=1)}function p(){d()}function m(e){l.value&&ne(()=>{!u.value||!l.value||!i||(i.transitionHeight.value=h(e.clientHeight))})}let g=N(()=>{let t=i.isReversed.value?e.reverseTransition:e.transition;return u.value?{name:typeof t==`string`?t:i.transition.value,onBeforeEnter:f,onAfterEnter:d,onEnterCancelled:p,onBeforeLeave:f,onAfterLeave:d,onLeaveCancelled:p,onEnter:m}:!1}),{hasContent:_}=oe(e,o.isSelected);return S(()=>F(s,{transition:g.value,disabled:!c.value},{default:()=>[E(P(`div`,{class:A([`v-window-item`,o.selectedClass.value,e.class]),style:j(e.style)},[_.value&&r.default?.()]),[[te,o.isSelected.value]])]})),{groupItem:o}}}),Q=p({...X()},`VTabsWindowItem`),$=u()({name:`VTabsWindowItem`,props:Q(),setup(e,t){let{slots:n}=t;return S(()=>{let t=Z.filterProps(e);return F(Z,L({_as:`VTabsWindowItem`},t,{class:[`v-tabs-window-item`,e.class],style:e.style}),n)}),{}}});function ve(e){return e?e.map(e=>o(e)?e:{text:e,value:e}):[]}const ye=p({alignTabs:{type:String,default:`start`},color:String,fixedTabs:Boolean,items:{type:Array,default:()=>[]},stacked:Boolean,bgColor:String,grow:Boolean,height:{type:[Number,String],default:void 0},hideSlider:Boolean,sliderColor:String,...ce({mandatory:`force`,selectedClass:`v-tab-item--selected`}),...r(),...g()},`VTabs`),be=u()({name:`VTabs`,props:ye(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:r}=t,a=x(e,`modelValue`),o=N(()=>ve(e.items)),{densityClasses:s}=i(e),{backgroundColorClasses:l,backgroundColorStyles:u}=c(()=>e.bgColor),{scopeId:f}=ie();return d({VTab:{color:k(()=>e.color),direction:k(()=>e.direction),stacked:k(()=>e.stacked),fixed:k(()=>e.fixedTabs),sliderColor:k(()=>e.sliderColor),hideSlider:k(()=>e.hideSlider)}}),S(()=>{let t=B.filterProps(e),i=!!(r.window||e.items.length>0);return P(M,null,[F(B,L(t,{modelValue:a.value,"onUpdate:modelValue":e=>a.value=e,class:[`v-tabs`,`v-tabs--${e.direction}`,`v-tabs--align-tabs-${e.alignTabs}`,{"v-tabs--fixed-tabs":e.fixedTabs,"v-tabs--grow":e.grow,"v-tabs--stacked":e.stacked},s.value,l.value,e.class],style:[{"--v-tabs-height":h(e.height)},u.value,e.style],role:`tablist`,symbol:V},f,n),{default:()=>[r.default?.()??o.value.map(e=>r.tab?.({item:e})??F(H,L(e,{key:e.text,value:e.value}),{default:r[`tab.${e.value}`]?()=>r[`tab.${e.value}`]?.({item:e}):void 0}))]}),i&&F(Y,L({modelValue:a.value,"onUpdate:modelValue":e=>a.value=e,key:`tabs-window`},f),{default:()=>[o.value.map(e=>r.item?.({item:e})??F($,{value:e.value},{default:()=>r[`item.${e.value}`]?.({item:e})})),r.window?.()]})])}),{}}});export{be as b,$ as c,Y as d,H as e};