import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{I as n,_ as r,ai as i,b as a,bB as o,bF as s,bJ as c,bK as l,bO as u,bS as d,bc as f,bd as p,bg as m,bh as h,bj as g,bl as _,bm as v,bn as y,bv as b,bx as x,bz as ee,j as S,n as C,o as w,p as T,s as E,t as D,u as O}from"./index-BSnscBhv.js";import{b as k,c as A,d as j}from"./VCard-DVRc-Pxh.js";import{b as M}from"./VChip-CBN0Kf2u.js";import{b as N}from"./VTextField-BU8lnKH2.js";import{b as P}from"./VForm-CDHrkI-n.js";import{b as F}from"./VSnackbar-KpoxlJmd.js";const I={class:`text-h6 font-weight-bold mb-2`},L={class:`text-subtitle-2 text-medium-emphasis mb-4`};var R=y({__name:`ProfileView`,setup(a){let y=i(),R=l(),z=l(),B=l(!1),V=l(!1),H=l(!1),U=l(!1),W=l(!1),G=l(!1),K=l(!1),q=l(!1),J=l(!1),Y=l(``),X=l(``),Z=c({full_name:``,username:``,email:``,phone:``}),Q=c({current_password:``,new_password:``,confirm_password:``}),te=[e=>!!e||`Full name is required`,e=>e.length>=2||`Name must be at least 2 characters`],ne=[e=>!!e||`Username is required`,e=>e.length>=3||`Username must be at least 3 characters`],re=[e=>!!e||`Email is required`,e=>/.+@.+\..+/.test(e)||`Email must be valid`],ie=[e=>!!e||`Current password is required`],ae=[e=>!!e||`New password is required`,e=>e.length>=6||`Password must be at least 6 characters`],oe=[e=>!!e||`Please confirm your password`,e=>e===Q.new_password||`Passwords do not match`],se=async()=>{if(B.value){H.value=!0;try{await y.updateProfile(Z),Y.value=`Profile updated successfully!`,q.value=!0}catch(e){X.value=e.response?.data?.message||`Failed to update profile`,J.value=!0}finally{H.value=!1}}},ce=async()=>{if(V.value){U.value=!0;try{await y.changePassword(Q),Y.value=`Password changed successfully!`,q.value=!0,Q.current_password=``,Q.new_password=``,Q.confirm_password=``,z.value?.reset()}catch(e){X.value=e.response?.data?.message||`Failed to change password`,J.value=!0}finally{U.value=!1}}},le=e=>{let t={admin:`Administrator`,dekan:`Dekan`,wakil_dekan:`Wakil Dekan`,kepala_prodi:`Kepala Program Studi`,sekretaris_prodi:`Sekretaris Program Studi`,dosen:`Dosen`};return t[e]||e},$=e=>e?new Date(e).toLocaleDateString(`id-ID`,{year:`numeric`,month:`long`,day:`numeric`}):`N/A`;return b(()=>{y.user&&(Z.full_name=y.user.full_name,Z.username=y.user.username,Z.email=y.user.email,Z.phone=y.user.phone||``)}),(i,a)=>{let c=o(`v-list-item-prepend`);return x(),g(`div`,null,[v(e,{class:`mb-6`},{default:s(()=>[v(t,null,{default:s(()=>a[16]||=[m(`h1`,{class:`text-h4 font-weight-bold text-primary`},`User Profile`,-1),m(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Manage your account settings and preferences `,-1)]),_:1,__:[16]})]),_:1}),v(e,null,{default:s(()=>[v(t,{cols:`12`,md:`8`},{default:s(()=>[v(k,{class:`mb-6`},{default:s(()=>[v(j,{class:`text-h6 font-weight-bold`},{default:s(()=>[v(n,{start:``},{default:s(()=>a[17]||=[_(`mdi-account-circle`,-1)]),_:1,__:[17]}),a[18]||=_(` Profile Information `,-1)]),_:1,__:[18]}),v(A,null,{default:s(()=>[v(P,{ref_key:`profileForm`,ref:R,modelValue:B.value,"onUpdate:modelValue":a[4]||=e=>B.value=e,onSubmit:f(se,[`prevent`])},{default:s(()=>[v(e,null,{default:s(()=>[v(t,{cols:`12`,md:`6`},{default:s(()=>[v(N,{modelValue:Z.full_name,"onUpdate:modelValue":a[0]||=e=>Z.full_name=e,rules:te,label:`Full Name`,variant:`outlined`,"prepend-inner-icon":`mdi-account`,disabled:H.value},null,8,[`modelValue`,`disabled`])]),_:1}),v(t,{cols:`12`,md:`6`},{default:s(()=>[v(N,{modelValue:Z.username,"onUpdate:modelValue":a[1]||=e=>Z.username=e,rules:ne,label:`Username`,variant:`outlined`,"prepend-inner-icon":`mdi-at`,disabled:H.value},null,8,[`modelValue`,`disabled`])]),_:1}),v(t,{cols:`12`,md:`6`},{default:s(()=>[v(N,{modelValue:Z.email,"onUpdate:modelValue":a[2]||=e=>Z.email=e,rules:re,label:`Email`,type:`email`,variant:`outlined`,"prepend-inner-icon":`mdi-email`,disabled:H.value},null,8,[`modelValue`,`disabled`])]),_:1}),v(t,{cols:`12`,md:`6`},{default:s(()=>[v(N,{modelValue:Z.phone,"onUpdate:modelValue":a[3]||=e=>Z.phone=e,label:`Phone Number`,variant:`outlined`,"prepend-inner-icon":`mdi-phone`,disabled:H.value},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1}),v(e,null,{default:s(()=>[v(t,null,{default:s(()=>[v(O,{type:`submit`,color:`primary`,loading:H.value,disabled:!B.value},{default:s(()=>[v(n,{start:``},{default:s(()=>a[19]||=[_(`mdi-content-save`,-1)]),_:1,__:[19]}),a[20]||=_(` Update Profile `,-1)]),_:1,__:[20]},8,[`loading`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`])]),_:1})]),_:1}),v(k,null,{default:s(()=>[v(j,{class:`text-h6 font-weight-bold`},{default:s(()=>[v(n,{start:``},{default:s(()=>a[21]||=[_(`mdi-lock`,-1)]),_:1,__:[21]}),a[22]||=_(` Change Password `,-1)]),_:1,__:[22]}),v(A,null,{default:s(()=>[v(P,{ref_key:`passwordForm`,ref:z,modelValue:V.value,"onUpdate:modelValue":a[11]||=e=>V.value=e,onSubmit:f(ce,[`prevent`])},{default:s(()=>[v(e,null,{default:s(()=>[v(t,{cols:`12`},{default:s(()=>[v(N,{modelValue:Q.current_password,"onUpdate:modelValue":a[5]||=e=>Q.current_password=e,rules:ie,type:W.value?`text`:`password`,label:`Current Password`,variant:`outlined`,"prepend-inner-icon":`mdi-lock`,"append-inner-icon":W.value?`mdi-eye`:`mdi-eye-off`,"onClick:appendInner":a[6]||=e=>W.value=!W.value,disabled:U.value},null,8,[`modelValue`,`type`,`append-inner-icon`,`disabled`])]),_:1}),v(t,{cols:`12`,md:`6`},{default:s(()=>[v(N,{modelValue:Q.new_password,"onUpdate:modelValue":a[7]||=e=>Q.new_password=e,rules:ae,type:G.value?`text`:`password`,label:`New Password`,variant:`outlined`,"prepend-inner-icon":`mdi-lock-plus`,"append-inner-icon":G.value?`mdi-eye`:`mdi-eye-off`,"onClick:appendInner":a[8]||=e=>G.value=!G.value,disabled:U.value},null,8,[`modelValue`,`type`,`append-inner-icon`,`disabled`])]),_:1}),v(t,{cols:`12`,md:`6`},{default:s(()=>[v(N,{modelValue:Q.confirm_password,"onUpdate:modelValue":a[9]||=e=>Q.confirm_password=e,rules:oe,type:K.value?`text`:`password`,label:`Confirm New Password`,variant:`outlined`,"prepend-inner-icon":`mdi-lock-check`,"append-inner-icon":K.value?`mdi-eye`:`mdi-eye-off`,"onClick:appendInner":a[10]||=e=>K.value=!K.value,disabled:U.value},null,8,[`modelValue`,`type`,`append-inner-icon`,`disabled`])]),_:1})]),_:1}),v(e,null,{default:s(()=>[v(t,null,{default:s(()=>[v(O,{type:`submit`,color:`warning`,loading:U.value,disabled:!V.value},{default:s(()=>[v(n,{start:``},{default:s(()=>a[23]||=[_(`mdi-key-change`,-1)]),_:1,__:[23]}),a[24]||=_(` Change Password `,-1)]),_:1,__:[24]},8,[`loading`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`])]),_:1})]),_:1})]),_:1}),v(t,{cols:`12`,md:`4`},{default:s(()=>[v(k,null,{default:s(()=>[v(A,{class:`text-center`},{default:s(()=>[v(D,{size:`120`,class:`mb-4`},{default:s(()=>[u(y).user?.avatar?(x(),h(r,{key:0,src:u(y).user.avatar,alt:u(y).user?.full_name},null,8,[`src`,`alt`])):(x(),h(n,{key:1,size:`60`},{default:s(()=>a[25]||=[_(`mdi-account`,-1)]),_:1,__:[25]}))]),_:1}),m(`h3`,I,d(u(y).user?.full_name),1),m(`p`,L,d(u(y).user?.email),1),(x(!0),g(p,null,ee(u(y).user?.roles,e=>(x(),h(M,{key:e,class:`ma-1`,color:`primary`,variant:`tonal`,size:`small`},{default:s(()=>[_(d(le(e)),1)]),_:2},1024))),128))]),_:1}),v(E),v(A,null,{default:s(()=>[v(S,{density:`compact`},{default:s(()=>[v(C,null,{default:s(()=>[v(c,null,{default:s(()=>[v(n,null,{default:s(()=>a[26]||=[_(`mdi-calendar`,-1)]),_:1,__:[26]})]),_:1}),v(w,null,{default:s(()=>a[27]||=[_(`Member Since`,-1)]),_:1,__:[27]}),v(T,null,{default:s(()=>[_(d($(u(y).user?.created_at)),1)]),_:1})]),_:1}),v(C,null,{default:s(()=>[v(c,null,{default:s(()=>[v(n,null,{default:s(()=>a[28]||=[_(`mdi-clock`,-1)]),_:1,__:[28]})]),_:1}),v(w,null,{default:s(()=>a[29]||=[_(`Last Login`,-1)]),_:1,__:[29]}),v(T,null,{default:s(()=>[_(d($(u(y).user?.last_login)),1)]),_:1})]),_:1}),v(C,null,{default:s(()=>[v(c,null,{default:s(()=>[v(n,null,{default:s(()=>a[30]||=[_(`mdi-shield-check`,-1)]),_:1,__:[30]})]),_:1}),v(w,null,{default:s(()=>a[31]||=[_(`Account Status`,-1)]),_:1,__:[31]}),v(T,null,{default:s(()=>[v(M,{color:u(y).user?.is_active?`success`:`error`,size:`x-small`,variant:`tonal`},{default:s(()=>[_(d(u(y).user?.is_active?`Active`:`Inactive`),1)]),_:1},8,[`color`])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),v(F,{modelValue:q.value,"onUpdate:modelValue":a[13]||=e=>q.value=e,color:`success`,timeout:`3000`},{actions:s(()=>[v(O,{onClick:a[12]||=e=>q.value=!1},{default:s(()=>a[32]||=[_(`Close`,-1)]),_:1,__:[32]})]),default:s(()=>[_(d(Y.value)+` `,1)]),_:1},8,[`modelValue`]),v(F,{modelValue:J.value,"onUpdate:modelValue":a[15]||=e=>J.value=e,color:`error`,timeout:`5000`},{actions:s(()=>[v(O,{onClick:a[14]||=e=>J.value=!1},{default:s(()=>a[33]||=[_(`Close`,-1)]),_:1,__:[33]})]),default:s(()=>[_(d(X.value)+` `,1)]),_:1},8,[`modelValue`])])}}}),z=a(R,[[`__scopeId`,`data-v-dc82110f`]]);export{z as default};