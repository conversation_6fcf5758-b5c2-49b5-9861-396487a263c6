import{I as e,J as t,K as n,T as r,U as i,W as a,X as o,a3 as s,a4 as c,a5 as l,a7 as u,a8 as d,a9 as f,aC as p,aD as m,aG as h,aH as g,aR as _,af as v,an as y,ao as b,ap as x,av as S,ax as C,b5 as w,bD as T,bK as E,bL as D,bM as O,bQ as k,bR as A,bf as j,bg as M,bm as N}from"./index-BSnscBhv.js";const P=g({dotColor:String,fillDot:Boolean,hideDot:Boolean,icon:x,iconColor:String,lineColor:String,...h(),...s(),...t(),...a()},`VTimelineDivider`),F=p()({name:`VTimelineDivider`,props:P(),setup(t,r){let{slots:i}=r,{sizeClasses:a,sizeStyles:s}=n(t,`v-timeline-divider__dot`),{backgroundColorStyles:u,backgroundColorClasses:d}=l(()=>t.dotColor),{roundedClasses:p}=c(t,`v-timeline-divider__dot`),{elevationClasses:m}=o(t),{backgroundColorClasses:h,backgroundColorStyles:g}=l(()=>t.lineColor);return C(()=>M(`div`,{class:k([`v-timeline-divider`,{"v-timeline-divider--fill-dot":t.fillDot},t.class]),style:A(t.style)},[M(`div`,{class:k([`v-timeline-divider__before`,h.value]),style:A(g.value)},null),!t.hideDot&&M(`div`,{key:`dot`,class:k([`v-timeline-divider__dot`,m.value,p.value,a.value]),style:A(s.value)},[M(`div`,{class:k([`v-timeline-divider__inner-dot`,d.value,p.value]),style:A(u.value)},[i.default?N(f,{key:`icon-defaults`,disabled:!t.icon,defaults:{VIcon:{color:t.iconColor,icon:t.icon,size:t.size}}},i.default):N(e,{key:`icon`,color:t.iconColor,icon:t.icon,size:t.size},null)])]),M(`div`,{class:k([`v-timeline-divider__after`,h.value]),style:A(g.value)},null)])),{}}}),I=g({density:String,dotColor:String,fillDot:Boolean,hideDot:Boolean,hideOpposite:{type:Boolean,default:void 0},icon:x,iconColor:String,lineInset:[Number,String],side:{type:String,validator:e=>e==null||[`start`,`end`].includes(e)},...h(),...u(),...a(),...s(),...t(),...v()},`VTimelineItem`),L=p()({name:`VTimelineItem`,props:I(),setup(e,t){let{slots:n}=t,{dimensionStyles:r}=d(e),i=D(0),a=E();return T(a,e=>{e&&(i.value=e.$el.querySelector(`.v-timeline-divider__dot`)?.getBoundingClientRect().width??0)},{flush:`post`}),C(()=>M(`div`,{class:k([`v-timeline-item`,{"v-timeline-item--fill-dot":e.fillDot,"v-timeline-item--side-start":e.side===`start`,"v-timeline-item--side-end":e.side===`end`},e.class]),style:A([{"--v-timeline-dot-size":_(i.value),"--v-timeline-line-inset":e.lineInset?`calc(var(--v-timeline-dot-size) / 2 + ${_(e.lineInset)})`:_(0)},e.style])},[M(`div`,{class:`v-timeline-item__body`,style:A(r.value)},[n.default?.()]),N(F,{ref:a,hideDot:e.hideDot,icon:e.icon,iconColor:e.iconColor,size:e.size,elevation:e.elevation,dotColor:e.dotColor,fillDot:e.fillDot,rounded:e.rounded},{default:n.icon}),e.density!==`compact`&&M(`div`,{class:`v-timeline-item__opposite`},[!e.hideOpposite&&n.opposite?.()])])),{}}}),R=g({align:{type:String,default:`center`,validator:e=>[`center`,`start`].includes(e)},direction:{type:String,default:`vertical`,validator:e=>[`vertical`,`horizontal`].includes(e)},justify:{type:String,default:`auto`,validator:e=>[`auto`,`center`].includes(e)},side:{type:String,validator:e=>e==null||[`start`,`end`].includes(e)},lineThickness:{type:[String,Number],default:2},lineColor:String,truncateLine:{type:String,validator:e=>[`start`,`end`,`both`].includes(e)},...w(I({lineInset:0}),[`dotColor`,`fillDot`,`hideOpposite`,`iconColor`,`lineInset`,`size`]),...h(),...r(),...v(),...y()},`VTimeline`),z=p()({name:`VTimeline`,props:R(),setup(e,t){let{slots:n}=t,{themeClasses:r}=b(e),{densityClasses:a}=i(e),{rtlClasses:o}=S();m({VTimelineDivider:{lineColor:O(()=>e.lineColor)},VTimelineItem:{density:O(()=>e.density),dotColor:O(()=>e.dotColor),fillDot:O(()=>e.fillDot),hideOpposite:O(()=>e.hideOpposite),iconColor:O(()=>e.iconColor),lineColor:O(()=>e.lineColor),lineInset:O(()=>e.lineInset),size:O(()=>e.size)}});let s=j(()=>{let t=e.side?e.side:e.density===`default`?null:`end`;return t&&`v-timeline--side-${t}`}),c=j(()=>{let t=[`v-timeline--truncate-line-start`,`v-timeline--truncate-line-end`];switch(e.truncateLine){case`both`:return t;case`start`:return t[0];case`end`:return t[1];default:return null}});return C(()=>N(e.tag,{class:k([`v-timeline`,`v-timeline--${e.direction}`,`v-timeline--align-${e.align}`,`v-timeline--justify-${e.justify}`,c.value,{"v-timeline--inset-line":!!e.lineInset},r.value,a.value,s.value,o.value,e.class]),style:A([{"--v-timeline-line-thickness":_(e.lineThickness)},e.style])},n)),{}}});export{z as b,L as c};