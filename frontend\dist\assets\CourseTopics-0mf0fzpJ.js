import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{I as n,b as r,bF as i,bJ as a,bK as o,bS as s,bd as ee,bf as c,bg as l,bh as u,bi as d,bj as f,bl as p,bm as m,bn as te,bv as ne,bx as h,bz as re,q as ie,u as g}from"./index-BSnscBhv.js";import{d as _}from"./api-BWRuf0Vj.js";import{b as v,c as y,d as b,e as ae}from"./VCard-DVRc-Pxh.js";import{b as x}from"./VChip-CBN0Kf2u.js";import{b as S,c as oe}from"./VTimeline-uQEqPSC6.js";import{b as se}from"./VSelect-DqM1bu6y.js";import{b as ce}from"./VDialog-VHlGBbps.js";import{b as C}from"./VTextField-BU8lnKH2.js";import{b as le,c as w}from"./FormModal-CAo97PhI.js";import{b as T}from"./VSnackbar-KpoxlJmd.js";import{b as E}from"./VTextarea-BciMMY-M.js";const ue={class:`pa-6`},D={class:`d-flex align-center justify-space-between mb-4`},O={class:`text-h6 font-weight-bold`},k={class:`d-flex align-start justify-space-between`},A={class:`flex-grow-1`},j={class:`d-flex align-center mb-2`},M={class:`text-subtitle-1 font-weight-bold mb-1`},N={key:0,class:`text-body-2 text-medium-emphasis mb-1`},P={key:1,class:`text-body-2 text-medium-emphasis mb-1`},F={key:2,class:`text-body-2 text-medium-emphasis mb-1`},I={key:3,class:`text-body-2 mb-1`},L=[`href`],R={key:4,class:`text-body-2 text-medium-emphasis`},de={class:`d-flex flex-column gap-1 ml-4`},fe={key:0,class:`text-center py-8`};var z=te({__name:`CourseReferences`,props:{course:{}},emits:[`close`],setup(r,{emit:te}){let S=r,oe=o(!1),z=o(!1),B=o(!1),V=o(!1),H=o(!1),U=o(!1),W=o(!1),G=o(``),K=o(``),q=o([]),J=o(null),Y=o(`create`),X=a({type:`utama`,title:``,author:``,publisher:``,year:null,isbn:``,url:``,notes:``}),pe=c(()=>Y.value===`create`?`Add Reference`:`Edit Reference`),me=[{title:`Main Reference (Utama)`,value:`utama`},{title:`Supporting Reference (Pendukung)`,value:`pendukung`},{title:`Additional Reference (Tambahan)`,value:`tambahan`}],he=[e=>!!e||`Reference type is required`],ge=[e=>!!e||`Title is required`,e=>e.length>=3||`Title must be at least 3 characters`],Z=async()=>{oe.value=!0;try{let e=await _.getReferences(S.course.id);q.value=e.data.data}catch{K.value=`Failed to load references`,W.value=!0}finally{oe.value=!1}},_e=()=>{Y.value=`create`,$(),V.value=!0},ve=e=>{Y.value=`edit`,J.value=e,ye(e),V.value=!0},Q=e=>{J.value=e,H.value=!0},$=()=>{Object.assign(X,{type:`utama`,title:``,author:``,publisher:``,year:null,isbn:``,url:``,notes:``})},ye=e=>{Object.assign(X,{type:e.type,title:e.title,author:e.author||``,publisher:e.publisher||``,year:e.year,isbn:e.isbn||``,url:e.url||``,notes:e.notes||``})},be=async()=>{z.value=!0;try{let e={...X,course_id:S.course.id};Y.value===`create`?(await _.createReference(S.course.id,e),G.value=`Reference added successfully!`):J.value&&(await _.updateReference(S.course.id,J.value.id,e),G.value=`Reference updated successfully!`),U.value=!0,Se(),await Z()}catch(e){K.value=e.response?.data?.message||`Operation failed`,W.value=!0}finally{z.value=!1}},xe=async()=>{if(J.value){B.value=!0;try{await _.deleteReference(S.course.id,J.value.id),G.value=`Reference deleted successfully!`,U.value=!0,H.value=!1,await Z()}catch(e){K.value=e.response?.data?.message||`Delete failed`,W.value=!0}finally{B.value=!1}}},Se=()=>{V.value=!1,J.value=null,$()},Ce=e=>{let t={utama:`primary`,pendukung:`success`,tambahan:`info`};return t[e]||`primary`},we=e=>{let t={utama:`Main`,pendukung:`Supporting`,tambahan:`Additional`};return t[e]||e};return ne(()=>{Z()}),(r,a)=>(h(),f(`div`,ue,[l(`div`,D,[l(`div`,null,[l(`h3`,O,s(r.course.name)+` - References`,1),a[13]||=l(`p`,{class:`text-subtitle-2 text-medium-emphasis`},` Manage course references and learning materials `,-1)]),m(g,{color:`primary`,onClick:_e},{default:i(()=>[m(n,{start:``},{default:i(()=>a[14]||=[p(`mdi-plus`,-1)]),_:1,__:[14]}),a[15]||=p(` Add Reference `,-1)]),_:1,__:[15]})]),m(e,null,{default:i(()=>[(h(!0),f(ee,null,re(q.value,e=>(h(),u(t,{key:e.id,cols:`12`},{default:i(()=>[m(v,{variant:`outlined`,class:`mb-3`},{default:i(()=>[m(y,null,{default:i(()=>[l(`div`,k,[l(`div`,A,[l(`div`,j,[m(x,{color:Ce(e.type),size:`small`,variant:`tonal`,class:`mr-2`},{default:i(()=>[p(s(we(e.type)),1)]),_:2},1032,[`color`]),e.year?(h(),u(x,{key:0,size:`small`,variant:`outlined`},{default:i(()=>[p(s(e.year),1)]),_:2},1024)):d(``,!0)]),l(`h4`,M,s(e.title),1),e.author?(h(),f(`p`,N,[m(n,{size:`small`,class:`mr-1`},{default:i(()=>a[16]||=[p(`mdi-account`,-1)]),_:1,__:[16]}),p(` `+s(e.author),1)])):d(``,!0),e.publisher?(h(),f(`p`,P,[m(n,{size:`small`,class:`mr-1`},{default:i(()=>a[17]||=[p(`mdi-domain`,-1)]),_:1,__:[17]}),p(` `+s(e.publisher),1)])):d(``,!0),e.isbn?(h(),f(`p`,F,[m(n,{size:`small`,class:`mr-1`},{default:i(()=>a[18]||=[p(`mdi-barcode`,-1)]),_:1,__:[18]}),p(` ISBN: `+s(e.isbn),1)])):d(``,!0),e.url?(h(),f(`p`,I,[m(n,{size:`small`,class:`mr-1`},{default:i(()=>a[19]||=[p(`mdi-link`,-1)]),_:1,__:[19]}),l(`a`,{href:e.url,target:`_blank`,class:`text-primary`},s(e.url),9,L)])):d(``,!0),e.notes?(h(),f(`p`,R,[m(n,{size:`small`,class:`mr-1`},{default:i(()=>a[20]||=[p(`mdi-note-text`,-1)]),_:1,__:[20]}),p(` `+s(e.notes),1)])):d(``,!0)]),l(`div`,de,[m(g,{icon:``,size:`small`,variant:`text`,onClick:t=>ve(e)},{default:i(()=>[m(n,{size:`small`},{default:i(()=>a[21]||=[p(`mdi-pencil`,-1)]),_:1,__:[21]}),m(w,{activator:`parent`},{default:i(()=>a[22]||=[p(`Edit`,-1)]),_:1,__:[22]})]),_:2},1032,[`onClick`]),m(g,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>Q(e)},{default:i(()=>[m(n,{size:`small`},{default:i(()=>a[23]||=[p(`mdi-delete`,-1)]),_:1,__:[23]}),m(w,{activator:`parent`},{default:i(()=>a[24]||=[p(`Delete`,-1)]),_:1,__:[24]})]),_:2},1032,[`onClick`])])])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),q.value.length===0?(h(),f(`div`,fe,[m(n,{size:`64`,color:`grey-lighten-2`},{default:i(()=>a[25]||=[p(`mdi-book-multiple`,-1)]),_:1,__:[25]}),a[28]||=l(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`No references added yet`,-1),m(g,{color:`primary`,onClick:_e,class:`mt-2`},{default:i(()=>[m(n,{start:``},{default:i(()=>a[26]||=[p(`mdi-plus`,-1)]),_:1,__:[26]}),a[27]||=p(` Add First Reference `,-1)]),_:1,__:[27]})])):d(``,!0),m(le,{modelValue:V.value,"onUpdate:modelValue":a[8]||=e=>V.value=e,title:pe.value,icon:`mdi-book-multiple`,mode:Y.value,loading:z.value,"max-width":`700`,onSubmit:be,onClose:Se},{default:i(()=>[m(e,null,{default:i(()=>[m(t,{cols:`12`},{default:i(()=>[m(se,{modelValue:X.type,"onUpdate:modelValue":a[0]||=e=>X.type=e,items:me,rules:he,label:`Reference Type *`,variant:`outlined`,"prepend-inner-icon":`mdi-tag`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(C,{modelValue:X.title,"onUpdate:modelValue":a[1]||=e=>X.title=e,rules:ge,label:`Title *`,variant:`outlined`,"prepend-inner-icon":`mdi-book`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`,md:`6`},{default:i(()=>[m(C,{modelValue:X.author,"onUpdate:modelValue":a[2]||=e=>X.author=e,label:`Author`,variant:`outlined`,"prepend-inner-icon":`mdi-account`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`,md:`6`},{default:i(()=>[m(C,{modelValue:X.publisher,"onUpdate:modelValue":a[3]||=e=>X.publisher=e,label:`Publisher`,variant:`outlined`,"prepend-inner-icon":`mdi-domain`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`,md:`6`},{default:i(()=>[m(C,{modelValue:X.year,"onUpdate:modelValue":a[4]||=e=>X.year=e,label:`Year`,type:`number`,variant:`outlined`,"prepend-inner-icon":`mdi-calendar`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`,md:`6`},{default:i(()=>[m(C,{modelValue:X.isbn,"onUpdate:modelValue":a[5]||=e=>X.isbn=e,label:`ISBN`,variant:`outlined`,"prepend-inner-icon":`mdi-barcode`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(C,{modelValue:X.url,"onUpdate:modelValue":a[6]||=e=>X.url=e,label:`URL`,type:`url`,variant:`outlined`,"prepend-inner-icon":`mdi-link`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(E,{modelValue:X.notes,"onUpdate:modelValue":a[7]||=e=>X.notes=e,label:`Notes`,variant:`outlined`,"prepend-inner-icon":`mdi-note-text`,rows:`3`,disabled:z.value},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`mode`,`loading`]),m(ce,{modelValue:H.value,"onUpdate:modelValue":a[10]||=e=>H.value=e,"max-width":`400`},{default:i(()=>[m(v,null,{default:i(()=>[m(b,{class:`text-h6`},{default:i(()=>a[29]||=[p(`Confirm Delete`,-1)]),_:1,__:[29]}),m(y,null,{default:i(()=>a[30]||=[p(` Are you sure you want to delete this reference? This action cannot be undone. `,-1)]),_:1,__:[30]}),m(ae,null,{default:i(()=>[m(ie),m(g,{onClick:a[9]||=e=>H.value=!1},{default:i(()=>a[31]||=[p(`Cancel`,-1)]),_:1,__:[31]}),m(g,{color:`error`,loading:B.value,onClick:xe},{default:i(()=>a[32]||=[p(` Delete `,-1)]),_:1,__:[32]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(T,{modelValue:U.value,"onUpdate:modelValue":a[11]||=e=>U.value=e,color:`success`,timeout:`3000`},{default:i(()=>[p(s(G.value),1)]),_:1},8,[`modelValue`]),m(T,{modelValue:W.value,"onUpdate:modelValue":a[12]||=e=>W.value=e,color:`error`,timeout:`5000`},{default:i(()=>[p(s(K.value),1)]),_:1},8,[`modelValue`])]))}}),B=r(z,[[`__scopeId`,`data-v-f8ec663b`]]);const V={class:`pa-6`},H={class:`d-flex align-center justify-space-between mb-4`},U={class:`text-h6 font-weight-bold`},W={class:`text-caption font-weight-bold`},G={class:`text-h6`},K={class:`text-subtitle-2 text-medium-emphasis`},q={class:`d-flex gap-1`},J={key:0,class:`mb-3`},Y={class:`text-body-2`},X={key:1,class:`mb-3`},pe={class:`text-body-2`},me={key:2,class:`mb-3`},he={class:`text-body-2`},ge={key:3,class:`d-flex align-center`},Z={class:`text-caption`},_e={key:0,class:`text-center py-8`};var ve=te({__name:`CourseTopics`,props:{course:{}},emits:[`close`],setup(r,{emit:te}){let x=r,ue=o(!1),D=o(!1),O=o(!1),k=o(!1),A=o(!1),j=o(!1),M=o(!1),N=o(``),P=o(``),F=o([]),I=o(null),L=o(`create`),R=a({week_number:null,topic_title:``,description:``,learning_materials:``,learning_methods:``,estimated_time:null}),de=c(()=>L.value===`create`?`Add Topic`:`Edit Topic`),fe=c(()=>[...F.value].sort((e,t)=>e.week_number-t.week_number)),z=c(()=>{let e=F.value.filter(e=>L.value===`create`||e.id!==I.value?.id).map(e=>e.week_number);return Array.from({length:16},(t,n)=>({title:`Week ${n+1}`,value:n+1,disabled:e.includes(n+1)}))}),B=[e=>!!e||`Week number is required`],ve=[e=>!!e||`Topic title is required`,e=>e.length>=3||`Title must be at least 3 characters`],Q=async()=>{ue.value=!0;try{let e=await _.getTopics(x.course.id);F.value=e.data.data}catch{P.value=`Failed to load topics`,M.value=!0}finally{ue.value=!1}},$=()=>{L.value=`create`,xe(),k.value=!0},ye=e=>{L.value=`edit`,I.value=e,Se(e),k.value=!0},be=e=>{I.value=e,A.value=!0},xe=()=>{Object.assign(R,{week_number:null,topic_title:``,description:``,learning_materials:``,learning_methods:``,estimated_time:null})},Se=e=>{Object.assign(R,{week_number:e.week_number,topic_title:e.topic_title,description:e.description||``,learning_materials:e.learning_materials||``,learning_methods:e.learning_methods||``,estimated_time:e.estimated_time})},Ce=async()=>{D.value=!0;try{let e={...R,course_id:x.course.id};L.value===`create`?(await _.createTopic(x.course.id,e),N.value=`Topic added successfully!`):I.value&&(await _.updateTopic(x.course.id,I.value.id,e),N.value=`Topic updated successfully!`),j.value=!0,Te(),await Q()}catch(e){P.value=e.response?.data?.message||`Operation failed`,M.value=!0}finally{D.value=!1}},we=async()=>{if(I.value){O.value=!0;try{await _.deleteTopic(x.course.id,I.value.id),N.value=`Topic deleted successfully!`,j.value=!0,A.value=!1,await Q()}catch(e){P.value=e.response?.data?.message||`Delete failed`,M.value=!0}finally{O.value=!1}}},Te=()=>{k.value=!1,I.value=null,xe()},Ee=e=>{let t=[`primary`,`success`,`warning`,`info`,`secondary`];return t[(e-1)%t.length]},De=e=>{if(e<60)return`${e} minutes`;let t=Math.floor(e/60),n=e%60;return n>0?`${t}h ${n}m`:`${t}h`};return ne(()=>{Q()}),(r,a)=>(h(),f(`div`,V,[l(`div`,H,[l(`div`,null,[l(`h3`,U,s(r.course.name)+` - Weekly Topics`,1),a[11]||=l(`p`,{class:`text-subtitle-2 text-medium-emphasis`},` Plan and manage weekly course topics and learning materials `,-1)]),m(g,{color:`primary`,onClick:$},{default:i(()=>[m(n,{start:``},{default:i(()=>a[12]||=[p(`mdi-plus`,-1)]),_:1,__:[12]}),a[13]||=p(` Add Topic `,-1)]),_:1,__:[13]})]),m(S,{align:`start`,class:`topic-timeline`},{default:i(()=>[(h(!0),f(ee,null,re(fe.value,e=>(h(),u(oe,{key:e.id,"dot-color":Ee(e.week_number),size:`small`},{icon:i(()=>[l(`span`,W,s(e.week_number),1)]),default:i(()=>[m(v,{variant:`outlined`,class:`mb-4`},{default:i(()=>[m(b,{class:`d-flex align-center justify-space-between`},{default:i(()=>[l(`div`,null,[l(`h4`,G,`Week `+s(e.week_number),1),l(`p`,K,s(e.topic_title),1)]),l(`div`,q,[m(g,{icon:``,size:`small`,variant:`text`,onClick:t=>ye(e)},{default:i(()=>[m(n,{size:`small`},{default:i(()=>a[14]||=[p(`mdi-pencil`,-1)]),_:1,__:[14]}),m(w,{activator:`parent`},{default:i(()=>a[15]||=[p(`Edit`,-1)]),_:1,__:[15]})]),_:2},1032,[`onClick`]),m(g,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>be(e)},{default:i(()=>[m(n,{size:`small`},{default:i(()=>a[16]||=[p(`mdi-delete`,-1)]),_:1,__:[16]}),m(w,{activator:`parent`},{default:i(()=>a[17]||=[p(`Delete`,-1)]),_:1,__:[17]})]),_:2},1032,[`onClick`])])]),_:2},1024),m(y,null,{default:i(()=>[e.description?(h(),f(`div`,J,[a[18]||=l(`h5`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Description:`,-1),l(`p`,Y,s(e.description),1)])):d(``,!0),e.learning_materials?(h(),f(`div`,X,[a[19]||=l(`h5`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Learning Materials:`,-1),l(`p`,pe,s(e.learning_materials),1)])):d(``,!0),e.learning_methods?(h(),f(`div`,me,[a[20]||=l(`h5`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Learning Methods:`,-1),l(`p`,he,s(e.learning_methods),1)])):d(``,!0),e.estimated_time?(h(),f(`div`,ge,[m(n,{size:`small`,class:`mr-1`},{default:i(()=>a[21]||=[p(`mdi-clock`,-1)]),_:1,__:[21]}),l(`span`,Z,s(De(e.estimated_time)),1)])):d(``,!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,[`dot-color`]))),128))]),_:1}),F.value.length===0?(h(),f(`div`,_e,[m(n,{size:`64`,color:`grey-lighten-2`},{default:i(()=>a[22]||=[p(`mdi-format-list-numbered`,-1)]),_:1,__:[22]}),a[25]||=l(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`No topics planned yet`,-1),m(g,{color:`primary`,onClick:$,class:`mt-2`},{default:i(()=>[m(n,{start:``},{default:i(()=>a[23]||=[p(`mdi-plus`,-1)]),_:1,__:[23]}),a[24]||=p(` Plan First Topic `,-1)]),_:1,__:[24]})])):d(``,!0),m(le,{modelValue:k.value,"onUpdate:modelValue":a[6]||=e=>k.value=e,title:de.value,icon:`mdi-format-list-numbered`,mode:L.value,loading:D.value,"max-width":`800`,onSubmit:Ce,onClose:Te},{default:i(()=>[m(e,null,{default:i(()=>[m(t,{cols:`12`,md:`6`},{default:i(()=>[m(se,{modelValue:R.week_number,"onUpdate:modelValue":a[0]||=e=>R.week_number=e,items:z.value,rules:B,label:`Week Number *`,variant:`outlined`,"prepend-inner-icon":`mdi-calendar-week`,disabled:D.value},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),m(t,{cols:`12`,md:`6`},{default:i(()=>[m(C,{modelValue:R.estimated_time,"onUpdate:modelValue":a[1]||=e=>R.estimated_time=e,label:`Estimated Time (minutes)`,type:`number`,variant:`outlined`,"prepend-inner-icon":`mdi-clock`,disabled:D.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(C,{modelValue:R.topic_title,"onUpdate:modelValue":a[2]||=e=>R.topic_title=e,rules:ve,label:`Topic Title *`,variant:`outlined`,"prepend-inner-icon":`mdi-format-title`,disabled:D.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(E,{modelValue:R.description,"onUpdate:modelValue":a[3]||=e=>R.description=e,label:`Description`,variant:`outlined`,"prepend-inner-icon":`mdi-text`,rows:`3`,disabled:D.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(E,{modelValue:R.learning_materials,"onUpdate:modelValue":a[4]||=e=>R.learning_materials=e,label:`Learning Materials`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open`,rows:`3`,disabled:D.value},null,8,[`modelValue`,`disabled`])]),_:1}),m(t,{cols:`12`},{default:i(()=>[m(E,{modelValue:R.learning_methods,"onUpdate:modelValue":a[5]||=e=>R.learning_methods=e,label:`Learning Methods`,variant:`outlined`,"prepend-inner-icon":`mdi-teach`,rows:`3`,disabled:D.value},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`mode`,`loading`]),m(ce,{modelValue:A.value,"onUpdate:modelValue":a[8]||=e=>A.value=e,"max-width":`400`},{default:i(()=>[m(v,null,{default:i(()=>[m(b,{class:`text-h6`},{default:i(()=>a[26]||=[p(`Confirm Delete`,-1)]),_:1,__:[26]}),m(y,null,{default:i(()=>a[27]||=[p(` Are you sure you want to delete this topic? This action cannot be undone. `,-1)]),_:1,__:[27]}),m(ae,null,{default:i(()=>[m(ie),m(g,{onClick:a[7]||=e=>A.value=!1},{default:i(()=>a[28]||=[p(`Cancel`,-1)]),_:1,__:[28]}),m(g,{color:`error`,loading:O.value,onClick:we},{default:i(()=>a[29]||=[p(` Delete `,-1)]),_:1,__:[29]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(T,{modelValue:j.value,"onUpdate:modelValue":a[9]||=e=>j.value=e,color:`success`,timeout:`3000`},{default:i(()=>[p(s(N.value),1)]),_:1},8,[`modelValue`]),m(T,{modelValue:M.value,"onUpdate:modelValue":a[10]||=e=>M.value=e,color:`error`,timeout:`5000`},{default:i(()=>[p(s(P.value),1)]),_:1},8,[`modelValue`])]))}}),Q=r(ve,[[`__scopeId`,`data-v-89809007`]]);export{Q as b,B as c};