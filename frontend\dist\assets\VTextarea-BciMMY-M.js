import{$ as e,aC as t,aH as n,aN as r,aQ as i,aR as a,aW as o,aw as s,ax as c,bD as l,bE as u,bG as d,bK as f,bL as ee,bQ as p,ba as m,bd as h,bf as g,bg as _,bm as v,bq as y,br as b,bt as x,bv as S,d as C}from"./index-BSnscBhv.js";import{d as w,e as T,f as E,g as D,h as O,i as k,m as A}from"./VTextField-BU8lnKH2.js";const j=n({autoGrow:Boolean,autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:Boolean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...k(),...E()},`VTextarea`),M=t()({name:`VTextarea`,directives:{vIntersect:e},inheritAttrs:!1,props:j(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0,"update:rows":e=>!0},setup(t,n){let{attrs:E,emit:k,slots:j}=n,M=s(t,`modelValue`),{isFocused:N,focus:P,blur:F}=A(t),{onIntersect:I}=w(t),L=g(()=>typeof t.counterValue==`function`?t.counterValue(M.value):(M.value||``).toString().length),R=g(()=>{if(E.maxlength)return E.maxlength;if(!(!t.counter||typeof t.counter!=`number`&&typeof t.counter!=`string`))return t.counter}),z=f(),B=f(),V=ee(``),H=f(),U=g(()=>t.persistentPlaceholder||N.value||t.active);function W(){H.value!==document.activeElement&&H.value?.focus(),N.value||P()}function G(e){W(),k(`click:control`,e)}function K(e){k(`mousedown:control`,e)}function q(e){e.stopPropagation(),W(),b(()=>{M.value=``,r(t[`onClick:clear`],e)})}function J(e){let n=e.target;if(M.value=n.value,t.modelModifiers?.trim){let e=[n.selectionStart,n.selectionEnd];b(()=>{n.selectionStart=e[0],n.selectionEnd=e[1]})}}let Y=f(),X=f(Number(t.rows)),Z=g(()=>[`plain`,`underlined`].includes(t.variant));u(()=>{t.autoGrow||(X.value=Number(t.rows))});function Q(){t.autoGrow&&b(()=>{if(!Y.value||!B.value)return;let e=getComputedStyle(Y.value),n=getComputedStyle(B.value.$el),r=parseFloat(e.getPropertyValue(`--v-field-padding-top`))+parseFloat(e.getPropertyValue(`--v-input-padding-top`))+parseFloat(e.getPropertyValue(`--v-field-padding-bottom`)),o=Y.value.scrollHeight,s=parseFloat(e.lineHeight),c=Math.max(parseFloat(t.rows)*s+r,parseFloat(n.getPropertyValue(`--v-input-control-height`))),l=parseFloat(t.maxRows)*s+r||1/0,u=i(o??0,c,l);X.value=Math.floor((u-r)/s),V.value=a(u)})}S(Q),l(M,Q),l(()=>t.rows,Q),l(()=>t.maxRows,Q),l(()=>t.density,Q),l(X,e=>{k(`update:rows`,e)});let $;return l(Y,e=>{e?($=new ResizeObserver(Q),$.observe(Y.value)):$?.disconnect()}),x(()=>{$?.disconnect()}),c(()=>{let n=!!(j.counter||t.counter||t.counterValue),r=!!(n||j.details),[i,a]=o(E),{modelValue:s,...c}=O.filterProps(t),l=T.filterProps(t);return v(O,y({ref:z,modelValue:M.value,"onUpdate:modelValue":e=>M.value=e,class:[`v-textarea v-text-field`,{"v-textarea--prefixed":t.prefix,"v-textarea--suffixed":t.suffix,"v-text-field--prefixed":t.prefix,"v-text-field--suffixed":t.suffix,"v-textarea--auto-grow":t.autoGrow,"v-textarea--no-resize":t.noResize||t.autoGrow,"v-input--plain-underlined":Z.value},t.class],style:t.style},i,c,{centerAffix:X.value===1&&!Z.value,focused:N.value}),{...j,default:n=>{let{id:r,isDisabled:i,isDirty:o,isReadonly:s,isValid:c,hasDetails:u}=n;return v(T,y({ref:B,style:{"--v-textarea-control-height":V.value},onClick:G,onMousedown:K,"onClick:clear":q,"onClick:prependInner":t[`onClick:prependInner`],"onClick:appendInner":t[`onClick:appendInner`]},l,{id:r.value,active:U.value||o.value,centerAffix:X.value===1&&!Z.value,dirty:o.value||t.dirty,disabled:i.value,focused:N.value,details:u.value,error:c.value===!1}),{...j,default:n=>{let{props:{class:r,...o}}=n;return _(h,null,[t.prefix&&_(`span`,{class:`v-text-field__prefix`},[t.prefix]),d(_(`textarea`,y({ref:H,class:r,value:M.value,onInput:J,autofocus:t.autofocus,readonly:s.value,disabled:i.value,placeholder:t.placeholder,rows:t.rows,name:t.name,onFocus:W,onBlur:F},o,a),null),[[e,{handler:I},null,{once:!0}]]),t.autoGrow&&d(_(`textarea`,{class:p([r,`v-textarea__sizer`]),id:`${o.id}-sizer`,"onUpdate:modelValue":e=>M.value=e,ref:Y,readonly:!0,"aria-hidden":`true`},null),[[m,M.value]]),t.suffix&&_(`span`,{class:`v-text-field__suffix`},[t.suffix])])}})},details:r?e=>_(h,null,[j.details?.(e),n&&_(h,null,[_(`span`,null,null),v(D,{active:t.persistentCounter||N.value,value:L.value,max:R.value,disabled:t.disabled},j.counter)])]):void 0})}),C({},z,B,H)}});export{M as b};