import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{B as n,C as r,D as i,H as a,I as o,J as s,R as c,T as l,U as u,W as d,Y as f,a3 as p,a5 as m,a9 as h,aB as g,aC as _,aD as v,aE as y,aG as b,aH as x,aI as S,aM as C,aQ as w,aR as T,aS as E,aU as D,aW as ee,aY as O,aZ as k,a_ as te,ac as ne,af as re,am as ie,an as ae,ao as oe,ap as A,as as se,at as ce,au as j,av as le,aw as M,ax as N,b as P,b0 as ue,b1 as de,b7 as F,b8 as fe,bA as pe,bC as me,bD as he,bE as ge,bF as I,bK as L,bL as _e,bM as R,bN as ve,bO as z,bP as ye,bQ as B,bR as V,bS as H,bc as be,bd as U,bf as W,bg as G,bh as K,bi as xe,bj as Se,bk as Ce,bl as q,bm as J,bn as we,bp as Y,bq as X,br as Te,bu as Ee,bx as Z,by as Q,bz as De,d as Oe,s as ke,u as $}from"./index-BSnscBhv.js";import{b as Ae,c as je,d as Me}from"./VCard-DVRc-Pxh.js";import{b as Ne}from"./VChip-CBN0Kf2u.js";import{b as Pe}from"./VSelect-DqM1bu6y.js";import{c as Fe,e as Ie,f as Le}from"./VDialog-VHlGBbps.js";import{b as Re,h as ze,i as Be,m as Ve}from"./VTextField-BU8lnKH2.js";import{c as He}from"./FormModal-CAo97PhI.js";function Ue(e,t,n){return Object.keys(e).filter(e=>ue(e)&&e.endsWith(t)).reduce((r,i)=>(r[i.slice(0,-t.length)]=t=>e[i](t,n(t)),r),{})}function We(){let e=L([]);Ee(()=>e.value=[]);function t(t,n){e.value[n]=t}return{refs:e,updateRef:t}}const Ge=x({activeColor:String,start:{type:[Number,String],default:1},modelValue:{type:Number,default:e=>e.start},disabled:Boolean,length:{type:[Number,String],default:1,validator:e=>e%1==0},totalVisible:[Number,String],firstIcon:{type:A,default:`$first`},prevIcon:{type:A,default:`$prev`},nextIcon:{type:A,default:`$next`},lastIcon:{type:A,default:`$last`},ariaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.root`},pageAriaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.page`},currentPageAriaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.currentPage`},firstAriaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.first`},previousAriaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.previous`},nextAriaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.next`},lastAriaLabel:{type:String,default:`$vuetify.pagination.ariaLabel.last`},ellipsis:{type:String,default:`...`},showFirstLastPage:Boolean,...f(),...b(),...l(),...d(),...p(),...s(),...re({tag:`nav`}),...ae(),...c({variant:`text`})},`VPagination`),Ke=_()({name:`VPagination`,props:Ge(),emits:{"update:modelValue":e=>!0,first:e=>!0,prev:e=>!0,next:e=>!0,last:e=>!0},setup(e,t){let{slots:n,emit:r}=t,i=M(e,`modelValue`),{t:a,n:o}=j(),{isRtl:s}=le(),{themeClasses:c}=oe(e),{width:l}=ce(),u=_e(-1);v(void 0,{scoped:!0});let{resizeRef:d}=ie(e=>{if(!e.length)return;let{target:t,contentRect:n}=e[0],r=t.querySelector(`.v-pagination__list > *`);if(!r)return;let i=n.width,a=r.offsetWidth+parseFloat(getComputedStyle(r).marginRight)*2;u.value=h(i,a)}),f=W(()=>parseInt(e.length,10)),p=W(()=>parseInt(e.start,10)),m=W(()=>e.totalVisible==null?u.value>=0?u.value:h(l.value,58):parseInt(e.totalVisible,10));function h(t,n){let r=e.showFirstLastPage?5:3;return Math.max(0,Math.floor(Number(((t-n*r)/n).toFixed(2))))}let g=W(()=>{if(f.value<=0||isNaN(f.value)||f.value>2**53-1||m.value<=0)return[];if(m.value===1)return[i.value];if(f.value<=m.value)return E(f.value,p.value);let t=m.value%2==0,n=t?m.value/2:Math.floor(m.value/2),r=t?n:n+1,a=f.value-n;if(r-i.value>=0)return[...E(Math.max(1,m.value-1),p.value),e.ellipsis,f.value];if(i.value-a>=(t?1:0)){let t=m.value-1,n=f.value-t+p.value;return[p.value,e.ellipsis,...E(t,n)]}else{let t=Math.max(1,m.value-2),n=t===1?i.value:i.value-Math.ceil(t/2)+p.value;return[p.value,e.ellipsis,...E(t,n),e.ellipsis,f.value]}});function _(e,t,n){e.preventDefault(),i.value=t,n&&r(n,t)}let{refs:y,updateRef:b}=We();v({VPaginationBtn:{color:R(()=>e.color),border:R(()=>e.border),density:R(()=>e.density),size:R(()=>e.size),variant:R(()=>e.variant),rounded:R(()=>e.rounded),elevation:R(()=>e.elevation)}});let x=W(()=>g.value.map((t,n)=>{let r=e=>b(e,n);if(typeof t==`string`)return{isActive:!1,key:`ellipsis-${n}`,page:t,props:{ref:r,ellipsis:!0,icon:!0,disabled:!0}};{let n=t===i.value;return{isActive:n,key:t,page:o(t),props:{ref:r,ellipsis:!1,icon:!0,disabled:!!e.disabled||Number(e.length)<2,color:n?e.activeColor:e.color,"aria-current":n,"aria-label":a(n?e.currentPageAriaLabel:e.pageAriaLabel,t),onClick:e=>_(e,t)}}}})),S=W(()=>{let t=!!e.disabled||i.value<=p.value,n=!!e.disabled||i.value>=p.value+f.value-1;return{first:e.showFirstLastPage?{icon:s.value?e.lastIcon:e.firstIcon,onClick:e=>_(e,p.value,`first`),disabled:t,"aria-label":a(e.firstAriaLabel),"aria-disabled":t}:void 0,prev:{icon:s.value?e.nextIcon:e.prevIcon,onClick:e=>_(e,i.value-1,`prev`),disabled:t,"aria-label":a(e.previousAriaLabel),"aria-disabled":t},next:{icon:s.value?e.prevIcon:e.nextIcon,onClick:e=>_(e,i.value+1,`next`),disabled:n,"aria-label":a(e.nextAriaLabel),"aria-disabled":n},last:e.showFirstLastPage?{icon:s.value?e.firstIcon:e.lastIcon,onClick:e=>_(e,p.value+f.value-1,`last`),disabled:n,"aria-label":a(e.lastAriaLabel),"aria-disabled":n}:void 0}});function C(){let e=i.value-p.value;y.value[e]?.$el.focus()}function w(t){t.key===de.left&&!e.disabled&&i.value>Number(e.start)?(--i.value,Te(C)):t.key===de.right&&!e.disabled&&i.value<p.value+f.value-1&&(i.value+=1,Te(C))}return N(()=>J(e.tag,{ref:d,class:B([`v-pagination`,c.value,e.class]),style:V(e.style),role:`navigation`,"aria-label":a(e.ariaLabel),onKeydown:w,"data-test":`v-pagination-root`},{default:()=>[G(`ul`,{class:`v-pagination__list`},[e.showFirstLastPage&&G(`li`,{key:`first`,class:`v-pagination__first`,"data-test":`v-pagination-first`},[n.first?n.first(S.value.first):J($,X({_as:`VPaginationBtn`},S.value.first),null)]),G(`li`,{key:`prev`,class:`v-pagination__prev`,"data-test":`v-pagination-prev`},[n.prev?n.prev(S.value.prev):J($,X({_as:`VPaginationBtn`},S.value.prev),null)]),x.value.map((e,t)=>G(`li`,{key:e.key,class:B([`v-pagination__item`,{"v-pagination__item--is-active":e.isActive}]),"data-test":`v-pagination-item`},[n.item?n.item(e):J($,X({_as:`VPaginationBtn`},e.props),{default:()=>[e.page]})])),G(`li`,{key:`next`,class:`v-pagination__next`,"data-test":`v-pagination-next`},[n.next?n.next(S.value.next):J($,X({_as:`VPaginationBtn`},S.value.next),null)]),e.showFirstLastPage&&G(`li`,{key:`last`,class:`v-pagination__last`,"data-test":`v-pagination-last`},[n.last?n.last(S.value.last):J($,X({_as:`VPaginationBtn`},S.value.last),null)])])]})),{}}}),qe=x({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},`DataTable-paginate`),Je=Symbol.for(`vuetify:data-table-pagination`);function Ye(e){let t=M(e,`page`,void 0,e=>Number(e??1)),n=M(e,`itemsPerPage`,void 0,e=>Number(e??10));return{page:t,itemsPerPage:n}}function Xe(e){let{page:t,itemsPerPage:n,itemsLength:r}=e,i=W(()=>n.value===-1?0:n.value*(t.value-1)),a=W(()=>n.value===-1?r.value:Math.min(r.value,i.value+n.value)),o=W(()=>n.value===-1||r.value===0?1:Math.ceil(r.value/n.value));he([t,o],()=>{t.value>o.value&&(t.value=o.value)});function s(e){n.value=e,t.value=1}function c(){t.value=w(t.value+1,1,o.value)}function l(){t.value=w(t.value-1,1,o.value)}function u(e){t.value=w(e,1,o.value)}let d={page:t,itemsPerPage:n,startIndex:i,stopIndex:a,pageCount:o,itemsLength:r,nextPage:c,prevPage:l,setPage:u,setItemsPerPage:s};return Q(Je,d),d}function Ze(){let e=Y(Je);if(!e)throw Error(`Missing pagination!`);return e}function Qe(e){let t=y(`usePaginatedItems`),{items:n,startIndex:r,stopIndex:i,itemsPerPage:a}=e,o=W(()=>a.value<=0?n.value:n.value.slice(r.value,i.value));return he(o,e=>{t.emit(`update:currentItems`,e)},{immediate:!0}),{paginatedItems:o}}const $e=x({prevIcon:{type:A,default:`$prev`},nextIcon:{type:A,default:`$next`},firstIcon:{type:A,default:`$first`},lastIcon:{type:A,default:`$last`},itemsPerPageText:{type:String,default:`$vuetify.dataFooter.itemsPerPageText`},pageText:{type:String,default:`$vuetify.dataFooter.pageText`},firstPageLabel:{type:String,default:`$vuetify.dataFooter.firstPage`},prevPageLabel:{type:String,default:`$vuetify.dataFooter.prevPage`},nextPageLabel:{type:String,default:`$vuetify.dataFooter.nextPage`},lastPageLabel:{type:String,default:`$vuetify.dataFooter.lastPage`},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:`10`},{value:25,title:`25`},{value:50,title:`50`},{value:100,title:`100`},{value:-1,title:`$vuetify.dataFooter.itemsPerPageAll`}]},showCurrentPage:Boolean},`VDataTableFooter`),et=_()({name:`VDataTableFooter`,props:$e(),setup(e,t){let{slots:n}=t,{t:r}=j(),{page:i,pageCount:a,startIndex:o,stopIndex:s,itemsLength:c,itemsPerPage:l,setItemsPerPage:u}=Ze(),d=W(()=>e.itemsPerPageOptions.map(e=>typeof e==`number`?{value:e,title:e===-1?r(`$vuetify.dataFooter.itemsPerPageAll`):String(e)}:{...e,title:isNaN(Number(e.title))?r(e.title):e.title}));return N(()=>{let t=Ke.filterProps(e);return G(`div`,{class:`v-data-table-footer`},[n.prepend?.(),G(`div`,{class:`v-data-table-footer__items-per-page`},[G(`span`,{"aria-label":r(e.itemsPerPageText)},[r(e.itemsPerPageText)]),J(Pe,{items:d.value,modelValue:l.value,"onUpdate:modelValue":e=>u(Number(e)),density:`compact`,variant:`outlined`,hideDetails:!0},null)]),G(`div`,{class:`v-data-table-footer__info`},[G(`div`,null,[r(e.pageText,c.value?o.value+1:0,s.value,c.value)])]),G(`div`,{class:`v-data-table-footer__pagination`},[J(Ke,X({modelValue:i.value,"onUpdate:modelValue":e=>i.value=e,density:`comfortable`,firstAriaLabel:e.firstPageLabel,lastAriaLabel:e.lastPageLabel,length:a.value,nextAriaLabel:e.nextPageLabel,previousAriaLabel:e.prevPageLabel,rounded:!0,showFirstLastPage:!0,totalVisible:e.showCurrentPage?1:0,variant:`plain`},t),null)])])}),{}}}),tt=g({align:{type:String,default:`start`},fixed:{type:[Boolean,String],default:!1},fixedOffset:[Number,String],fixedEndOffset:[Number,String],height:[Number,String],lastFixed:Boolean,firstFixedEnd:Boolean,noPadding:Boolean,tag:String,width:[Number,String],maxWidth:[Number,String],nowrap:Boolean},(e,t)=>{let{slots:n}=t,r=e.tag??`td`,i=typeof e.fixed==`string`?e.fixed:e.fixed?`start`:`none`;return J(r,{class:B([`v-data-table__td`,{"v-data-table-column--fixed":i===`start`,"v-data-table-column--fixed-end":i===`end`,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--first-fixed-end":e.firstFixedEnd,"v-data-table-column--no-padding":e.noPadding,"v-data-table-column--nowrap":e.nowrap},`v-data-table-column--align-${e.align}`]),style:{height:T(e.height),width:T(e.width),maxWidth:T(e.maxWidth),left:i===`start`?T(e.fixedOffset||null):void 0,right:i===`end`?T(e.fixedEndOffset||null):void 0}},{default:()=>[n.default?.()]})}),nt=x({headers:Array},`DataTable-header`),rt=Symbol.for(`vuetify:data-table-headers`),it={title:``,sortable:!1},at={...it,width:48};function ot(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=e.map(e=>({element:e,priority:0}));return{enqueue:(e,n)=>{let r=!1;for(let i=0;i<t.length;i++){let a=t[i];if(a.priority>n){t.splice(i,0,{element:e,priority:n}),r=!0;break}}r||t.push({element:e,priority:n})},size:()=>t.length,count:()=>{let e=0;if(!t.length)return 0;let n=Math.floor(t[0].priority);for(let r=0;r<t.length;r++)Math.floor(t[r].priority)===n&&(e+=1);return e},dequeue:()=>t.shift()}}function st(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];if(!e.children)t.push(e);else for(let n of e.children)st(n,t);return t}function ct(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new Set;for(let n of e)n.key&&t.add(n.key),n.children&&ct(n.children,t);return t}function lt(e){if(e.key){if(e.key===`data-table-group`)return it;if([`data-table-expand`,`data-table-select`].includes(e.key))return at}}function ut(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.children?Math.max(t,...e.children.map(e=>ut(e,t+1))):t}function dt(e){let t=!1;function n(e,r){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:`none`;if(!e)return;i!==`none`&&(e.fixed=i),e.fixed===!0&&(e.fixed=`start`);let a=r===`start`?e.children?.toReversed():e.children;if(e.fixed===r)if(a)for(let e of a)n(e,r,r);else !t&&r===`start`?e.lastFixed=!0:!t&&r===`end`?e.firstFixedEnd=!0:isNaN(Number(e.width))?S(`Multiple fixed columns should have a static width (key: ${e.key})`):e.minWidth=Math.max(Number(e.width)||0,Number(e.minWidth)||0),t=!0;else if(a)for(let e of a)n(e,r);else t=!1}for(let t of e.toReversed())n(t,`start`);for(let t of e)n(t,`end`);function r(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!e)return t;if(e.children){e.fixedOffset=t;for(let n of e.children)t=r(n,t)}else e.fixed&&e.fixed!==`end`&&(e.fixedOffset=t,t+=parseFloat(e.width||`0`)||0);return t}let i=0;for(let t of e)i=r(t,i);function a(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!e)return t;if(e.children){e.fixedEndOffset=t;for(let n of e.children)t=a(n,t)}else e.fixed===`end`&&(e.fixedEndOffset=t,t+=parseFloat(e.width||`0`)||0);return t}let o=0;for(let t of e.toReversed())o=a(t,o)}function ft(e,t){let n=[],r=0,i=ot(e);for(;i.size()>0;){let e=i.count(),a=[],o=1;for(;e>0;){let{element:n,priority:s}=i.dequeue(),c=t-r-ut(n);if(a.push({...n,rowspan:c??1,colspan:n.children?st(n).length:1}),n.children)for(let e of n.children){let t=s%1+o/10**(r+2);i.enqueue(e,r+c+t)}o+=1,--e}r+=1,n.push(a)}let a=e.map(e=>st(e)).flat();return{columns:a,headers:n}}function pt(e){let t=[];for(let n of e){let e={...lt(n),...n},r=e.key??(typeof e.value==`string`?e.value:null),i=e.value??r??null,a={...e,key:r,value:i,sortable:e.sortable??(e.key!=null||!!e.sort),children:e.children?pt(e.children):void 0};t.push(a)}return t}function mt(e,t){let n=L([]),r=L([]),i=L({}),a=L({}),o=L({});ge(()=>{let s=e.headers||Object.keys(e.items[0]??{}).map(e=>({key:e,title:ye(e)})),c=s.slice(),l=ct(c);t?.groupBy?.value.length&&!l.has(`data-table-group`)&&c.unshift({key:`data-table-group`,title:`Group`}),t?.showSelect?.value&&!l.has(`data-table-select`)&&c.unshift({key:`data-table-select`}),t?.showExpand?.value&&!l.has(`data-table-expand`)&&c.push({key:`data-table-expand`});let u=pt(c);dt(u);let d=Math.max(...u.map(e=>ut(e)))+1,f=ft(u,d);n.value=f.headers,r.value=f.columns;let p=f.headers.flat(1);for(let e of p){if(!e.key)continue;e.sortable&&(e.sort&&(i.value[e.key]=e.sort),e.sortRaw&&(a.value[e.key]=e.sortRaw)),e.filter&&(o.value[e.key]=e.filter)}});let s={headers:n,columns:r,sortFunctions:i,sortRawFunctions:a,filterFunctions:o};return Q(rt,s),s}function ht(){let e=Y(rt);if(!e)throw Error(`Missing headers!`);return e}const gt={showSelectAll:!1,allSelected:()=>[],select:e=>{let{items:t,value:n}=e;return new Set(n?[t[0]?.value]:[])},selectAll:e=>{let{selected:t}=e;return t}},_t={showSelectAll:!0,allSelected:e=>{let{currentPage:t}=e;return t},select:e=>{let{items:t,value:n,selected:r}=e;for(let e of t)n?r.add(e.value):r.delete(e.value);return r},selectAll:e=>{let{value:t,currentPage:n,selected:r}=e;return _t.select({items:n,value:t,selected:r})}},vt={showSelectAll:!0,allSelected:e=>{let{allItems:t}=e;return t},select:e=>{let{items:t,value:n,selected:r}=e;for(let e of t)n?r.add(e.value):r.delete(e.value);return r},selectAll:e=>{let{value:t,allItems:n,selected:r}=e;return vt.select({items:n,value:t,selected:r})}},yt=x({showSelect:Boolean,selectStrategy:{type:[String,Object],default:`page`},modelValue:{type:Array,default:()=>[]},valueComparator:{type:Function,default:D}},`DataTable-select`),bt=Symbol.for(`vuetify:data-table-selection`);function xt(e,t){let{allItems:n,currentPage:r}=t,i=M(e,`modelValue`,e.modelValue,t=>new Set(F(t).map(t=>n.value.find(n=>e.valueComparator(t,n.value))?.value??t)),e=>[...e.values()]),a=W(()=>n.value.filter(e=>e.selectable)),o=W(()=>r.value.filter(e=>e.selectable)),s=W(()=>{if(typeof e.selectStrategy==`object`)return e.selectStrategy;switch(e.selectStrategy){case`single`:return gt;case`all`:return vt;case`page`:default:return _t}}),c=_e(null);function l(e){return F(e).every(e=>i.value.has(e.value))}function u(e){return F(e).some(e=>i.value.has(e.value))}function d(e,t){let n=s.value.select({items:e,value:t,selected:new Set(i.value)});i.value=n}function f(t,n,i){let a=[];if(n??=r.value.findIndex(e=>e.value===t.value),e.selectStrategy!==`single`&&i?.shiftKey&&c.value!==null){let[e,t]=[c.value,n].sort((e,t)=>e-t);a.push(...r.value.slice(e,t+1).filter(e=>e.selectable))}else a.push(t),c.value=n;d(a,!l([t]))}function p(e){let t=s.value.selectAll({value:e,allItems:a.value,currentPage:o.value,selected:new Set(i.value)});i.value=t}let m=W(()=>i.value.size>0),h=W(()=>{let e=s.value.allSelected({allItems:a.value,currentPage:o.value});return!!e.length&&l(e)}),g=R(()=>s.value.showSelectAll),_={toggleSelect:f,select:d,selectAll:p,isSelected:l,isSomeSelected:u,someSelected:m,allSelected:h,showSelectAll:g,lastSelectedIndex:c,selectStrategy:s};return Q(bt,_),_}function St(){let e=Y(bt);if(!e)throw Error(`Missing selection!`);return e}const Ct=x({sortBy:{type:Array,default:()=>[]},customKeySort:Object,multiSort:Boolean,mustSort:Boolean},`DataTable-sort`),wt=Symbol.for(`vuetify:data-table-sort`);function Tt(e){let t=M(e,`sortBy`),n=R(()=>e.mustSort),r=R(()=>e.multiSort);return{sortBy:t,mustSort:n,multiSort:r}}function Et(e){let{sortBy:t,mustSort:n,multiSort:r,page:i}=e,a=e=>{if(e.key==null)return;let a=t.value.map(e=>({...e}))??[],o=a.find(t=>t.key===e.key);o?o.order===`desc`?n.value&&a.length===1?o.order=`asc`:a=a.filter(t=>t.key!==e.key):o.order=`desc`:r.value?a.push({key:e.key,order:`asc`}):a=[{key:e.key,order:`asc`}],t.value=a,i&&(i.value=1)};function o(e){return!!t.value.find(t=>t.key===e.key)}let s={sortBy:t,toggleSort:a,isSorted:o};return Q(wt,s),s}function Dt(){let e=Y(wt);if(!e)throw Error(`Missing sort!`);return e}function Ot(e,t,n,r){let i=j(),a=W(()=>n.value.length?kt(t.value,n.value,i.current.value,{transform:r?.transform,sortFunctions:{...e.customKeySort,...r?.sortFunctions?.value},sortRawFunctions:r?.sortRawFunctions?.value}):t.value);return{sortedItems:a}}function kt(e,t,n,r){let i=new Intl.Collator(n,{sensitivity:`accent`,usage:`sort`}),a=e.map(e=>[e,r?.transform?r.transform(e):e]);return a.sort((e,n)=>{for(let a=0;a<t.length;a++){let o=!1,s=t[a].key,c=t[a].order??`asc`;if(c===!1)continue;let l=O(e[1],s),u=O(n[1],s),d=e[0].raw,f=n[0].raw;if(c===`desc`&&([l,u]=[u,l],[d,f]=[f,d]),r?.sortRawFunctions?.[s]){let e=r.sortRawFunctions[s](d,f);if(e==null)continue;if(o=!0,e)return e}if(r?.sortFunctions?.[s]){let e=r.sortFunctions[s](l,u);if(e==null)continue;if(o=!0,e)return e}if(!o&&(l instanceof Date&&u instanceof Date&&(l=l.getTime(),u=u.getTime()),[l,u]=[l,u].map(e=>e==null?e:e.toString().toLocaleLowerCase()),l!==u))return te(l)&&te(u)?0:te(l)?-1:te(u)?1:!isNaN(l)&&!isNaN(u)?Number(l)-Number(u):i.compare(l,u)}return 0}).map(e=>{let[t]=e;return t})}const At=x({color:String,disableSort:Boolean,fixedHeader:Boolean,multiSort:Boolean,sortAscIcon:{type:A,default:`$sortAsc`},sortDescIcon:{type:A,default:`$sortDesc`},headerProps:{type:Object},sticky:Boolean,...se(),...r()},`VDataTableHeaders`),jt=_()({name:`VDataTableHeaders`,props:At(),setup(e,t){let{slots:r}=t,{t:a}=j(),{toggleSort:s,sortBy:c,isSorted:l}=Dt(),{someSelected:u,allSelected:d,selectAll:f,showSelectAll:p}=St(),{columns:h,headers:g}=ht(),{loaderClasses:_}=i(e);function v(t,n){if(!(e.sticky||e.fixedHeader)&&!t.fixed)return;let r=typeof t.fixed==`string`?t.fixed:t.fixed?`start`:`none`;return{position:`sticky`,left:r===`start`?T(t.fixedOffset):void 0,right:r===`end`?T(t.fixedEndOffset):void 0,top:e.sticky||e.fixedHeader?`calc(var(--v-table-header-height) * ${n})`:void 0}}function y(t,n){t.key===`Enter`&&!e.disableSort&&s(n)}function b(t){let n=c.value.find(e=>e.key===t.key);return n?n.order===`asc`?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}let{backgroundColorClasses:x,backgroundColorStyles:S}=m(()=>e.color),{displayClasses:C,mobile:w}=ce(e),E=W(()=>({headers:g.value,columns:h.value,toggleSort:s,isSorted:l,sortBy:c.value,someSelected:u.value,allSelected:d.value,selectAll:f,getSortIcon:b})),D=W(()=>[`v-data-table__th`,{"v-data-table__th--sticky":e.sticky||e.fixedHeader},C.value,_.value]),ee=t=>{let{column:n,x:i,y:a}=t,m=n.key===`data-table-select`||n.key===`data-table-expand`,h=X(e.headerProps??{},n.headerProps??{});return J(tt,X({tag:`th`,align:n.align,class:[{"v-data-table__th--sortable":n.sortable&&!e.disableSort,"v-data-table__th--sorted":l(n),"v-data-table__th--fixed":n.fixed},...D.value],style:{width:T(n.width),minWidth:T(n.minWidth),maxWidth:T(n.maxWidth),...v(n,a)},colspan:n.colspan,rowspan:n.rowspan,fixed:n.fixed,nowrap:n.nowrap,lastFixed:n.lastFixed,firstFixedEnd:n.firstFixedEnd,noPadding:m,tabindex:n.sortable?0:void 0,onClick:n.sortable?()=>s(n):void 0,onKeydown:n.sortable?e=>y(e,n):void 0},h),{default:()=>{let t=`header.${n.key}`,i={column:n,selectAll:f,isSorted:l,toggleSort:s,sortBy:c.value,someSelected:u.value,allSelected:d.value,getSortIcon:b};return r[t]?r[t](i):n.key===`data-table-select`?r[`header.data-table-select`]?.(i)??(p.value&&J(Fe,{modelValue:d.value,indeterminate:u.value&&!d.value,"onUpdate:modelValue":f},null)):G(`div`,{class:`v-data-table-header__content`},[G(`span`,null,[n.title]),n.sortable&&!e.disableSort&&J(o,{key:`icon`,class:`v-data-table-header__sort-icon`,icon:b(n)},null),e.multiSort&&l(n)&&G(`div`,{key:`badge`,class:B([`v-data-table-header__sort-badge`,...x.value]),style:V(S.value)},[c.value.findIndex(e=>e.key===n.key)+1])])}})},O=()=>{let t=W(()=>h.value.filter(t=>t?.sortable&&!e.disableSort)),n=W(()=>{let e=h.value.find(e=>e.key===`data-table-select`);if(e!=null)return d.value?`$checkboxOn`:u.value?`$checkboxIndeterminate`:`$checkboxOff`});return J(tt,X({tag:`th`,class:[...D.value],colspan:g.value.length+1},e.headerProps),{default:()=>[G(`div`,{class:`v-data-table-header__content`},[J(Pe,{chips:!0,class:`v-data-table__td-sort-select`,clearable:!0,density:`default`,items:t.value,label:a(`$vuetify.dataTable.sortBy`),multiple:e.multiSort,variant:`underlined`,"onClick:clear":()=>c.value=[],appendIcon:n.value,"onClick:append":()=>f(!d.value)},{...r,chip:e=>J(Ne,{onClick:e.item.raw?.sortable?()=>s(e.item.raw):void 0,onMousedown:e=>{e.preventDefault(),e.stopPropagation()}},{default:()=>[e.item.title,J(o,{class:B([`v-data-table__td-sort-icon`,l(e.item.raw)&&`v-data-table__td-sort-icon-active`]),icon:b(e.item.raw),size:`small`},null)]})})])]})};N(()=>w.value?G(`tr`,null,[J(O,null,null)]):G(U,null,[r.headers?r.headers(E.value):g.value.map((e,t)=>G(`tr`,null,[e.map((e,n)=>J(ee,{column:e,x:n,y:t},null))])),e.loading&&G(`tr`,{class:`v-data-table-progress`},[G(`th`,{colspan:h.value.length},[J(n,{name:`v-data-table-progress`,absolute:!0,active:!0,color:typeof e.loading==`boolean`?void 0:e.loading,indeterminate:!0},{default:r.loader})])])]))}}),Mt=x({groupBy:{type:Array,default:()=>[]}},`DataTable-group`),Nt=Symbol.for(`vuetify:data-table-group`);function Pt(e){let t=M(e,`groupBy`);return{groupBy:t}}function Ft(e){let{disableSort:t,groupBy:n,sortBy:r}=e,i=L(new Set),a=W(()=>n.value.map(e=>({...e,order:e.order??!1})).concat(t?.value?[]:r.value));function o(e){return i.value.has(e.id)}function s(e){let t=new Set(i.value);o(e)?t.delete(e.id):t.add(e.id),i.value=t}function c(e){function t(e){let n=[];for(let r of e.items)`type`in r&&r.type===`group`?n.push(...t(r)):n.push(r);return[...new Set(n)]}return t({type:`group`,items:e,id:`dummy`,key:`dummy`,value:`dummy`,depth:0})}let l={sortByWithGroups:a,toggleGroup:s,opened:i,groupBy:n,extractRows:c,isGroupOpen:o};return Q(Nt,l),l}function It(){let e=Y(Nt);if(!e)throw Error(`Missing group!`);return e}function Lt(e,t){if(!e.length)return[];let n=new Map;for(let r of e){let e=O(r.raw,t);n.has(e)||n.set(e,[]),n.get(e).push(r)}return n}function Rt(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:`root`;if(!t.length)return[];let i=Lt(e,t[0]),a=[],o=t.slice(1);return i.forEach((e,i)=>{let s=t[0],c=`${r}_${s}_${i}`;a.push({depth:n,id:c,key:s,value:i,items:o.length?Rt(e,o,n+1,c):e,type:`group`})}),a}function zt(e,t){let n=[];for(let r of e)`type`in r&&r.type===`group`?(r.value!=null&&n.push(r),(t.has(r.id)||r.value==null)&&n.push(...zt(r.items,t))):n.push(r);return n}function Bt(e,t,n){let r=W(()=>{if(!t.value.length)return e.value;let r=Rt(e.value,t.value.map(e=>e.key));return zt(r,n.value)});return{flatItems:r}}const Vt=x({item:{type:Object,required:!0}},`VDataTableGroupHeaderRow`),Ht=_()({name:`VDataTableGroupHeaderRow`,props:Vt(),setup(e,t){let{slots:n}=t,{isGroupOpen:r,toggleGroup:i,extractRows:a}=It(),{isSelected:o,isSomeSelected:s,select:c}=St(),{columns:l}=ht(),u=W(()=>a([e.item]));return()=>G(`tr`,{class:`v-data-table-group-header-row`,style:{"--v-data-table-group-header-row-depth":e.item.depth}},[l.value.map(t=>{if(t.key===`data-table-group`){let t=r(e.item)?`$expand`:`$next`,a=()=>i(e.item);return n[`data-table-group`]?.({item:e.item,count:u.value.length,props:{icon:t,onClick:a}})??J(tt,{class:`v-data-table-group-header-row__column`},{default:()=>[J($,{size:`small`,variant:`text`,icon:t,onClick:a},null),G(`span`,null,[e.item.value]),G(`span`,null,[q(`(`),u.value.length,q(`)`)])]})}if(t.key===`data-table-select`){let e=o(u.value),t=s(u.value)&&!e,r=e=>c(u.value,e);return n[`data-table-select`]?.({props:{modelValue:e,indeterminate:t,"onUpdate:modelValue":r}})??G(`td`,null,[J(Fe,{modelValue:e,indeterminate:t,"onUpdate:modelValue":r},null)])}return G(`td`,null,null)})])}}),Ut=x({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},`DataTable-expand`),Wt=Symbol.for(`vuetify:datatable:expanded`);function Gt(e){let t=R(()=>e.expandOnClick),n=M(e,`expanded`,e.expanded,e=>new Set(e),e=>[...e.values()]);function r(e,t){let r=new Set(n.value);t?r.add(e.value):r.delete(e.value),n.value=r}function i(e){return n.value.has(e.value)}function a(e){r(e,!i(e))}let o={expand:r,expanded:n,expandOnClick:t,isExpanded:i,toggleExpand:a};return Q(Wt,o),o}function Kt(){let e=Y(Wt);if(!e)throw Error(`foo`);return e}const qt=x({index:Number,item:Object,cellProps:[Object,Function],onClick:C(),onContextmenu:C(),onDblclick:C(),...se()},`VDataTableRow`),Jt=_()({name:`VDataTableRow`,props:qt(),setup(e,t){let{slots:n}=t,{displayClasses:r,mobile:i}=ce(e,`v-data-table__tr`),{isSelected:a,toggleSelect:o,someSelected:s,allSelected:c,selectAll:l}=St(),{isExpanded:u,toggleExpand:d}=Kt(),{toggleSort:f,sortBy:p,isSorted:m}=Dt(),{columns:h}=ht();N(()=>G(`tr`,{class:B([`v-data-table__tr`,{"v-data-table__tr--clickable":!!(e.onClick||e.onContextmenu||e.onDblclick)},r.value]),onClick:e.onClick,onContextmenu:e.onContextmenu,onDblclick:e.onDblclick},[e.item&&h.value.map((t,r)=>{let h=e.item,g=`item.${t.key}`,_=`header.${t.key}`,v={index:e.index,item:h.raw,internalItem:h,value:O(h.columns,t.key),column:t,isSelected:a,toggleSelect:o,isExpanded:u,toggleExpand:d},y={column:t,selectAll:l,isSorted:m,toggleSort:f,sortBy:p.value,someSelected:s.value,allSelected:c.value,getSortIcon:()=>``},b=typeof e.cellProps==`function`?e.cellProps({index:v.index,item:v.item,internalItem:v.internalItem,value:v.value,column:t}):e.cellProps,x=typeof t.cellProps==`function`?t.cellProps({index:v.index,item:v.item,internalItem:v.internalItem,value:v.value}):t.cellProps;return J(tt,X({align:t.align,class:{"v-data-table__td--expanded-row":t.key===`data-table-expand`,"v-data-table__td--select-row":t.key===`data-table-select`},fixed:t.fixed,fixedOffset:t.fixedOffset,fixedEndOffset:t.fixedEndOffset,lastFixed:t.lastFixed,firstFixedEnd:t.firstFixedEnd,maxWidth:i.value?void 0:t.maxWidth,noPadding:t.key===`data-table-select`||t.key===`data-table-expand`,nowrap:t.nowrap,width:i.value?void 0:t.width},b,x),{default:()=>{if(t.key===`data-table-select`)return n[`item.data-table-select`]?.({...v,props:{disabled:!h.selectable,modelValue:a([h]),onClick:be(()=>o(h),[`stop`])}})??J(Fe,{disabled:!h.selectable,modelValue:a([h]),onClick:be(t=>o(h,e.index,t),[`stop`])},null);if(t.key===`data-table-expand`)return n[`item.data-table-expand`]?.({...v,props:{icon:u(h)?`$collapse`:`$expand`,size:`small`,variant:`text`,onClick:be(()=>d(h),[`stop`])}})??J($,{icon:u(h)?`$collapse`:`$expand`,size:`small`,variant:`text`,onClick:be(()=>d(h),[`stop`])},null);if(n[g]&&!i.value)return n[g](v);let r=H(v.value);return i.value?G(U,null,[G(`div`,{class:`v-data-table__td-title`},[n[_]?.(y)??t.title]),G(`div`,{class:`v-data-table__td-value`},[n[g]?.(v)??r])]):r}})})]))}}),Yt=x({loading:[Boolean,String],loadingText:{type:String,default:`$vuetify.dataIterator.loadingText`},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:`$vuetify.noDataText`},rowProps:[Object,Function],cellProps:[Object,Function],...se()},`VDataTableRows`),Xt=_()({name:`VDataTableRows`,inheritAttrs:!1,props:Yt(),setup(e,t){let{attrs:n,slots:r}=t,{columns:i}=ht(),{expandOnClick:a,toggleExpand:o,isExpanded:s}=Kt(),{isSelected:c,toggleSelect:l}=St(),{toggleGroup:u,isGroupOpen:d}=It(),{t:f}=j(),{mobile:p}=ce(e);return N(()=>e.loading&&(!e.items.length||r.loading)?G(`tr`,{class:`v-data-table-rows-loading`,key:`loading`},[G(`td`,{colspan:i.value.length},[r.loading?.()??f(e.loadingText)])]):!e.loading&&!e.items.length&&!e.hideNoData?G(`tr`,{class:`v-data-table-rows-no-data`,key:`no-data`},[G(`td`,{colspan:i.value.length},[r[`no-data`]?.()??f(e.noDataText)])]):G(U,null,[e.items.map((t,f)=>{if(t.type===`group`){let e={index:f,item:t,columns:i.value,isExpanded:s,toggleExpand:o,isSelected:c,toggleSelect:l,toggleGroup:u,isGroupOpen:d};return r[`group-header`]?r[`group-header`](e):J(Ht,X({key:`group-header_${t.id}`,item:t},Ue(n,`:group-header`,()=>e)),r)}let m={index:f,item:t.raw,internalItem:t,columns:i.value,isExpanded:s,toggleExpand:o,isSelected:c,toggleSelect:l},h={...m,props:X({key:`item_${t.key??t.index}`,onClick:a.value?()=>{o(t)}:void 0,index:f,item:t,cellProps:e.cellProps,mobile:p.value},Ue(n,`:row`,()=>m),typeof e.rowProps==`function`?e.rowProps({item:m.item,index:m.index,internalItem:m.internalItem}):e.rowProps)};return G(U,{key:h.props.key},[r.item?r.item(h):J(Jt,h.props,r),s(t)&&r[`expanded-row`]?.(m)])})])),{}}}),Zt=x({fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,striped:{type:String,default:null,validator:e=>[`even`,`odd`].includes(e)},...b(),...l(),...re(),...ae()},`VTable`),Qt=_()({name:`VTable`,props:Zt(),setup(e,t){let{slots:n,emit:r}=t,{themeClasses:i}=oe(e),{densityClasses:a}=u(e);return N(()=>J(e.tag,{class:B([`v-table`,{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!n.top,"v-table--has-bottom":!!n.bottom,"v-table--hover":e.hover,"v-table--striped-even":e.striped===`even`,"v-table--striped-odd":e.striped===`odd`},i.value,a.value,e.class]),style:V(e.style)},{default:()=>[n.top?.(),n.default?G(`div`,{class:`v-table__wrapper`,style:{height:T(e.height)}},[G(`table`,null,[n.default()])]):n.wrapper?.(),n.bottom?.()]})),{}}}),$t=x({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:`id`},itemSelectable:{type:[String,Array,Function],default:null},rowProps:[Object,Function],cellProps:[Object,Function],returnObject:Boolean},`DataTable-items`);function en(e,t,n,r){let i=e.returnObject?t:k(t,e.itemValue),a=k(t,e.itemSelectable,!0),o=r.reduce((e,n)=>(n.key!=null&&(e[n.key]=k(t,n.value)),e),{});return{type:`item`,key:e.returnObject?k(t,e.itemValue):i,index:n,value:i,selectable:a,columns:o,raw:t}}function tn(e,t,n){return t.map((t,r)=>en(e,t,r,n))}function nn(e,t){let n=W(()=>tn(e,e.items,t.value));return{items:n}}function rn(e){let{page:t,itemsPerPage:n,sortBy:r,groupBy:i,search:a}=e,o=y(`VDataTable`),s=()=>({page:t.value,itemsPerPage:n.value,sortBy:r.value,groupBy:i.value,search:a.value}),c=null;he(s,e=>{D(c,e)||(c&&c.search!==e.search&&(t.value=1),o.emit(`update:options`,e),c=e)},{deep:!0,immediate:!0})}const an=(e,t,n)=>{if(e==null||t==null)return-1;if(!t.length)return 0;e=e.toString().toLocaleLowerCase(),t=t.toString().toLocaleLowerCase();let r=[],i=e.indexOf(t);for(;~i;)r.push([i,i+t.length]),i=e.indexOf(t,i+t.length);return r.length?r:-1};function on(e,t){if(!(e==null||typeof e==`boolean`||e===-1))return typeof e==`number`?[[e,e+t.length]]:Array.isArray(e[0])?e:[e]}const sn=x({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:`intersection`},noFilter:Boolean},`filter`);function cn(e,t,n){let r=[],i=n?.default??an,a=n?.filterKeys?F(n.filterKeys):!1,o=Object.keys(n?.customKeyFilter??{}).length;if(!e?.length)return r;loop:for(let s=0;s<e.length;s++){let[c,l=c]=F(e[s]),u={},d={},f=-1;if((t||o>0)&&!n?.noFilter){if(typeof c==`object`){if(c.type===`divider`||c.type===`subheader`)continue;let e=a||Object.keys(l);for(let r of e){let e=k(l,r),a=n?.customKeyFilter?.[r];if(f=a?a(e,t,c):i(e,t,c),f!==-1&&f!==!1)a?u[r]=on(f,t):d[r]=on(f,t);else if(n?.filterMode===`every`)continue loop}}else f=i(c,t,c),f!==-1&&f!==!1&&(d.title=on(f,t));let e=Object.keys(d).length,r=Object.keys(u).length;if(!e&&!r||n?.filterMode===`union`&&r!==o&&!e||n?.filterMode===`intersection`&&(r!==o||!e))continue}r.push({index:s,matches:{...d,...u}})}return r}function ln(e,t,n,r){let i=_e([]),a=_e(new Map),o=W(()=>r?.transform?z(t).map(e=>[e,r.transform(e)]):z(t));ge(()=>{let s=typeof n==`function`?n():z(n),c=typeof s!=`string`&&typeof s!=`number`?``:String(s),l=cn(o.value,c,{customKeyFilter:{...e.customKeyFilter,...z(r?.customKeyFilter)},default:e.customFilter,filterKeys:e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}),u=z(t),d=[],f=new Map;l.forEach(e=>{let{index:t,matches:n}=e,r=u[t];d.push(r),f.set(r.value,n)}),i.value=d,a.value=f});function s(e){return a.value.get(e.value)}return{filteredItems:i,filteredMatches:a,getMatches:s}}const un=x({...Yt(),hideDefaultBody:Boolean,hideDefaultFooter:Boolean,hideDefaultHeader:Boolean,width:[String,Number],search:String,...Ut(),...Mt(),...nt(),...$t(),...yt(),...Ct(),...At(),...Zt()},`DataTable`),dn=x({...qe(),...un(),...sn(),...$e()},`VDataTable`),fn=_()({name:`VDataTable`,props:dn(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,t){let{attrs:n,slots:r}=t,{groupBy:i}=Pt(e),{sortBy:a,multiSort:o,mustSort:s}=Tt(e),{page:c,itemsPerPage:l}=Ye(e),{disableSort:u}=ve(e),{columns:d,headers:f,sortFunctions:p,sortRawFunctions:m,filterFunctions:h}=mt(e,{groupBy:i,showSelect:R(()=>e.showSelect),showExpand:R(()=>e.showExpand)}),{items:g}=nn(e,d),_=R(()=>e.search),{filteredItems:y}=ln(e,g,_,{transform:e=>e.columns,customKeyFilter:h}),{toggleSort:b}=Et({sortBy:a,multiSort:o,mustSort:s,page:c}),{sortByWithGroups:x,opened:S,extractRows:C,isGroupOpen:w,toggleGroup:T}=Ft({groupBy:i,sortBy:a,disableSort:u}),{sortedItems:E}=Ot(e,y,x,{transform:e=>({...e.raw,...e.columns}),sortFunctions:p,sortRawFunctions:m}),{flatItems:D}=Bt(E,i,S),ee=W(()=>D.value.length),{startIndex:O,stopIndex:k,pageCount:te,setItemsPerPage:ne}=Xe({page:c,itemsPerPage:l,itemsLength:ee}),{paginatedItems:re}=Qe({items:D,startIndex:O,stopIndex:k,itemsPerPage:l}),ie=W(()=>C(re.value)),{isSelected:ae,select:oe,selectAll:A,toggleSelect:se,someSelected:ce,allSelected:j}=xt(e,{allItems:g,currentPage:ie}),{isExpanded:le,toggleExpand:M}=Gt(e);rn({page:c,itemsPerPage:l,sortBy:a,groupBy:i,search:_}),v({VDataTableRows:{hideNoData:R(()=>e.hideNoData),noDataText:R(()=>e.noDataText),loading:R(()=>e.loading),loadingText:R(()=>e.loadingText)}});let P=W(()=>({page:c.value,itemsPerPage:l.value,sortBy:a.value,pageCount:te.value,toggleSort:b,setItemsPerPage:ne,someSelected:ce.value,allSelected:j.value,isSelected:ae,select:oe,selectAll:A,toggleSelect:se,isExpanded:le,toggleExpand:M,isGroupOpen:w,toggleGroup:T,items:ie.value.map(e=>e.raw),internalItems:ie.value,groupedItems:re.value,columns:d.value,headers:f.value}));return N(()=>{let t=et.filterProps(e),i=jt.filterProps(e),a=Xt.filterProps(e),o=Qt.filterProps(e);return J(Qt,X({class:[`v-data-table`,{"v-data-table--show-select":e.showSelect,"v-data-table--loading":e.loading},e.class],style:e.style},o,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>r.top?.(P.value),default:()=>r.default?r.default(P.value):G(U,null,[r.colgroup?.(P.value),!e.hideDefaultHeader&&G(`thead`,{key:`thead`},[J(jt,i,r)]),r.thead?.(P.value),!e.hideDefaultBody&&G(`tbody`,null,[r[`body.prepend`]?.(P.value),r.body?r.body(P.value):J(Xt,X(n,a,{items:re.value}),r),r[`body.append`]?.(P.value)]),r.tbody?.(P.value),r.tfoot?.(P.value)]),bottom:()=>r.bottom?r.bottom(P.value):!e.hideDefaultFooter&&G(U,null,[J(ke,null,null),J(et,t,{prepend:r[`footer.prepend`]})])})}),{}}}),pn={class:`d-flex align-center`},mn={class:`text-h6 font-weight-bold`},hn={class:`d-flex align-center gap-2`},gn={key:0},_n={key:2},vn={key:4},yn={class:`d-flex align-center gap-1`},bn={class:`text-center py-8`},xn={class:`text-h6 text-medium-emphasis mt-4`},Sn={class:`text-center py-8`};var Cn=we({__name:`DataTable`,props:{title:{},icon:{},itemName:{},headers:{},items:{},loading:{type:Boolean,default:!1},totalItems:{default:0},itemsPerPage:{default:20},showAddButton:{type:Boolean,default:!0},filters:{default:()=>[]},noDataText:{default:`No data available`},noDataIcon:{default:`mdi-database-off`}},emits:[`add`,`edit`,`delete`,`view`,`refresh`,`search`,`filter`,`update:options`],setup(n,{emit:r}){let i=n,s=r,c=L(``),l=L({}),u=L(1),d=L([]);W(()=>[...i.headers,{key:`actions`,title:`Actions`,sortable:!1,width:`120px`,align:`center`}]);let f=()=>{s(`search`,c.value)},p=()=>{s(`filter`,l.value)},m=e=>{u.value=e.page,d.value=e.sortBy,s(`update:options`,e)},h=e=>e?new Date(e).toLocaleDateString(`id-ID`,{year:`numeric`,month:`short`,day:`numeric`}):`N/A`,g=e=>{let t={active:`success`,inactive:`error`,pending:`warning`,approved:`success`,rejected:`error`,draft:`info`};return t[e?.toLowerCase()]||`primary`};return he(()=>i.items,()=>{u.value=1}),(n,r)=>(Z(),K(Ae,null,{default:I(()=>[J(Me,{class:`d-flex align-center justify-space-between`},{default:I(()=>[G(`div`,pn,[n.icon?(Z(),K(o,{key:0,class:`mr-2`},{default:I(()=>[q(H(n.icon),1)]),_:1})):xe(``,!0),G(`span`,mn,H(n.title),1)]),G(`div`,hn,[J(Re,{modelValue:c.value,"onUpdate:modelValue":r[0]||=e=>c.value=e,"prepend-inner-icon":`mdi-magnify`,label:`Search...`,variant:`outlined`,density:`compact`,"hide-details":``,clearable:``,style:{"min-width":`250px`},onInput:f},null,8,[`modelValue`]),n.showAddButton?(Z(),K($,{key:0,color:`primary`,onClick:r[1]||=e=>n.$emit(`add`)},{default:I(()=>[J(o,{start:``},{default:I(()=>r[4]||=[q(`mdi-plus`,-1)]),_:1,__:[4]}),q(` Add `+H(n.itemName),1)]),_:1})):xe(``,!0),J($,{icon:``,variant:`text`,onClick:r[2]||=e=>n.$emit(`refresh`),loading:n.loading},{default:I(()=>[J(o,null,{default:I(()=>r[5]||=[q(`mdi-refresh`,-1)]),_:1,__:[5]})]),_:1},8,[`loading`])])]),_:1}),J(ke),n.filters.length>0?(Z(),K(je,{key:0,class:`pb-0`},{default:I(()=>[J(e,null,{default:I(()=>[(Z(!0),Se(U,null,De(n.filters,e=>(Z(),K(t,{key:e.key,cols:`12`,sm:`6`,md:`3`},{default:I(()=>[J(Pe,{modelValue:l.value[e.key],"onUpdate:modelValue":[t=>l.value[e.key]=t,p],items:e.options,label:e.label,variant:`outlined`,density:`compact`,clearable:``},null,8,[`modelValue`,`onUpdate:modelValue`,`items`,`label`])]),_:2},1024))),128))]),_:1})]),_:1})):xe(``,!0),J(fn,{headers:n.headers,items:n.items,loading:n.loading,"items-per-page":n.itemsPerPage,"server-items-length":n.totalItems,class:`elevation-0`,"onUpdate:options":m},Ce({"item.actions":I(({item:e})=>[G(`div`,yn,[J($,{icon:``,size:`small`,variant:`text`,onClick:t=>n.$emit(`view`,e)},{default:I(()=>[J(o,{size:`small`},{default:I(()=>r[6]||=[q(`mdi-eye`,-1)]),_:1,__:[6]}),J(He,{activator:`parent`},{default:I(()=>r[7]||=[q(`View`,-1)]),_:1,__:[7]})]),_:2},1032,[`onClick`]),J($,{icon:``,size:`small`,variant:`text`,onClick:t=>n.$emit(`edit`,e)},{default:I(()=>[J(o,{size:`small`},{default:I(()=>r[8]||=[q(`mdi-pencil`,-1)]),_:1,__:[8]}),J(He,{activator:`parent`},{default:I(()=>r[9]||=[q(`Edit`,-1)]),_:1,__:[9]})]),_:2},1032,[`onClick`]),J($,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>n.$emit(`delete`,e)},{default:I(()=>[J(o,{size:`small`},{default:I(()=>r[10]||=[q(`mdi-delete`,-1)]),_:1,__:[10]}),J(He,{activator:`parent`},{default:I(()=>r[11]||=[q(`Delete`,-1)]),_:1,__:[11]})]),_:2},1032,[`onClick`])])]),"no-data":I(()=>[G(`div`,bn,[J(o,{size:`64`,color:`grey-lighten-2`},{default:I(()=>[q(H(n.noDataIcon),1)]),_:1}),G(`p`,xn,H(n.noDataText),1),n.showAddButton?(Z(),K($,{key:0,color:`primary`,onClick:r[3]||=e=>n.$emit(`add`),class:`mt-2`},{default:I(()=>[J(o,{start:``},{default:I(()=>r[12]||=[q(`mdi-plus`,-1)]),_:1,__:[12]}),q(` Add `+H(n.itemName),1)]),_:1})):xe(``,!0)])]),loading:I(()=>[G(`div`,Sn,[J(a,{indeterminate:``,color:`primary`}),r[13]||=G(`p`,{class:`text-body-2 text-medium-emphasis mt-2`},`Loading data...`,-1)])]),_:2},[De(n.headers,e=>({name:`item.${e.key}`,fn:I(({item:t})=>[pe(n.$slots,`item.${e.key}`,{item:t,value:t[e.key]},()=>[e.type===`text`?(Z(),Se(`span`,gn,H(t[e.key]),1)):e.type===`boolean`?(Z(),K(Ne,{key:1,color:t[e.key]?`success`:`error`,size:`small`,variant:`tonal`},{default:I(()=>[q(H(t[e.key]?`Active`:`Inactive`),1)]),_:2},1032,[`color`])):e.type===`date`?(Z(),Se(`span`,_n,H(h(t[e.key])),1)):e.type===`badge`?(Z(),K(Ne,{key:3,color:g(t[e.key]),size:`small`,variant:`tonal`},{default:I(()=>[q(H(t[e.key]),1)]),_:2},1032,[`color`])):(Z(),Se(`span`,vn,H(t[e.key]),1))],!0)])}))]),1032,[`headers`,`items`,`loading`,`items-per-page`,`server-items-length`])]),_:3}))}}),wn=P(Cn,[[`__scopeId`,`data-v-ff4a9f5b`]]);const Tn=x({indeterminate:Boolean,inset:Boolean,flat:Boolean,loading:{type:[Boolean,String],default:!1},...Be(),...Le()},`VSwitch`),En=_()({name:`VSwitch`,inheritAttrs:!1,props:Tn(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,t){let{attrs:r,slots:s}=t,c=M(e,`indeterminate`),l=M(e,`modelValue`),{loaderClasses:u}=i(e),{isFocused:d,focus:f,blur:p}=Ve(e),m=L(),g=L(),_=fe&&window.matchMedia(`(forced-colors: active)`).matches,v=R(()=>typeof e.loading==`string`&&e.loading!==``?e.loading:e.color),y=me(),b=R(()=>e.id||`switch-${y}`);function x(){c.value&&=!1}function S(e){e.stopPropagation(),e.preventDefault(),m.value?.input?.click()}return N(()=>{let[t,i]=ee(r),y=ze.filterProps(e),C=Ie.filterProps(e);return J(ze,X({ref:g,class:[`v-switch`,{"v-switch--flat":e.flat},{"v-switch--inset":e.inset},{"v-switch--indeterminate":c.value},u.value,e.class]},t,y,{modelValue:l.value,"onUpdate:modelValue":e=>l.value=e,id:b.value,focused:d.value,style:e.style}),{...s,default:t=>{let{id:r,messagesId:u,isDisabled:d,isReadonly:g,isValid:y}=t,b={model:l,isValid:y};return J(Ie,X({ref:m},C,{modelValue:l.value,"onUpdate:modelValue":[e=>l.value=e,x],id:r.value,"aria-describedby":u.value,type:`checkbox`,"aria-checked":c.value?`mixed`:void 0,disabled:d.value,readonly:g.value,onFocus:f,onBlur:p},i),{...s,default:e=>{let{backgroundColorClasses:t,backgroundColorStyles:n}=e;return G(`div`,{class:B([`v-switch__track`,_?void 0:t.value]),style:V(n.value),onClick:S},[s[`track-true`]&&G(`div`,{key:`prepend`,class:`v-switch__track-true`},[s[`track-true`](b)]),s[`track-false`]&&G(`div`,{key:`append`,class:`v-switch__track-false`},[s[`track-false`](b)])])},input:t=>{let{inputNode:r,icon:i,backgroundColorClasses:c,backgroundColorStyles:l}=t;return G(U,null,[r,G(`div`,{class:B([`v-switch__thumb`,{"v-switch__thumb--filled":i||e.loading},e.inset||_?void 0:c.value]),style:V(e.inset?void 0:l.value)},[s.thumb?J(h,{defaults:{VIcon:{icon:i,size:`x-small`}}},{default:()=>[s.thumb({...b,icon:i})]}):J(ne,null,{default:()=>[e.loading?J(n,{name:`v-switch`,active:!0,color:y.value===!1?void 0:v.value},{default:e=>s.loader?s.loader(e):J(a,{active:e.isActive,color:e.color,indeterminate:!0,size:`16`,width:`2`},null)}):i&&J(o,{key:String(i),icon:i,size:`x-small`},null)]})])])}})}})}),Oe({},g)}});export{En as b,wn as c};