import{b as e,c as t}from"./VSwitch-Bh_Rc-In.js";import{b as n,c as r}from"./VRow-Cvqvybmt.js";import{I as i,bD as ee,bF as a,bJ as o,bK as s,bO as c,bS as l,bd as te,bf as u,bg as d,bh as f,bi as p,bj as m,bl as h,bm as g,bn as ne,bv as re,bx as _,bz as ie,q as ae,s as v,t as oe,u as y}from"./index-BSnscBhv.js";import{d as b,j as se,k as ce}from"./api-BWRuf0Vj.js";import{b as x,c as S,d as C,e as le}from"./VCard-DVRc-Pxh.js";import{b as w}from"./VChip-CBN0Kf2u.js";import"./VTimeline-uQEqPSC6.js";import{b as T}from"./VSelect-DqM1bu6y.js";import{b as E}from"./VDialog-VHlGBbps.js";import{b as D}from"./VTextField-BU8lnKH2.js";import{b as ue,c as O}from"./FormModal-CAo97PhI.js";import"./VForm-CDHrkI-n.js";import{b as k}from"./VSnackbar-KpoxlJmd.js";import{b as A}from"./VTextarea-BciMMY-M.js";import{b as de,c as fe}from"./CourseTopics-0mf0fzpJ.js";const pe={class:`d-flex align-center justify-space-between`},me={class:`text-h4 font-weight-bold`},he={class:`d-flex align-center justify-space-between`},ge={class:`text-h4 font-weight-bold`},_e={class:`d-flex align-center justify-space-between`},ve={class:`text-h4 font-weight-bold`},ye={class:`d-flex align-center justify-space-between`},be={class:`text-h4 font-weight-bold`},xe={class:`d-flex align-center`},Se={class:`font-weight-medium`},Ce={key:0,class:`d-flex align-center`},we={key:1,class:`text-medium-emphasis`},Te={key:0},Ee={key:1,class:`text-medium-emphasis`},De={class:`d-flex align-center gap-1`},Oe={class:`d-flex align-center`},ke={class:`d-flex align-center`};var j=ne({__name:`CoursesView`,setup(ne){let j=s(!1),M=s(!1),N=s(!1),P=s(!1),F=s(!1),I=s(!1),L=s(!1),R=s(!1),z=s(!1),B=s(``),V=s(``),H=s([]),U=s([]),W=s([]),G=s(0),K=s(null),q=s(`create`),J=o({code:``,name:``,study_program_id:null,semester:null,credits:null,course_type:`wajib`,prerequisite_courses:[],description:``,learning_objectives:``,coordinator_id:null,is_active:!0}),Y=s(``),X=s({}),Z=s({page:1,per_page:20,sort_by:`created_at`,sort_order:`desc`}),Ae=u(()=>H.value.length),je=u(()=>H.value.filter(e=>e.is_active).length),Me=u(()=>H.value.filter(e=>e.course_type===`wajib`).length),Ne=u(()=>H.value.filter(e=>e.course_type===`pilihan`).length),Pe=[{key:`code`,title:`Code`,sortable:!0,type:`text`},{key:`name`,title:`Course Name`,sortable:!0,type:`text`},{key:`course_type`,title:`Type`,sortable:!0,type:`text`},{key:`semester`,title:`Semester`,sortable:!0,type:`text`},{key:`credits`,title:`Credits`,sortable:!0,type:`text`},{key:`coordinator`,title:`Coordinator`,sortable:!1,type:`text`},{key:`prerequisites`,title:`Prerequisites`,sortable:!1,type:`text`},{key:`is_active`,title:`Status`,sortable:!0,type:`boolean`}],Fe=[{key:`course_type`,label:`Course Type`,options:[{title:`Mandatory`,value:`wajib`},{title:`Elective`,value:`pilihan`}]},{key:`semester`,label:`Semester`,options:Array.from({length:8},(e,t)=>({title:`Semester ${t+1}`,value:t+1}))},{key:`is_active`,label:`Status`,options:[{title:`Active`,value:!0},{title:`Inactive`,value:!1}]}],Ie=u(()=>{switch(q.value){case`create`:return`Create New Course`;case`edit`:return`Edit Course`;case`view`:return`View Course Details`;default:return`Course Form`}}),Le=u(()=>{switch(q.value){case`create`:return`mdi-book-plus`;case`edit`:return`mdi-book-edit`;case`view`:return`mdi-book-open-page-variant`;default:return`mdi-book`}}),Re=u(()=>U.value.map(e=>({title:`${e.name} (${e.code})`,value:e.id}))),ze=u(()=>W.value.map(e=>({title:e.full_name,value:e.id}))),Be=u(()=>!J.study_program_id||!J.semester?[]:H.value.filter(e=>e.study_program_id===J.study_program_id&&e.semester<(J.semester||0)&&e.is_active).map(e=>({title:`${e.code} - ${e.name}`,value:e.id}))),Ve=[{title:`Mandatory (Wajib)`,value:`wajib`},{title:`Elective (Pilihan)`,value:`pilihan`}],He=Array.from({length:8},(e,t)=>({title:`Semester ${t+1}`,value:t+1})),Ue=Array.from({length:6},(e,t)=>({title:`${t+1} SKS`,value:t+1})),We=[e=>!!e||`Course code is required`,e=>e.length>=3||`Course code must be at least 3 characters`,e=>/^[A-Z]{2,3}\d{3}$/.test(e)||`Course code format: ABC123 (2-3 letters + 3 digits)`],Ge=[e=>!!e||`Course name is required`,e=>e.length>=3||`Course name must be at least 3 characters`],Ke=[e=>!!e||`Study program is required`],qe=[e=>!!e||`Course type is required`],Je=[e=>!!e||`Semester is required`,e=>e>=1&&e<=8||`Semester must be between 1 and 8`],Ye=[e=>!!e||`Credits is required`,e=>e>=1&&e<=6||`Credits must be between 1 and 6`],Q=async()=>{j.value=!0;try{let e={...Z.value,search:Y.value,...X.value},t=await b.getAll(e);H.value=t.data.data,G.value=t.data.meta?.total||0}catch(e){V.value=`Failed to load courses`,z.value=!0,console.error(`Load courses error:`,e)}finally{j.value=!1}},Xe=async()=>{try{let e=await se.getAll({per_page:100});U.value=e.data.data}catch(e){console.error(`Load study programs error:`,e)}},Ze=async()=>{try{let e=await ce.getAll({per_page:100,role:`dosen`});W.value=e.data.data}catch(e){console.error(`Load coordinators error:`,e)}},Qe=()=>{q.value=`create`,rt(),P.value=!0},$=e=>{q.value=`edit`,K.value=e,it(e),P.value=!0},$e=e=>{q.value=`view`,K.value=e,it(e),P.value=!0},et=e=>{K.value=e,F.value=!0},tt=e=>{K.value=e,I.value=!0},nt=e=>{K.value=e,L.value=!0},rt=()=>{Object.assign(J,{code:``,name:``,study_program_id:null,semester:null,credits:null,course_type:`wajib`,prerequisite_courses:[],description:``,learning_objectives:``,coordinator_id:null,is_active:!0})},it=e=>{Object.assign(J,{code:e.code,name:e.name,study_program_id:e.study_program_id,semester:e.semester,credits:e.credits,course_type:e.course_type,prerequisite_courses:e.prerequisite_courses||[],description:e.description||``,learning_objectives:e.learning_objectives||``,coordinator_id:e.coordinator_id,is_active:e.is_active})},at=async()=>{M.value=!0;try{q.value===`create`?(await b.create(J),B.value=`Course created successfully!`):q.value===`edit`&&K.value&&(await b.update(K.value.id,J),B.value=`Course updated successfully!`),R.value=!0,st(),await Q()}catch(e){V.value=e.response?.data?.message||`Operation failed`,z.value=!0}finally{M.value=!1}},ot=async()=>{if(K.value){N.value=!0;try{await b.delete(K.value.id),B.value=`Course deleted successfully!`,R.value=!0,L.value=!1,await Q()}catch(e){V.value=e.response?.data?.message||`Delete failed`,z.value=!0}finally{N.value=!1}}},st=()=>{P.value=!1,K.value=null,rt()},ct=e=>{Y.value=e,Z.value.page=1,Q()},lt=e=>{X.value=e,Z.value.page=1,Q()},ut=e=>{Z.value={...Z.value,page:e.page,per_page:e.itemsPerPage,sort_by:e.sortBy?.[0]?.key||`created_at`,sort_order:e.sortBy?.[0]?.order||`desc`},Q()};return ee([()=>J.study_program_id,()=>J.semester],()=>{J.prerequisite_courses=[]}),re(async()=>{await Promise.all([Q(),Xe(),Ze()])}),(ee,o)=>(_(),m(`div`,null,[g(n,{class:`mb-6`},{default:a(()=>[g(r,null,{default:a(()=>o[24]||=[d(`h1`,{class:`text-h4 font-weight-bold text-primary`},`Course Management`,-1),d(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Manage courses, references, topics, and learning objectives `,-1)]),_:1,__:[24]})]),_:1}),g(n,{class:`mb-6`},{default:a(()=>[g(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[g(x,{color:`primary`,variant:`tonal`},{default:a(()=>[g(S,null,{default:a(()=>[d(`div`,pe,[d(`div`,null,[o[25]||=d(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total Courses`,-1),d(`h2`,me,l(Ae.value),1)]),g(i,{size:`48`,color:`primary`},{default:a(()=>o[26]||=[h(`mdi-book-open-page-variant`,-1)]),_:1,__:[26]})])]),_:1})]),_:1})]),_:1}),g(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[g(x,{color:`success`,variant:`tonal`},{default:a(()=>[g(S,null,{default:a(()=>[d(`div`,he,[d(`div`,null,[o[27]||=d(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Active Courses`,-1),d(`h2`,ge,l(je.value),1)]),g(i,{size:`48`,color:`success`},{default:a(()=>o[28]||=[h(`mdi-check-circle`,-1)]),_:1,__:[28]})])]),_:1})]),_:1})]),_:1}),g(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[g(x,{color:`warning`,variant:`tonal`},{default:a(()=>[g(S,null,{default:a(()=>[d(`div`,_e,[d(`div`,null,[o[29]||=d(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Mandatory`,-1),d(`h2`,ve,l(Me.value),1)]),g(i,{size:`48`,color:`warning`},{default:a(()=>o[30]||=[h(`mdi-star`,-1)]),_:1,__:[30]})])]),_:1})]),_:1})]),_:1}),g(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[g(x,{color:`info`,variant:`tonal`},{default:a(()=>[g(S,null,{default:a(()=>[d(`div`,ye,[d(`div`,null,[o[31]||=d(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Elective`,-1),d(`h2`,be,l(Ne.value),1)]),g(i,{size:`48`,color:`info`},{default:a(()=>o[32]||=[h(`mdi-school`,-1)]),_:1,__:[32]})])]),_:1})]),_:1})]),_:1})]),_:1}),g(t,{title:`Course List`,icon:`mdi-book-open-page-variant`,"item-name":`Course`,headers:Pe,items:H.value,loading:j.value,"total-items":G.value,filters:Fe,onAdd:Qe,onEdit:$,onDelete:nt,onView:$e,onRefresh:Q,onSearch:ct,onFilter:lt,"onUpdate:options":ut},{"item.code":a(({item:e})=>[g(w,{color:`primary`,variant:`tonal`,size:`small`,class:`font-weight-bold`},{default:a(()=>[h(l(e.code),1)]),_:2},1024)]),"item.course_type":a(({item:e})=>[g(w,{color:e.course_type===`wajib`?`warning`:`info`,variant:`tonal`,size:`small`},{default:a(()=>[h(l(e.course_type===`wajib`?`Mandatory`:`Elective`),1)]),_:2},1032,[`color`])]),"item.credits":a(({item:e})=>[d(`div`,xe,[g(i,{size:`small`,class:`mr-1`},{default:a(()=>o[33]||=[h(`mdi-star`,-1)]),_:1,__:[33]}),d(`span`,Se,l(e.credits)+` SKS`,1)])]),"item.semester":a(({item:e})=>[g(w,{color:`secondary`,variant:`outlined`,size:`small`},{default:a(()=>[h(` Semester `+l(e.semester),1)]),_:2},1024)]),"item.coordinator":a(({item:e})=>[e.coordinator_name?(_(),m(`div`,Ce,[g(oe,{size:`24`,class:`mr-2`},{default:a(()=>[g(i,{size:`small`},{default:a(()=>o[34]||=[h(`mdi-account`,-1)]),_:1,__:[34]})]),_:1}),d(`span`,null,l(e.coordinator_name),1)])):(_(),m(`span`,we,`Not assigned`))]),"item.prerequisites":a(({item:e})=>[e.prerequisite_courses&&e.prerequisite_courses.length>0?(_(),m(`div`,Te,[(_(!0),m(te,null,ie(e.prerequisite_courses.slice(0,2),e=>(_(),f(w,{key:e.id,size:`x-small`,variant:`outlined`,class:`ma-1`},{default:a(()=>[h(l(e.code),1)]),_:2},1024))),128)),e.prerequisite_courses.length>2?(_(),f(w,{key:0,size:`x-small`,variant:`text`,class:`ma-1`},{default:a(()=>[h(` +`+l(e.prerequisite_courses.length-2)+` more `,1)]),_:2},1024)):p(``,!0)])):(_(),m(`span`,Ee,`None`))]),"item.actions":a(({item:e})=>[d(`div`,De,[g(y,{icon:``,size:`small`,variant:`text`,onClick:t=>$e(e)},{default:a(()=>[g(i,{size:`small`},{default:a(()=>o[35]||=[h(`mdi-eye`,-1)]),_:1,__:[35]}),g(O,{activator:`parent`},{default:a(()=>o[36]||=[h(`View Details`,-1)]),_:1,__:[36]})]),_:2},1032,[`onClick`]),g(y,{icon:``,size:`small`,variant:`text`,onClick:t=>$(e)},{default:a(()=>[g(i,{size:`small`},{default:a(()=>o[37]||=[h(`mdi-pencil`,-1)]),_:1,__:[37]}),g(O,{activator:`parent`},{default:a(()=>o[38]||=[h(`Edit Course`,-1)]),_:1,__:[38]})]),_:2},1032,[`onClick`]),g(y,{icon:``,size:`small`,variant:`text`,onClick:t=>et(e)},{default:a(()=>[g(i,{size:`small`},{default:a(()=>o[39]||=[h(`mdi-book-multiple`,-1)]),_:1,__:[39]}),g(O,{activator:`parent`},{default:a(()=>o[40]||=[h(`Manage References`,-1)]),_:1,__:[40]})]),_:2},1032,[`onClick`]),g(y,{icon:``,size:`small`,variant:`text`,onClick:t=>tt(e)},{default:a(()=>[g(i,{size:`small`},{default:a(()=>o[41]||=[h(`mdi-format-list-numbered`,-1)]),_:1,__:[41]}),g(O,{activator:`parent`},{default:a(()=>o[42]||=[h(`Manage Topics`,-1)]),_:1,__:[42]})]),_:2},1032,[`onClick`]),g(y,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>nt(e)},{default:a(()=>[g(i,{size:`small`},{default:a(()=>o[43]||=[h(`mdi-delete`,-1)]),_:1,__:[43]}),g(O,{activator:`parent`},{default:a(()=>o[44]||=[h(`Delete Course`,-1)]),_:1,__:[44]})]),_:2},1032,[`onClick`])])]),_:1},8,[`items`,`loading`,`total-items`]),g(ue,{modelValue:P.value,"onUpdate:modelValue":o[11]||=e=>P.value=e,title:Ie.value,icon:Le.value,mode:q.value,loading:M.value,"max-width":`900`,onSubmit:at,onClose:st},{default:a(()=>[g(n,null,{default:a(()=>[g(r,{cols:`12`,md:`6`},{default:a(()=>[g(D,{modelValue:J.code,"onUpdate:modelValue":o[0]||=e=>J.code=e,rules:We,label:`Course Code *`,variant:`outlined`,"prepend-inner-icon":`mdi-identifier`,disabled:M.value||q.value===`view`,placeholder:`e.g., TIF101`},null,8,[`modelValue`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`6`},{default:a(()=>[g(D,{modelValue:J.name,"onUpdate:modelValue":o[1]||=e=>J.name=e,rules:Ge,label:`Course Name *`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open-page-variant`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`6`},{default:a(()=>[g(T,{modelValue:J.study_program_id,"onUpdate:modelValue":o[2]||=e=>J.study_program_id=e,items:Re.value,rules:Ke,label:`Study Program *`,variant:`outlined`,"prepend-inner-icon":`mdi-school`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`6`},{default:a(()=>[g(T,{modelValue:J.course_type,"onUpdate:modelValue":o[3]||=e=>J.course_type=e,items:Ve,rules:qe,label:`Course Type *`,variant:`outlined`,"prepend-inner-icon":`mdi-tag`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`4`},{default:a(()=>[g(T,{modelValue:J.semester,"onUpdate:modelValue":o[4]||=e=>J.semester=e,items:c(He),rules:Je,label:`Semester *`,variant:`outlined`,"prepend-inner-icon":`mdi-calendar`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`4`},{default:a(()=>[g(T,{modelValue:J.credits,"onUpdate:modelValue":o[5]||=e=>J.credits=e,items:c(Ue),rules:Ye,label:`Credits (SKS) *`,variant:`outlined`,"prepend-inner-icon":`mdi-star`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`4`},{default:a(()=>[g(T,{modelValue:J.coordinator_id,"onUpdate:modelValue":o[6]||=e=>J.coordinator_id=e,items:ze.value,label:`Course Coordinator`,variant:`outlined`,"prepend-inner-icon":`mdi-account-tie`,clearable:``,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),g(r,{cols:`12`},{default:a(()=>[g(T,{modelValue:J.prerequisite_courses,"onUpdate:modelValue":o[7]||=e=>J.prerequisite_courses=e,items:Be.value,label:`Prerequisite Courses`,variant:`outlined`,"prepend-inner-icon":`mdi-arrow-left-circle`,multiple:``,chips:``,clearable:``,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),g(r,{cols:`12`},{default:a(()=>[g(A,{modelValue:J.description,"onUpdate:modelValue":o[8]||=e=>J.description=e,label:`Course Description`,variant:`outlined`,"prepend-inner-icon":`mdi-text`,rows:`3`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),g(r,{cols:`12`},{default:a(()=>[g(A,{modelValue:J.learning_objectives,"onUpdate:modelValue":o[9]||=e=>J.learning_objectives=e,label:`Learning Objectives`,variant:`outlined`,"prepend-inner-icon":`mdi-target`,rows:`3`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),g(r,{cols:`12`,md:`6`},{default:a(()=>[g(e,{modelValue:J.is_active,"onUpdate:modelValue":o[10]||=e=>J.is_active=e,label:`Active`,color:`primary`,disabled:M.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`icon`,`mode`,`loading`]),g(E,{modelValue:F.value,"onUpdate:modelValue":o[14]||=e=>F.value=e,"max-width":`1000`,scrollable:``},{default:a(()=>[g(x,null,{default:a(()=>[g(C,{class:`d-flex align-center justify-space-between`},{default:a(()=>[d(`div`,Oe,[g(i,{class:`mr-2`},{default:a(()=>o[45]||=[h(`mdi-book-multiple`,-1)]),_:1,__:[45]}),o[46]||=d(`span`,{class:`text-h6 font-weight-bold`},`Course References`,-1)]),g(y,{icon:``,variant:`text`,onClick:o[12]||=e=>F.value=!1},{default:a(()=>[g(i,null,{default:a(()=>o[47]||=[h(`mdi-close`,-1)]),_:1,__:[47]})]),_:1})]),_:1}),g(v),g(S,{class:`pa-0`},{default:a(()=>[K.value?(_(),f(fe,{key:0,course:K.value,onClose:o[13]||=e=>F.value=!1},null,8,[`course`])):p(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),g(E,{modelValue:I.value,"onUpdate:modelValue":o[17]||=e=>I.value=e,"max-width":`1200`,scrollable:``},{default:a(()=>[g(x,null,{default:a(()=>[g(C,{class:`d-flex align-center justify-space-between`},{default:a(()=>[d(`div`,ke,[g(i,{class:`mr-2`},{default:a(()=>o[48]||=[h(`mdi-format-list-numbered`,-1)]),_:1,__:[48]}),o[49]||=d(`span`,{class:`text-h6 font-weight-bold`},`Course Topics`,-1)]),g(y,{icon:``,variant:`text`,onClick:o[15]||=e=>I.value=!1},{default:a(()=>[g(i,null,{default:a(()=>o[50]||=[h(`mdi-close`,-1)]),_:1,__:[50]})]),_:1})]),_:1}),g(v),g(S,{class:`pa-0`},{default:a(()=>[K.value?(_(),f(de,{key:0,course:K.value,onClose:o[16]||=e=>I.value=!1},null,8,[`course`])):p(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),g(E,{modelValue:L.value,"onUpdate:modelValue":o[19]||=e=>L.value=e,"max-width":`400`},{default:a(()=>[g(x,null,{default:a(()=>[g(C,{class:`text-h6`},{default:a(()=>o[51]||=[h(`Confirm Delete`,-1)]),_:1,__:[51]}),g(S,null,{default:a(()=>[h(` Are you sure you want to delete course "`+l(K.value?.name)+`"? This action cannot be undone and will also delete all related references and topics. `,1)]),_:1}),g(le,null,{default:a(()=>[g(ae),g(y,{onClick:o[18]||=e=>L.value=!1},{default:a(()=>o[52]||=[h(`Cancel`,-1)]),_:1,__:[52]}),g(y,{color:`error`,loading:N.value,onClick:ot},{default:a(()=>o[53]||=[h(` Delete `,-1)]),_:1,__:[53]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),g(k,{modelValue:R.value,"onUpdate:modelValue":o[21]||=e=>R.value=e,color:`success`,timeout:`3000`},{actions:a(()=>[g(y,{onClick:o[20]||=e=>R.value=!1},{default:a(()=>o[54]||=[h(`Close`,-1)]),_:1,__:[54]})]),default:a(()=>[h(l(B.value)+` `,1)]),_:1},8,[`modelValue`]),g(k,{modelValue:z.value,"onUpdate:modelValue":o[23]||=e=>z.value=e,color:`error`,timeout:`5000`},{actions:a(()=>[g(y,{onClick:o[22]||=e=>z.value=!1},{default:a(()=>o[55]||=[h(`Close`,-1)]),_:1,__:[55]})]),default:a(()=>[h(l(V.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),M=j;export{M as default};