.v-card{overflow-wrap:break-word;z-index:0;border-color:rgba(var(--v-border-color),var(--v-border-opacity));border-style:solid;border-width:0;padding:0;text-decoration:none;transition-property:box-shadow,opacity,background;transition-duration:.28s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:relative;overflow:hidden}.v-card--border{box-shadow:none;border-width:thin}.v-card--absolute{position:absolute}.v-card--fixed{position:fixed}.v-card{border-radius:4px}.v-card:hover>.v-card__overlay{opacity:calc(var(--v-hover-opacity)*var(--v-theme-overlay-multiplier))}.v-card:focus-visible>.v-card__overlay{opacity:calc(var(--v-focus-opacity)*var(--v-theme-overlay-multiplier))}@supports not selector(:focus-visible){.v-card:focus>.v-card__overlay{opacity:calc(var(--v-focus-opacity)*var(--v-theme-overlay-multiplier))}}.v-card--active>.v-card__overlay,.v-card[aria-haspopup=menu][aria-expanded=true]>.v-card__overlay{opacity:calc(var(--v-activated-opacity)*var(--v-theme-overlay-multiplier))}.v-card--active:hover>.v-card__overlay,.v-card[aria-haspopup=menu][aria-expanded=true]:hover>.v-card__overlay{opacity:calc((var(--v-activated-opacity) + var(--v-hover-opacity))*var(--v-theme-overlay-multiplier))}.v-card--active:focus-visible>.v-card__overlay,.v-card[aria-haspopup=menu][aria-expanded=true]:focus-visible>.v-card__overlay{opacity:calc((var(--v-activated-opacity) + var(--v-focus-opacity))*var(--v-theme-overlay-multiplier))}@supports not selector(:focus-visible){.v-card--active:focus>.v-card__overlay,.v-card[aria-haspopup=menu][aria-expanded=true]:focus>.v-card__overlay{opacity:calc((var(--v-activated-opacity) + var(--v-focus-opacity))*var(--v-theme-overlay-multiplier))}}.v-card--variant-plain,.v-card--variant-outlined,.v-card--variant-text,.v-card--variant-tonal{color:inherit;background:0 0}.v-card--variant-plain{opacity:.62}.v-card--variant-plain:focus,.v-card--variant-plain:hover{opacity:1}.v-card--variant-plain .v-card__overlay{display:none}.v-card--variant-elevated,.v-card--variant-flat{background:rgb(var(--v-theme-surface));color:rgba(var(--v-theme-on-surface),var(--v-high-emphasis-opacity))}.v-card--variant-elevated{box-shadow:0px 2px 1px -1px var(--v-shadow-key-umbra-opacity,#0003),0px 1px 1px 0px var(--v-shadow-key-penumbra-opacity,#00000024),0px 1px 3px 0px var(--v-shadow-key-ambient-opacity,#0000001f)}.v-card--variant-flat{box-shadow:0px 0px 0px 0px var(--v-shadow-key-umbra-opacity,#0003),0px 0px 0px 0px var(--v-shadow-key-penumbra-opacity,#00000024),0px 0px 0px 0px var(--v-shadow-key-ambient-opacity,#0000001f)}.v-card--variant-outlined{border:thin solid}.v-card--variant-text .v-card__overlay{background:currentColor}.v-card--variant-tonal .v-card__underlay{opacity:var(--v-activated-opacity);border-radius:inherit;pointer-events:none;background:currentColor;inset:0}.v-card .v-card__underlay{position:absolute}.v-card--disabled{pointer-events:none;-webkit-user-select:none;user-select:none}.v-card--disabled>:not(.v-card__loader){opacity:.6}.v-card--flat{box-shadow:none}.v-card--hover{cursor:pointer}.v-card--hover:before,.v-card--hover:after{border-radius:inherit;content:"";pointer-events:none;transition:inherit;display:block;position:absolute;inset:0}.v-card--hover:before{opacity:1;z-index:-1;box-shadow:0px 2px 1px -1px var(--v-shadow-key-umbra-opacity,#0003),0px 1px 1px 0px var(--v-shadow-key-penumbra-opacity,#00000024),0px 1px 3px 0px var(--v-shadow-key-ambient-opacity,#0000001f)}.v-card--hover:after{z-index:1;opacity:0;box-shadow:0px 5px 5px -3px var(--v-shadow-key-umbra-opacity,#0003),0px 8px 10px 1px var(--v-shadow-key-penumbra-opacity,#00000024),0px 3px 14px 2px var(--v-shadow-key-ambient-opacity,#0000001f)}.v-card--hover:hover:after{opacity:1}.v-card--hover:hover:before{opacity:0}.v-card--hover:hover{box-shadow:0px 5px 5px -3px var(--v-shadow-key-umbra-opacity,#0003),0px 8px 10px 1px var(--v-shadow-key-penumbra-opacity,#00000024),0px 3px 14px 2px var(--v-shadow-key-ambient-opacity,#0000001f)}.v-card--link{cursor:pointer}.v-card-actions{flex:none;align-items:center;gap:.5rem;min-height:52px;padding:.5rem;display:flex}.v-card-item{flex:none;grid-template-columns:max-content auto max-content;grid-template-areas:"prepend content append";align-items:center;padding:.625rem 1rem;display:grid}.v-card-item+.v-card-text{padding-top:0}.v-card-item__prepend,.v-card-item__append{align-items:center;display:flex}.v-card-item__prepend{grid-area:prepend;padding-inline-end:.5rem}.v-card-item__append{grid-area:append;padding-inline-start:.5rem}.v-card-item__content{grid-area:content;align-self:center;overflow:hidden}.v-card-title{-webkit-hyphens:auto;hyphens:auto;letter-spacing:.0125em;overflow-wrap:normal;text-overflow:ellipsis;text-transform:none;white-space:nowrap;word-break:normal;word-wrap:break-word;flex:none;min-width:0;padding:.5rem 1rem;font-size:1.25rem;font-weight:500;display:block;overflow:hidden}.v-card .v-card-title{line-height:1.6}.v-card--density-comfortable .v-card-title{line-height:1.75rem}.v-card--density-compact .v-card-title{line-height:1.55rem}.v-card-item .v-card-title{padding:0}.v-card-title+.v-card-text,.v-card-title+.v-card-actions{padding-top:0}.v-card-subtitle{letter-spacing:.0178571em;opacity:var(--v-card-subtitle-opacity,var(--v-medium-emphasis-opacity));text-overflow:ellipsis;text-transform:none;white-space:nowrap;flex:none;padding:0 1rem;font-size:.875rem;font-weight:400;display:block;overflow:hidden}.v-card .v-card-subtitle{line-height:1.425}.v-card--density-comfortable .v-card-subtitle{line-height:1.125rem}.v-card--density-compact .v-card-subtitle{line-height:1rem}.v-card-item .v-card-subtitle{padding:0 0 .25rem}.v-card-text{letter-spacing:.0178571em;opacity:var(--v-card-text-opacity,1);text-transform:none;flex:auto;padding:1rem;font-size:.875rem;font-weight:400}.v-card .v-card-text{line-height:1.425}.v-card--density-comfortable .v-card-text{line-height:1.2rem}.v-card--density-compact .v-card-text{line-height:1.15rem}.v-card__image{z-index:-1;flex:auto;width:100%;height:100%;display:flex;position:absolute;top:0;left:0;overflow:hidden}.v-card__content{border-radius:inherit;position:relative;overflow:hidden}.v-card__loader{z-index:1;width:100%;position:absolute;inset:0 0 auto}.v-card__overlay{border-radius:inherit;pointer-events:none;opacity:0;background-color:currentColor;transition:opacity .2s ease-in-out;position:absolute;inset:0}
