import{b as e,c as t}from"./VSwitch-Bh_Rc-In.js";import{b as n,c as r}from"./VRow-Cvqvybmt.js";import{E as i,I as a,bD as o,bF as s,bJ as c,bK as l,bS as u,bf as d,bg as f,bh as p,bi as m,bj as h,bl as g,bm as _,bn as v,bv as y,bx as b,q as x,s as S,u as C}from"./index-BSnscBhv.js";import{b as w,c as T,d as E}from"./api-BWRuf0Vj.js";import{b as D,c as O,d as k,e as A}from"./VCard-DVRc-Pxh.js";import{b as j}from"./VChip-CBN0Kf2u.js";import{b as M}from"./VSelect-DqM1bu6y.js";import{b as N}from"./VDialog-VHlGBbps.js";import{b as ee}from"./VTextField-BU8lnKH2.js";import{b as te,c as P}from"./FormModal-CAo97PhI.js";import"./VForm-CDHrkI-n.js";import{b as F}from"./VSnackbar-KpoxlJmd.js";import{b as I}from"./VTextarea-BciMMY-M.js";import{b as L,c as R,d as z,e as B}from"./VTabs-Dp1ayKKb.js";const V={class:`pa-6`},H={class:`text-center py-8`},U={class:`text-body-2 text-medium-emphasis`};var W=v({__name:`AssessmentMethodAnalytics`,props:{method:{}},emits:[`close`],setup(e){return(e,t)=>(b(),h(`div`,V,[f(`div`,H,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[0]||=[g(`mdi-chart-bar`,-1)]),_:1,__:[0]}),t[1]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Assessment Method Analytics`,-1),f(`p`,U,` Usage analytics and performance metrics for `+u(e.method.name),1),t[2]||=f(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),ne=W;const re={class:`d-flex align-center justify-space-between`},ie={class:`text-h4 font-weight-bold`},ae={class:`d-flex align-center justify-space-between`},G={class:`text-h4 font-weight-bold`},oe={class:`d-flex align-center justify-space-between`},se={class:`text-h4 font-weight-bold`},K={class:`d-flex align-center justify-space-between`},ce={class:`text-h4 font-weight-bold`},le={class:`d-flex align-center`},ue={class:`font-weight-medium`},de={class:`text-truncate`,style:{"max-width":`300px`}},fe={class:`d-flex align-center gap-1`},pe={class:`d-flex align-center`},me={class:`text-h6 font-weight-bold`};var q=v({__name:`AssessmentMethodsTab`,setup(i){let o=l(!1),v=l(!1),T=l(!1),E=l(!1),L=l(!1),R=l(!1),z=l(!1),B=l(!1),V=l(``),H=l(``),U=l([]),W=l(0),q=l(null),J=l(`create`),Y=c({name:``,description:``,type:`formatif`,category:`tugas`,is_active:!0}),X=l(``),Z=l({}),Q=l({page:1,per_page:20,sort_by:`created_at`,sort_order:`desc`}),he=d(()=>U.value.length),ge=d(()=>U.value.filter(e=>e.type===`formatif`).length),_e=d(()=>U.value.filter(e=>e.type===`sumatif`).length),ve=d(()=>U.value.filter(e=>e.is_active).length),ye=[{key:`name`,title:`Method Name`,sortable:!0,type:`text`},{key:`type`,title:`Type`,sortable:!0,type:`text`},{key:`category`,title:`Category`,sortable:!0,type:`text`},{key:`description`,title:`Description`,sortable:!1,type:`text`},{key:`is_active`,title:`Status`,sortable:!0,type:`boolean`},{key:`created_at`,title:`Created`,sortable:!0,type:`date`}],be=[{key:`type`,label:`Assessment Type`,options:[{title:`Formative`,value:`formatif`},{title:`Summative`,value:`sumatif`}]},{key:`category`,label:`Category`,options:[{title:`Assignment (Tugas)`,value:`tugas`},{title:`Quiz (Kuis)`,value:`kuis`},{title:`Midterm Exam (UTS)`,value:`uts`},{title:`Final Exam (UAS)`,value:`uas`},{title:`Lab Work (Praktikum)`,value:`praktikum`},{title:`Project (Proyek)`,value:`proyek`},{title:`Presentation (Presentasi)`,value:`presentasi`},{title:`Other (Lainnya)`,value:`lainnya`}]},{key:`is_active`,label:`Status`,options:[{title:`Active`,value:!0},{title:`Inactive`,value:!1}]}],xe=d(()=>{switch(J.value){case`create`:return`Create New Assessment Method`;case`edit`:return`Edit Assessment Method`;case`view`:return`View Assessment Method Details`;default:return`Assessment Method Form`}}),Se=d(()=>{switch(J.value){case`create`:return`mdi-clipboard-plus`;case`edit`:return`mdi-clipboard-edit`;case`view`:return`mdi-clipboard-list`;default:return`mdi-clipboard-list`}}),Ce=[{title:`Formative (Formatif)`,value:`formatif`},{title:`Summative (Sumatif)`,value:`sumatif`}],we=[{title:`Assignment (Tugas)`,value:`tugas`},{title:`Quiz (Kuis)`,value:`kuis`},{title:`Midterm Exam (UTS)`,value:`uts`},{title:`Final Exam (UAS)`,value:`uas`},{title:`Lab Work (Praktikum)`,value:`praktikum`},{title:`Project (Proyek)`,value:`proyek`},{title:`Presentation (Presentasi)`,value:`presentasi`},{title:`Other (Lainnya)`,value:`lainnya`}],Te=[e=>!!e||`Method name is required`,e=>e.length>=3||`Method name must be at least 3 characters`],Ee=[e=>!!e||`Assessment type is required`],De=[e=>!!e||`Assessment category is required`],$=async()=>{o.value=!0;try{let e={...Q.value,search:X.value,...Z.value},t=await w.getAll(e);U.value=t.data.data,W.value=t.data.meta?.total||0}catch(e){H.value=`Failed to load assessment methods`,B.value=!0,console.error(`Load assessment methods error:`,e)}finally{o.value=!1}},Oe=()=>{J.value=`create`,Ne(),E.value=!0},ke=e=>{J.value=`edit`,q.value=e,Pe(e),E.value=!0},Ae=e=>{J.value=`view`,q.value=e,Pe(e),E.value=!0},je=e=>{q.value=e,L.value=!0},Me=e=>{q.value=e,R.value=!0},Ne=()=>{Object.assign(Y,{name:``,description:``,type:`formatif`,category:`tugas`,is_active:!0})},Pe=e=>{Object.assign(Y,{name:e.name,description:e.description||``,type:e.type,category:e.category,is_active:e.is_active})},Fe=async()=>{v.value=!0;try{J.value===`create`?(await w.create(Y),V.value=`Assessment method created successfully!`):J.value===`edit`&&q.value&&(await w.update(q.value.id,Y),V.value=`Assessment method updated successfully!`),z.value=!0,Le(),await $()}catch(e){H.value=e.response?.data?.message||`Operation failed`,B.value=!0}finally{v.value=!1}},Ie=async()=>{if(q.value){T.value=!0;try{await w.delete(q.value.id),V.value=`Assessment method deleted successfully!`,z.value=!0,R.value=!1,await $()}catch(e){H.value=e.response?.data?.message||`Delete failed`,B.value=!0}finally{T.value=!1}}},Le=()=>{E.value=!1,q.value=null,Ne()},Re=e=>{X.value=e,Q.value.page=1,$()},ze=e=>{Z.value=e,Q.value.page=1,$()},Be=e=>{Q.value={...Q.value,page:e.page,per_page:e.itemsPerPage,sort_by:e.sortBy?.[0]?.key||`created_at`,sort_order:e.sortBy?.[0]?.order||`desc`},$()},Ve=e=>e===`formatif`?`success`:`warning`,He=e=>e===`formatif`?`mdi-clipboard-check`:`mdi-clipboard-text`,Ue=e=>e===`formatif`?`Formative`:`Summative`,We=e=>{let t={tugas:`blue`,kuis:`green`,uts:`orange`,uas:`red`,praktikum:`purple`,proyek:`indigo`,presentasi:`teal`,lainnya:`grey`};return t[e]||`primary`},Ge=e=>{let t={tugas:`mdi-file-document`,kuis:`mdi-help-circle`,uts:`mdi-school`,uas:`mdi-certificate`,praktikum:`mdi-flask`,proyek:`mdi-folder-multiple`,presentasi:`mdi-presentation`,lainnya:`mdi-dots-horizontal`};return t[e]||`mdi-clipboard-list`},Ke=e=>{let t={tugas:`Assignment`,kuis:`Quiz`,uts:`Midterm`,uas:`Final Exam`,praktikum:`Lab Work`,proyek:`Project`,presentasi:`Presentation`,lainnya:`Other`};return t[e]||e};return y(()=>{$()}),(i,c)=>(b(),h(`div`,null,[_(n,{class:`mb-6`},{default:s(()=>[_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`primary`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,re,[f(`div`,null,[c[15]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total Methods`,-1),f(`h2`,ie,u(he.value),1)]),_(a,{size:`48`,color:`primary`},{default:s(()=>c[16]||=[g(`mdi-clipboard-list`,-1)]),_:1,__:[16]})])]),_:1})]),_:1})]),_:1}),_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`success`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,ae,[f(`div`,null,[c[17]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Formative`,-1),f(`h2`,G,u(ge.value),1)]),_(a,{size:`48`,color:`success`},{default:s(()=>c[18]||=[g(`mdi-clipboard-check`,-1)]),_:1,__:[18]})])]),_:1})]),_:1})]),_:1}),_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`warning`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,oe,[f(`div`,null,[c[19]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Summative`,-1),f(`h2`,se,u(_e.value),1)]),_(a,{size:`48`,color:`warning`},{default:s(()=>c[20]||=[g(`mdi-clipboard-text`,-1)]),_:1,__:[20]})])]),_:1})]),_:1})]),_:1}),_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`info`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,K,[f(`div`,null,[c[21]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Active`,-1),f(`h2`,ce,u(ve.value),1)]),_(a,{size:`48`,color:`info`},{default:s(()=>c[22]||=[g(`mdi-check-circle`,-1)]),_:1,__:[22]})])]),_:1})]),_:1})]),_:1})]),_:1}),_(t,{title:`Assessment Methods`,icon:`mdi-clipboard-list`,"item-name":`Assessment Method`,headers:ye,items:U.value,loading:o.value,"total-items":W.value,filters:be,onAdd:Oe,onEdit:ke,onDelete:Me,onView:Ae,onRefresh:$,onSearch:Re,onFilter:ze,"onUpdate:options":Be},{"item.name":s(({item:e})=>[f(`div`,le,[_(a,{color:Ve(e.type),class:`mr-2`,size:`small`},{default:s(()=>[g(u(He(e.type)),1)]),_:2},1032,[`color`]),f(`span`,ue,u(e.name),1)])]),"item.type":s(({item:e})=>[_(j,{color:Ve(e.type),variant:`tonal`,size:`small`},{default:s(()=>[g(u(Ue(e.type)),1)]),_:2},1032,[`color`])]),"item.category":s(({item:e})=>[_(j,{color:We(e.category),variant:`tonal`,size:`small`},{default:s(()=>[_(a,{start:``,size:`small`},{default:s(()=>[g(u(Ge(e.category)),1)]),_:2},1024),g(` `+u(Ke(e.category)),1)]),_:2},1032,[`color`])]),"item.description":s(({item:e})=>[f(`div`,de,u(e.description||`-`),1)]),"item.actions":s(({item:e})=>[f(`div`,fe,[_(C,{icon:``,size:`small`,variant:`text`,onClick:t=>Ae(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>c[23]||=[g(`mdi-eye`,-1)]),_:1,__:[23]}),_(P,{activator:`parent`},{default:s(()=>c[24]||=[g(`View Details`,-1)]),_:1,__:[24]})]),_:2},1032,[`onClick`]),_(C,{icon:``,size:`small`,variant:`text`,onClick:t=>ke(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>c[25]||=[g(`mdi-pencil`,-1)]),_:1,__:[25]}),_(P,{activator:`parent`},{default:s(()=>c[26]||=[g(`Edit Method`,-1)]),_:1,__:[26]})]),_:2},1032,[`onClick`]),_(C,{icon:``,size:`small`,variant:`text`,onClick:t=>je(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>c[27]||=[g(`mdi-chart-bar`,-1)]),_:1,__:[27]}),_(P,{activator:`parent`},{default:s(()=>c[28]||=[g(`Usage Analytics`,-1)]),_:1,__:[28]})]),_:2},1032,[`onClick`]),_(C,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>Me(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>c[29]||=[g(`mdi-delete`,-1)]),_:1,__:[29]}),_(P,{activator:`parent`},{default:s(()=>c[30]||=[g(`Delete Method`,-1)]),_:1,__:[30]})]),_:2},1032,[`onClick`])])]),_:1},8,[`items`,`loading`,`total-items`]),_(te,{modelValue:E.value,"onUpdate:modelValue":c[5]||=e=>E.value=e,title:xe.value,icon:Se.value,mode:J.value,loading:v.value,"max-width":`700`,onSubmit:Fe,onClose:Le},{default:s(()=>[_(n,null,{default:s(()=>[_(r,{cols:`12`},{default:s(()=>[_(ee,{modelValue:Y.name,"onUpdate:modelValue":c[0]||=e=>Y.name=e,rules:Te,label:`Method Name *`,variant:`outlined`,"prepend-inner-icon":`mdi-clipboard-list`,disabled:v.value||J.value===`view`,placeholder:`e.g., Quiz Mingguan`},null,8,[`modelValue`,`disabled`])]),_:1}),_(r,{cols:`12`,md:`6`},{default:s(()=>[_(M,{modelValue:Y.type,"onUpdate:modelValue":c[1]||=e=>Y.type=e,items:Ce,rules:Ee,label:`Assessment Type *`,variant:`outlined`,"prepend-inner-icon":`mdi-format-list-bulleted-type`,disabled:v.value||J.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),_(r,{cols:`12`,md:`6`},{default:s(()=>[_(M,{modelValue:Y.category,"onUpdate:modelValue":c[2]||=e=>Y.category=e,items:we,rules:De,label:`Assessment Category *`,variant:`outlined`,"prepend-inner-icon":`mdi-tag`,disabled:v.value||J.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),_(r,{cols:`12`},{default:s(()=>[_(I,{modelValue:Y.description,"onUpdate:modelValue":c[3]||=e=>Y.description=e,label:`Description`,variant:`outlined`,"prepend-inner-icon":`mdi-text`,rows:`3`,disabled:v.value||J.value===`view`,hint:`Optional description of the assessment method`},null,8,[`modelValue`,`disabled`])]),_:1}),_(r,{cols:`12`,md:`6`},{default:s(()=>[_(e,{modelValue:Y.is_active,"onUpdate:modelValue":c[4]||=e=>Y.is_active=e,label:`Active`,color:`primary`,disabled:v.value||J.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`icon`,`mode`,`loading`]),_(N,{modelValue:L.value,"onUpdate:modelValue":c[8]||=e=>L.value=e,"max-width":`800`,scrollable:``},{default:s(()=>[_(D,null,{default:s(()=>[_(k,{class:`d-flex align-center justify-space-between`},{default:s(()=>[f(`div`,pe,[_(a,{class:`mr-2`},{default:s(()=>c[31]||=[g(`mdi-chart-bar`,-1)]),_:1,__:[31]}),f(`span`,me,`Usage Analytics - `+u(q.value?.name),1)]),_(C,{icon:``,variant:`text`,onClick:c[6]||=e=>L.value=!1},{default:s(()=>[_(a,null,{default:s(()=>c[32]||=[g(`mdi-close`,-1)]),_:1,__:[32]})]),_:1})]),_:1}),_(S),_(O,null,{default:s(()=>[q.value?(b(),p(ne,{key:0,method:q.value,onClose:c[7]||=e=>L.value=!1},null,8,[`method`])):m(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),_(N,{modelValue:R.value,"onUpdate:modelValue":c[10]||=e=>R.value=e,"max-width":`400`},{default:s(()=>[_(D,null,{default:s(()=>[_(k,{class:`text-h6`},{default:s(()=>c[33]||=[g(`Confirm Delete`,-1)]),_:1,__:[33]}),_(O,null,{default:s(()=>[g(` Are you sure you want to delete assessment method "`+u(q.value?.name)+`"? This action cannot be undone and will affect all related assessment plans. `,1)]),_:1}),_(A,null,{default:s(()=>[_(x),_(C,{onClick:c[9]||=e=>R.value=!1},{default:s(()=>c[34]||=[g(`Cancel`,-1)]),_:1,__:[34]}),_(C,{color:`error`,loading:T.value,onClick:Ie},{default:s(()=>c[35]||=[g(` Delete `,-1)]),_:1,__:[35]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),_(F,{modelValue:z.value,"onUpdate:modelValue":c[12]||=e=>z.value=e,color:`success`,timeout:`3000`},{actions:s(()=>[_(C,{onClick:c[11]||=e=>z.value=!1},{default:s(()=>c[36]||=[g(`Close`,-1)]),_:1,__:[36]})]),default:s(()=>[g(u(V.value)+` `,1)]),_:1},8,[`modelValue`]),_(F,{modelValue:B.value,"onUpdate:modelValue":c[14]||=e=>B.value=e,color:`error`,timeout:`5000`},{actions:s(()=>[_(C,{onClick:c[13]||=e=>B.value=!1},{default:s(()=>c[37]||=[g(`Close`,-1)]),_:1,__:[37]})]),default:s(()=>[g(u(H.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),J=q;const Y={class:`pa-6`},X={class:`text-center py-8`};var Z=v({__name:`AssessmentPlanningWizard`,props:{courseId:{}},emits:[`close`,`completed`],setup(e){return(e,t)=>(b(),h(`div`,Y,[f(`div`,X,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[0]||=[g(`mdi-wizard-hat`,-1)]),_:1,__:[0]}),t[1]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Assessment Planning Wizard`,-1),t[2]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Step-by-step wizard to create comprehensive assessment plans for the course `,-1),t[3]||=f(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),Q=Z;const he={class:`pa-6`},ge={class:`text-center py-8`};var _e=v({__name:`AssessmentWeightValidation`,props:{courseId:{}},emits:[`close`],setup(e){return(e,t)=>(b(),h(`div`,he,[f(`div`,ge,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[0]||=[g(`mdi-scale-balance`,-1)]),_:1,__:[0]}),t[1]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Assessment Weight Validation`,-1),t[2]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Validate and balance assessment weight distribution across all course assessments `,-1),t[3]||=f(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),ve=_e;const ye={class:`pa-6`},be={class:`text-center py-8`},xe={class:`text-body-2 text-medium-emphasis`};var Se=v({__name:`AssessmentRubricBuilder`,props:{assessmentPlan:{}},emits:[`close`,`updated`],setup(e){return(e,t)=>(b(),h(`div`,ye,[f(`div`,be,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[0]||=[g(`mdi-format-list-checks`,-1)]),_:1,__:[0]}),t[1]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Assessment Rubric Builder`,-1),f(`p`,xe,` Create and manage detailed rubrics for `+u(e.assessmentPlan.assessment_title),1),t[2]||=f(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),Ce=Se;const we={class:`d-flex align-center justify-space-between`},Te={class:`text-h6 font-weight-bold`},Ee={class:`text-subtitle-2 text-medium-emphasis`},De={class:`text-right`},$={class:`text-h6 font-weight-bold`},Oe={class:`d-flex align-center justify-space-between`},ke={class:`text-h4 font-weight-bold`},Ae={class:`d-flex align-center justify-space-between`},je={class:`text-h4 font-weight-bold`},Me={class:`d-flex align-center justify-space-between`},Ne={class:`text-h4 font-weight-bold`},Pe={class:`d-flex align-center justify-space-between`},Fe={class:`text-h4 font-weight-bold`},Ie={class:`font-weight-medium`},Le={class:`text-caption text-medium-emphasis`},Re={key:0},ze={key:0,class:`text-caption`},Be={key:1,class:`text-medium-emphasis`},Ve={class:`d-flex align-center`},He={class:`text-caption font-weight-medium`},Ue={class:`font-weight-medium`},We={key:0},Ge={key:1,class:`text-medium-emphasis`},Ke={class:`d-flex align-center gap-1`},qe={key:3,class:`text-center py-12`},Je={class:`d-flex align-center`},Ye={class:`d-flex align-center`},Xe={class:`d-flex align-center`};var Ze=v({__name:`AssessmentPlanningTab`,setup(e){let c=l(!1),v=l(!1),x=l(!1),w=l(!1),A=l([]),ee=l([]),te=l(0),F=l(null),I=l(null),L=l(null),R=l(``),z=l({}),B=l({page:1,per_page:20,sort_by:`week_number`,sort_order:`asc`}),V=d(()=>A.value.length),H=d(()=>Math.round(A.value.reduce((e,t)=>e+t.weight_percentage,0))),U=d(()=>{let e=new Set(A.value.map(e=>e.cpmk_id));return e.size}),W=d(()=>{if(A.value.length===0)return 0;let e=A.value.reduce((e,t)=>e+t.passing_grade,0);return Math.round(e/A.value.length)}),ne=d(()=>ee.value.map(e=>({title:`${e.code} - ${e.name}`,value:e.id}))),re=[{key:`assessment_title`,title:`Assessment Title`,sortable:!0,type:`text`},{key:`cpmk`,title:`CPMK`,sortable:!1,type:`text`},{key:`assessment_method`,title:`Method`,sortable:!1,type:`text`},{key:`weight_percentage`,title:`Weight`,sortable:!0,type:`text`},{key:`passing_grade`,title:`Passing Grade`,sortable:!0,type:`text`},{key:`due_date`,title:`Due Date`,sortable:!0,type:`date`},{key:`is_active`,title:`Status`,sortable:!0,type:`boolean`}],ie=[{key:`week_number`,label:`Week`,options:Array.from({length:16},(e,t)=>({title:`Week ${t+1}`,value:t+1}))},{key:`assessment_method_type`,label:`Assessment Type`,options:[{title:`Formative`,value:`formatif`},{title:`Summative`,value:`sumatif`}]},{key:`is_active`,label:`Status`,options:[{title:`Active`,value:!0},{title:`Inactive`,value:!1}]}],ae=async()=>{try{let e=await E.getAll({per_page:1e3});ee.value=e.data.data}catch(e){console.error(`Load courses error:`,e)}},G=async()=>{if(F.value){c.value=!0;try{let e={...B.value,search:R.value,...z.value},t=await T.getByCourse(F.value,e);A.value=t.data.data,te.value=t.data.meta?.total||0}catch(e){console.error(`Load assessment plans error:`,e)}finally{c.value=!1}}},oe=async()=>{if(F.value){try{let e=await E.getById(F.value);I.value=e.data}catch(e){console.error(`Load course info error:`,e)}await G()}else I.value=null,A.value=[]},se=()=>{v.value=!0},K=()=>{v.value=!1},ce=()=>{K(),G()},le=()=>{console.log(`Open create modal`)},ue=e=>{console.log(`Open edit modal`,e)},de=e=>{console.log(`Open view modal`,e)},fe=e=>{L.value=e,w.value=!0},pe=e=>{console.log(`Open delete dialog`,e)},me=()=>{w.value=!1,G()},q=e=>{R.value=e,B.value.page=1,G()},J=e=>{z.value=e,B.value.page=1,G()},Y=e=>{B.value={...B.value,page:e.page,per_page:e.itemsPerPage,sort_by:e.sortBy?.[0]?.key||`week_number`,sort_order:e.sortBy?.[0]?.order||`asc`},G()},X=e=>e===`formatif`?`success`:`warning`,Z=e=>e>=30?`error`:e>=20?`warning`:e>=10?`success`:`info`,he=e=>new Date(e).toLocaleDateString(`id-ID`,{day:`numeric`,month:`short`,year:`numeric`});return y(()=>{ae()}),o(F,oe),(e,o)=>(b(),h(`div`,null,[_(n,{class:`mb-6`},{default:s(()=>[_(r,{cols:`12`,md:`8`},{default:s(()=>[_(M,{modelValue:F.value,"onUpdate:modelValue":[o[0]||=e=>F.value=e,oe],items:ne.value,label:`Select Course for Assessment Planning`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open-page-variant`,clearable:``},null,8,[`modelValue`,`items`])]),_:1}),_(r,{cols:`12`,md:`4`,class:`d-flex align-center gap-2`},{default:s(()=>[_(C,{color:`primary`,onClick:se,disabled:!F.value},{default:s(()=>[_(a,{start:``},{default:s(()=>o[9]||=[g(`mdi-wizard-hat`,-1)]),_:1,__:[9]}),o[10]||=g(` Planning Wizard `,-1)]),_:1,__:[10]},8,[`disabled`]),_(C,{color:`secondary`,variant:`outlined`,onClick:o[1]||=e=>x.value=!0,disabled:!F.value},{default:s(()=>[_(a,{start:``},{default:s(()=>o[11]||=[g(`mdi-scale-balance`,-1)]),_:1,__:[11]}),o[12]||=g(` Validate Weights `,-1)]),_:1,__:[12]},8,[`disabled`])]),_:1})]),_:1}),I.value?(b(),p(D,{key:0,class:`mb-6`,variant:`outlined`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,we,[f(`div`,null,[f(`h3`,Te,u(I.value.name),1),f(`p`,Ee,u(I.value.code)+` • `+u(I.value.credits)+` SKS • Semester `+u(I.value.semester),1)]),f(`div`,De,[f(`div`,$,u(V.value),1),o[13]||=f(`div`,{class:`text-caption text-medium-emphasis`},`Assessment Plans`,-1)])])]),_:1})]),_:1})):m(``,!0),F.value?(b(),p(n,{key:1,class:`mb-6`},{default:s(()=>[_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`primary`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,Oe,[f(`div`,null,[o[14]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total Plans`,-1),f(`h2`,ke,u(V.value),1)]),_(a,{size:`48`,color:`primary`},{default:s(()=>o[15]||=[g(`mdi-calendar-clock`,-1)]),_:1,__:[15]})])]),_:1})]),_:1})]),_:1}),_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`success`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,Ae,[f(`div`,null,[o[16]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total Weight`,-1),f(`h2`,je,u(H.value)+`%`,1)]),_(a,{size:`48`,color:`success`},{default:s(()=>o[17]||=[g(`mdi-scale-balance`,-1)]),_:1,__:[17]})])]),_:1})]),_:1})]),_:1}),_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`warning`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,Me,[f(`div`,null,[o[18]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`CPMK Covered`,-1),f(`h2`,Ne,u(U.value),1)]),_(a,{size:`48`,color:`warning`},{default:s(()=>o[19]||=[g(`mdi-target`,-1)]),_:1,__:[19]})])]),_:1})]),_:1})]),_:1}),_(r,{cols:`12`,sm:`6`,md:`3`},{default:s(()=>[_(D,{color:`info`,variant:`tonal`},{default:s(()=>[_(O,null,{default:s(()=>[f(`div`,Pe,[f(`div`,null,[o[20]||=f(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Avg Passing`,-1),f(`h2`,Fe,u(W.value)+`%`,1)]),_(a,{size:`48`,color:`info`},{default:s(()=>o[21]||=[g(`mdi-chart-line`,-1)]),_:1,__:[21]})])]),_:1})]),_:1})]),_:1})]),_:1})):m(``,!0),F.value?(b(),p(t,{key:2,title:`Assessment Plans`,icon:`mdi-calendar-clock`,"item-name":`Assessment Plan`,headers:re,items:A.value,loading:c.value,"total-items":te.value,filters:ie,onAdd:le,onEdit:ue,onDelete:pe,onView:de,onRefresh:G,onSearch:q,onFilter:J,"onUpdate:options":Y},{"item.assessment_title":s(({item:e})=>[f(`div`,null,[f(`div`,Ie,u(e.assessment_title),1),f(`div`,Le,`Week `+u(e.week_number),1)])]),"item.cpmk":s(({item:e})=>[e.cpmk_code?(b(),h(`div`,Re,[_(j,{color:`primary`,variant:`tonal`,size:`small`,class:`mb-1`},{default:s(()=>[g(u(e.cpmk_code),1)]),_:2},1024),e.sub_cpmk_code?(b(),h(`div`,ze,` Sub: `+u(e.sub_cpmk_code),1)):m(``,!0)])):(b(),h(`span`,Be,`-`))]),"item.assessment_method":s(({item:e})=>[_(j,{color:X(e.assessment_method_type),variant:`tonal`,size:`small`},{default:s(()=>[g(u(e.assessment_method_name),1)]),_:2},1032,[`color`])]),"item.weight_percentage":s(({item:e})=>[f(`div`,Ve,[_(i,{"model-value":e.weight_percentage,color:Z(e.weight_percentage),height:`6`,rounded:``,class:`mr-2`,style:{"min-width":`60px`}},null,8,[`model-value`,`color`]),f(`span`,He,u(e.weight_percentage)+`%`,1)])]),"item.passing_grade":s(({item:e})=>[f(`span`,Ue,u(e.passing_grade)+`%`,1)]),"item.due_date":s(({item:e})=>[e.due_date?(b(),h(`div`,We,u(he(e.due_date)),1)):(b(),h(`span`,Ge,`-`))]),"item.actions":s(({item:e})=>[f(`div`,Ke,[_(C,{icon:``,size:`small`,variant:`text`,onClick:t=>de(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>o[22]||=[g(`mdi-eye`,-1)]),_:1,__:[22]}),_(P,{activator:`parent`},{default:s(()=>o[23]||=[g(`View Details`,-1)]),_:1,__:[23]})]),_:2},1032,[`onClick`]),_(C,{icon:``,size:`small`,variant:`text`,onClick:t=>ue(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>o[24]||=[g(`mdi-pencil`,-1)]),_:1,__:[24]}),_(P,{activator:`parent`},{default:s(()=>o[25]||=[g(`Edit Plan`,-1)]),_:1,__:[25]})]),_:2},1032,[`onClick`]),_(C,{icon:``,size:`small`,variant:`text`,onClick:t=>fe(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>o[26]||=[g(`mdi-format-list-checks`,-1)]),_:1,__:[26]}),_(P,{activator:`parent`},{default:s(()=>o[27]||=[g(`Manage Rubric`,-1)]),_:1,__:[27]})]),_:2},1032,[`onClick`]),_(C,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>pe(e)},{default:s(()=>[_(a,{size:`small`},{default:s(()=>o[28]||=[g(`mdi-delete`,-1)]),_:1,__:[28]}),_(P,{activator:`parent`},{default:s(()=>o[29]||=[g(`Delete Plan`,-1)]),_:1,__:[29]})]),_:2},1032,[`onClick`])])]),_:1},8,[`items`,`loading`,`total-items`])):(b(),h(`div`,qe,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>o[30]||=[g(`mdi-calendar-clock`,-1)]),_:1,__:[30]}),o[31]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Select a Course to Start Assessment Planning`,-1),o[32]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Choose a course from the dropdown above to create and manage assessment plans `,-1)])),_(N,{modelValue:v.value,"onUpdate:modelValue":o[2]||=e=>v.value=e,"max-width":`1200`,scrollable:``,persistent:``},{default:s(()=>[_(D,null,{default:s(()=>[_(k,{class:`d-flex align-center justify-space-between`},{default:s(()=>[f(`div`,Je,[_(a,{class:`mr-2`},{default:s(()=>o[33]||=[g(`mdi-wizard-hat`,-1)]),_:1,__:[33]}),o[34]||=f(`span`,{class:`text-h6 font-weight-bold`},`Assessment Planning Wizard`,-1)]),_(C,{icon:``,variant:`text`,onClick:K},{default:s(()=>[_(a,null,{default:s(()=>o[35]||=[g(`mdi-close`,-1)]),_:1,__:[35]})]),_:1})]),_:1}),_(S),_(O,null,{default:s(()=>[F.value?(b(),p(Q,{key:0,"course-id":F.value,onClose:K,onCompleted:ce},null,8,[`course-id`])):m(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),_(N,{modelValue:x.value,"onUpdate:modelValue":o[5]||=e=>x.value=e,"max-width":`800`},{default:s(()=>[_(D,null,{default:s(()=>[_(k,{class:`d-flex align-center justify-space-between`},{default:s(()=>[f(`div`,Ye,[_(a,{class:`mr-2`},{default:s(()=>o[36]||=[g(`mdi-scale-balance`,-1)]),_:1,__:[36]}),o[37]||=f(`span`,{class:`text-h6 font-weight-bold`},`Weight Validation`,-1)]),_(C,{icon:``,variant:`text`,onClick:o[3]||=e=>x.value=!1},{default:s(()=>[_(a,null,{default:s(()=>o[38]||=[g(`mdi-close`,-1)]),_:1,__:[38]})]),_:1})]),_:1}),_(S),_(O,null,{default:s(()=>[F.value?(b(),p(ve,{key:0,"course-id":F.value,onClose:o[4]||=e=>x.value=!1},null,8,[`course-id`])):m(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),_(N,{modelValue:w.value,"onUpdate:modelValue":o[8]||=e=>w.value=e,"max-width":`1000`,scrollable:``},{default:s(()=>[_(D,null,{default:s(()=>[_(k,{class:`d-flex align-center justify-space-between`},{default:s(()=>[f(`div`,Xe,[_(a,{class:`mr-2`},{default:s(()=>o[39]||=[g(`mdi-format-list-checks`,-1)]),_:1,__:[39]}),o[40]||=f(`span`,{class:`text-h6 font-weight-bold`},`Rubric Management`,-1)]),_(C,{icon:``,variant:`text`,onClick:o[6]||=e=>w.value=!1},{default:s(()=>[_(a,null,{default:s(()=>o[41]||=[g(`mdi-close`,-1)]),_:1,__:[41]})]),_:1})]),_:1}),_(S),_(O,null,{default:s(()=>[L.value?(b(),p(Ce,{key:0,"assessment-plan":L.value,onClose:o[7]||=e=>w.value=!1,onUpdated:me},null,8,[`assessment-plan`])):m(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`])]))}}),Qe=Ze;const $e={key:0,class:`text-center py-12`},et={key:1,class:`text-center py-12`};var tt=v({__name:`AssessmentCalendarTab`,setup(e){let t=l([]),i=l(null),o=d(()=>t.value.map(e=>({title:`${e.code} - ${e.name}`,value:e.id}))),c=async()=>{try{let e=await E.getAll({per_page:1e3});t.value=e.data.data}catch(e){console.error(`Load courses error:`,e)}},u=()=>{console.log(`Course changed:`,i.value)},p=()=>{console.log(`Export calendar for course:`,i.value)};return y(()=>{c()}),(e,t)=>(b(),h(`div`,null,[_(n,{class:`mb-6`},{default:s(()=>[_(r,{cols:`12`,md:`8`},{default:s(()=>[_(M,{modelValue:i.value,"onUpdate:modelValue":[t[0]||=e=>i.value=e,u],items:o.value,label:`Select Course for Assessment Calendar`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open-page-variant`,clearable:``},null,8,[`modelValue`,`items`])]),_:1}),_(r,{cols:`12`,md:`4`,class:`d-flex align-center gap-2`},{default:s(()=>[_(C,{color:`primary`,variant:`outlined`,onClick:p,disabled:!i.value},{default:s(()=>[_(a,{start:``},{default:s(()=>t[1]||=[g(`mdi-download`,-1)]),_:1,__:[1]}),t[2]||=g(` Export Calendar `,-1)]),_:1,__:[2]},8,[`disabled`])]),_:1})]),_:1}),i.value?(b(),h(`div`,$e,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[3]||=[g(`mdi-calendar`,-1)]),_:1,__:[3]}),t[4]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Assessment Calendar`,-1),t[5]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Interactive calendar view showing all assessment schedules for the selected course `,-1),t[6]||=f(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])):(b(),h(`div`,et,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[7]||=[g(`mdi-calendar`,-1)]),_:1,__:[7]}),t[8]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Select a Course to View Assessment Calendar`,-1),t[9]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Choose a course from the dropdown above to view the assessment calendar `,-1)]))]))}}),nt=tt;const rt={key:0,class:`text-center py-12`},it={key:1,class:`text-center py-12`};var at=v({__name:`AssessmentAnalyticsTab`,setup(e){let t=l([]),i=l(null),o=d(()=>t.value.map(e=>({title:`${e.code} - ${e.name}`,value:e.id}))),c=async()=>{try{let e=await E.getAll({per_page:1e3});t.value=e.data.data}catch(e){console.error(`Load courses error:`,e)}},u=()=>{console.log(`Course changed:`,i.value)},p=()=>{console.log(`Export report for course:`,i.value)};return y(()=>{c()}),(e,t)=>(b(),h(`div`,null,[_(n,{class:`mb-6`},{default:s(()=>[_(r,{cols:`12`,md:`8`},{default:s(()=>[_(M,{modelValue:i.value,"onUpdate:modelValue":[t[0]||=e=>i.value=e,u],items:o.value,label:`Select Course for Analytics`,variant:`outlined`,"prepend-inner-icon":`mdi-book-open-page-variant`,clearable:``},null,8,[`modelValue`,`items`])]),_:1}),_(r,{cols:`12`,md:`4`,class:`d-flex align-center gap-2`},{default:s(()=>[_(C,{color:`primary`,variant:`outlined`,onClick:p,disabled:!i.value},{default:s(()=>[_(a,{start:``},{default:s(()=>t[1]||=[g(`mdi-file-chart`,-1)]),_:1,__:[1]}),t[2]||=g(` Export Report `,-1)]),_:1,__:[2]},8,[`disabled`])]),_:1})]),_:1}),i.value?(b(),h(`div`,rt,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[3]||=[g(`mdi-chart-line`,-1)]),_:1,__:[3]}),t[4]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Assessment Analytics & Reports`,-1),t[5]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Comprehensive analytics dashboard with performance metrics, grade distributions, and achievement reports `,-1),t[6]||=f(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])):(b(),h(`div`,it,[_(a,{size:`64`,color:`grey-lighten-2`},{default:s(()=>t[7]||=[g(`mdi-chart-line`,-1)]),_:1,__:[7]}),t[8]||=f(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`Select a Course to View Analytics`,-1),t[9]||=f(`p`,{class:`text-body-2 text-medium-emphasis`},` Choose a course from the dropdown above to view assessment analytics and reports `,-1)]))]))}}),ot=at,st=v({__name:`AssessmentsView`,setup(e){let t=l(`methods`);return(e,i)=>(b(),h(`div`,null,[_(n,{class:`mb-6`},{default:s(()=>[_(r,null,{default:s(()=>i[2]||=[f(`h1`,{class:`text-h4 font-weight-bold text-primary`},`Assessment System & Planning`,-1),f(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Comprehensive assessment management with methods, planning, and analytics `,-1)]),_:1,__:[2]})]),_:1}),_(L,{modelValue:t.value,"onUpdate:modelValue":i[0]||=e=>t.value=e,color:`primary`,class:`mb-6`},{default:s(()=>[_(B,{value:`methods`},{default:s(()=>[_(a,{start:``},{default:s(()=>i[3]||=[g(`mdi-clipboard-list`,-1)]),_:1,__:[3]}),i[4]||=g(` Assessment Methods `,-1)]),_:1,__:[4]}),_(B,{value:`planning`},{default:s(()=>[_(a,{start:``},{default:s(()=>i[5]||=[g(`mdi-calendar-clock`,-1)]),_:1,__:[5]}),i[6]||=g(` Assessment Planning `,-1)]),_:1,__:[6]}),_(B,{value:`calendar`},{default:s(()=>[_(a,{start:``},{default:s(()=>i[7]||=[g(`mdi-calendar`,-1)]),_:1,__:[7]}),i[8]||=g(` Assessment Calendar `,-1)]),_:1,__:[8]}),_(B,{value:`analytics`},{default:s(()=>[_(a,{start:``},{default:s(()=>i[9]||=[g(`mdi-chart-line`,-1)]),_:1,__:[9]}),i[10]||=g(` Analytics & Reports `,-1)]),_:1,__:[10]})]),_:1},8,[`modelValue`]),_(z,{modelValue:t.value,"onUpdate:modelValue":i[1]||=e=>t.value=e},{default:s(()=>[_(R,{value:`methods`},{default:s(()=>[_(J)]),_:1}),_(R,{value:`planning`},{default:s(()=>[_(Qe)]),_:1}),_(R,{value:`calendar`},{default:s(()=>[_(nt)]),_:1}),_(R,{value:`analytics`},{default:s(()=>[_(ot)]),_:1})]),_:1},8,[`modelValue`])]))}}),ct=st;export{ct as default};