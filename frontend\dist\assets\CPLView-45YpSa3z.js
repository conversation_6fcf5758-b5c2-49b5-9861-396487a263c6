import{b as e,c as t}from"./VSwitch-Bh_Rc-In.js";import{b as n,c as r}from"./VRow-Cvqvybmt.js";import{I as i,bF as a,bJ as ee,bK as o,bS as s,bf as c,bg as l,bh as u,bi as d,bj as f,bl as p,bm as m,bn as h,bv as te,bx as g,q as ne,s as _,u as v}from"./index-BSnscBhv.js";import{e as y,j as re}from"./api-BWRuf0Vj.js";import{b,c as x,d as S,e as ie}from"./VCard-DVRc-Pxh.js";import{b as C}from"./VChip-CBN0Kf2u.js";import{b as w}from"./VSelect-DqM1bu6y.js";import{b as T}from"./VDialog-VHlGBbps.js";import{b as E}from"./VTextField-BU8lnKH2.js";import{b as ae,c as D}from"./FormModal-CAo97PhI.js";import"./VForm-CDHrkI-n.js";import{b as O}from"./VSnackbar-KpoxlJmd.js";import{b as k}from"./VTextarea-BciMMY-M.js";const A={class:`pa-6`},j={class:`text-center py-8`},M={class:`text-body-2 text-medium-emphasis`};var N=h({__name:`CPLMappingView`,props:{cpl:{}},emits:[`close`,`updated`],setup(e){return(e,t)=>(g(),f(`div`,A,[l(`div`,j,[m(i,{size:`64`,color:`grey-lighten-2`},{default:a(()=>t[0]||=[p(`mdi-vector-link`,-1)]),_:1,__:[0]}),t[1]||=l(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`CPL Mapping View`,-1),l(`p`,M,` View CPMK mappings for `+s(e.cpl.code),1),t[2]||=l(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),oe=N;const P={class:`pa-6`},F={class:`text-center py-8`};var I=h({__name:`CPLMappingMatrix`,props:{studyProgramId:{}},emits:[`close`],setup(e){return(e,t)=>(g(),f(`div`,P,[l(`div`,F,[m(i,{size:`64`,color:`grey-lighten-2`},{default:a(()=>t[0]||=[p(`mdi-matrix`,-1)]),_:1,__:[0]}),t[1]||=l(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`CPL-CPMK Mapping Matrix`,-1),t[2]||=l(`p`,{class:`text-body-2 text-medium-emphasis`},` Interactive matrix showing relationships between CPL and CPMK `,-1),t[3]||=l(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),se=I;const L={class:`pa-6`},R={class:`text-center py-8`};var z=h({__name:`CPLAchievementReport`,props:{studyProgramId:{}},emits:[`close`],setup(e){return(e,t)=>(g(),f(`div`,L,[l(`div`,R,[m(i,{size:`64`,color:`grey-lighten-2`},{default:a(()=>t[0]||=[p(`mdi-chart-line`,-1)]),_:1,__:[0]}),t[1]||=l(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`CPL Achievement Report`,-1),t[2]||=l(`p`,{class:`text-body-2 text-medium-emphasis`},` Analytics and reports on CPL achievement for the study program `,-1),t[3]||=l(`p`,{class:`text-caption text-medium-emphasis mt-2`},` This feature will be fully implemented in the next development phase `,-1)])]))}}),ce=z;const le={class:`d-flex align-center justify-space-between`},ue={class:`text-h4 font-weight-bold`},de={class:`d-flex align-center justify-space-between`},fe={class:`text-h4 font-weight-bold`},pe={class:`d-flex align-center justify-space-between`},me={class:`text-h4 font-weight-bold`},he={class:`d-flex align-center justify-space-between`},ge={class:`text-h4 font-weight-bold`},_e={key:0},ve={class:`font-weight-medium`},ye={class:`text-caption text-medium-emphasis`},be={key:1,class:`text-medium-emphasis`},xe={class:`text-truncate`,style:{"max-width":`300px`}},Se={class:`d-flex align-center gap-1`},Ce={class:`d-flex align-center`},we={class:`text-h6 font-weight-bold`},Te={class:`d-flex align-center`},Ee={class:`d-flex align-center`};var B=h({__name:`CPLView`,setup(h){let A=o(!1),j=o(!1),M=o(!1),N=o(!1),P=o(!1),F=o(!1),I=o(!1),L=o(!1),R=o(!1),z=o(!1),B=o(``),V=o(``),H=o([]),U=o([]),W=o(0),G=o(null),K=o(null),q=o(`create`),J=ee({code:``,study_program_id:null,category:`knowledge`,description:``,learning_outcome:``,achievement_target:null,measurement_criteria:``,is_active:!0}),Y=o(``),X=o({}),Z=o({page:1,per_page:20,sort_by:`created_at`,sort_order:`desc`}),De=c(()=>H.value.length),Oe=c(()=>H.value.filter(e=>e.category===`attitude`).length),ke=c(()=>H.value.filter(e=>e.category===`knowledge`).length),Ae=c(()=>H.value.filter(e=>e.category===`general_skills`||e.category===`specific_skills`).length),je=[{key:`code`,title:`Code`,sortable:!0,type:`text`},{key:`category`,title:`Category`,sortable:!0,type:`text`},{key:`description`,title:`Description`,sortable:!1,type:`text`},{key:`study_program`,title:`Study Program`,sortable:!1,type:`text`},{key:`is_active`,title:`Status`,sortable:!0,type:`boolean`},{key:`created_at`,title:`Created`,sortable:!0,type:`date`}],Q=[{key:`category`,label:`Category`,options:[{title:`Sikap (Attitude)`,value:`attitude`},{title:`Pengetahuan (Knowledge)`,value:`knowledge`},{title:`Keterampilan Umum (General Skills)`,value:`general_skills`},{title:`Keterampilan Khusus (Specific Skills)`,value:`specific_skills`}]},{key:`study_program_id`,label:`Study Program`,options:[]},{key:`is_active`,label:`Status`,options:[{title:`Active`,value:!0},{title:`Inactive`,value:!1}]}],Me=c(()=>{switch(q.value){case`create`:return`Create New CPL`;case`edit`:return`Edit CPL`;case`view`:return`View CPL Details`;default:return`CPL Form`}}),Ne=c(()=>{switch(q.value){case`create`:return`mdi-target-plus`;case`edit`:return`mdi-target-edit`;case`view`:return`mdi-target`;default:return`mdi-target`}}),Pe=c(()=>U.value.map(e=>({title:`${e.name} (${e.code})`,value:e.id}))),Fe=[{title:`Sikap (Attitude)`,value:`attitude`,subtitle:`Professional ethics, responsibility, integrity`},{title:`Pengetahuan (Knowledge)`,value:`knowledge`,subtitle:`Theoretical foundation, domain expertise`},{title:`Keterampilan Umum (General Skills)`,value:`general_skills`,subtitle:`Communication, teamwork, leadership`},{title:`Keterampilan Khusus (Specific Skills)`,value:`specific_skills`,subtitle:`Technical competencies, problem-solving`}],Ie=[e=>!!e||`CPL code is required`,e=>e.length>=3||`CPL code must be at least 3 characters`,e=>/^CPL-\d{2}$/.test(e)||`CPL code format: CPL-01, CPL-02, etc.`],Le=[e=>!!e||`Study program is required`],Re=[e=>!!e||`Category is required`],ze=[e=>!!e||`Description is required`,e=>e.length>=10||`Description must be at least 10 characters`],Be=[e=>!!e||`Learning outcome statement is required`,e=>e.length>=20||`Learning outcome must be at least 20 characters`],Ve=[e=>e==null||e>=0&&e<=100||`Achievement target must be between 0 and 100`],$=async()=>{A.value=!0;try{let e={...Z.value,search:Y.value,...X.value},t=await y.getAll(e);H.value=t.data.data,W.value=t.data.meta?.total||0}catch(e){V.value=`Failed to load CPLs`,z.value=!0,console.error(`Load CPLs error:`,e)}finally{A.value=!1}},He=async()=>{try{let e=await re.getAll({per_page:100});U.value=e.data.data,Q[1].options=U.value.map(e=>({title:e.name,value:e.id}))}catch(e){console.error(`Load study programs error:`,e)}},Ue=()=>{K.value?(X.value.study_program_id=K.value,$()):(delete X.value.study_program_id,$())},We=()=>{q.value=`create`,Ye(),N.value=!0},Ge=e=>{q.value=`edit`,G.value=e,Xe(e),N.value=!0},Ke=e=>{q.value=`view`,G.value=e,Xe(e),N.value=!0},qe=e=>{G.value=e,P.value=!0},Je=e=>{G.value=e,L.value=!0},Ye=()=>{Object.assign(J,{code:``,study_program_id:K.value,category:`knowledge`,description:``,learning_outcome:``,achievement_target:null,measurement_criteria:``,is_active:!0})},Xe=e=>{Object.assign(J,{code:e.code,study_program_id:e.study_program_id,category:e.category,description:e.description,learning_outcome:e.learning_outcome,achievement_target:e.achievement_target,measurement_criteria:e.measurement_criteria||``,is_active:e.is_active})},Ze=async()=>{j.value=!0;try{q.value===`create`?(await y.create(J),B.value=`CPL created successfully!`):q.value===`edit`&&G.value&&(await y.update(G.value.id,J),B.value=`CPL updated successfully!`),R.value=!0,$e(),await $()}catch(e){V.value=e.response?.data?.message||`Operation failed`,z.value=!0}finally{j.value=!1}},Qe=async()=>{if(G.value){M.value=!0;try{await y.delete(G.value.id),B.value=`CPL deleted successfully!`,R.value=!0,L.value=!1,await $()}catch(e){V.value=e.response?.data?.message||`Delete failed`,z.value=!0}finally{M.value=!1}}},$e=()=>{N.value=!1,G.value=null,Ye()},et=e=>{Y.value=e,Z.value.page=1,$()},tt=e=>{X.value=e,Z.value.page=1,$()},nt=e=>{Z.value={...Z.value,page:e.page,per_page:e.itemsPerPage,sort_by:e.sortBy?.[0]?.key||`created_at`,sort_order:e.sortBy?.[0]?.order||`desc`},$()},rt=e=>{let t={attitude:`success`,knowledge:`info`,general_skills:`warning`,specific_skills:`secondary`};return t[e]||`primary`},it=e=>{let t={attitude:`mdi-heart`,knowledge:`mdi-brain`,general_skills:`mdi-account-group`,specific_skills:`mdi-tools`};return t[e]||`mdi-target`},at=e=>{let t={attitude:`Sikap`,knowledge:`Pengetahuan`,general_skills:`Keterampilan Umum`,specific_skills:`Keterampilan Khusus`};return t[e]||e};return te(async()=>{await Promise.all([He(),$()])}),(ee,o)=>(g(),f(`div`,null,[m(n,{class:`mb-6`},{default:a(()=>[m(r,null,{default:a(()=>o[27]||=[l(`h1`,{class:`text-h4 font-weight-bold text-primary`},`CPL Management`,-1),l(`p`,{class:`text-subtitle-1 text-medium-emphasis`},` Manage Graduate Learning Outcomes (Capaian Pembelajaran Lulusan) `,-1)]),_:1,__:[27]})]),_:1}),m(n,{class:`mb-6`},{default:a(()=>[m(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[m(b,{color:`primary`,variant:`tonal`},{default:a(()=>[m(x,null,{default:a(()=>[l(`div`,le,[l(`div`,null,[o[28]||=l(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Total CPL`,-1),l(`h2`,ue,s(De.value),1)]),m(i,{size:`48`,color:`primary`},{default:a(()=>o[29]||=[p(`mdi-target`,-1)]),_:1,__:[29]})])]),_:1})]),_:1})]),_:1}),m(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[m(b,{color:`success`,variant:`tonal`},{default:a(()=>[m(x,null,{default:a(()=>[l(`div`,de,[l(`div`,null,[o[30]||=l(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Sikap`,-1),l(`h2`,fe,s(Oe.value),1)]),m(i,{size:`48`,color:`success`},{default:a(()=>o[31]||=[p(`mdi-heart`,-1)]),_:1,__:[31]})])]),_:1})]),_:1})]),_:1}),m(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[m(b,{color:`info`,variant:`tonal`},{default:a(()=>[m(x,null,{default:a(()=>[l(`div`,pe,[l(`div`,null,[o[32]||=l(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Pengetahuan`,-1),l(`h2`,me,s(ke.value),1)]),m(i,{size:`48`,color:`info`},{default:a(()=>o[33]||=[p(`mdi-brain`,-1)]),_:1,__:[33]})])]),_:1})]),_:1})]),_:1}),m(r,{cols:`12`,sm:`6`,md:`3`},{default:a(()=>[m(b,{color:`warning`,variant:`tonal`},{default:a(()=>[m(x,null,{default:a(()=>[l(`div`,he,[l(`div`,null,[o[34]||=l(`p`,{class:`text-subtitle-2 text-medium-emphasis mb-1`},`Keterampilan`,-1),l(`h2`,ge,s(Ae.value),1)]),m(i,{size:`48`,color:`warning`},{default:a(()=>o[35]||=[p(`mdi-tools`,-1)]),_:1,__:[35]})])]),_:1})]),_:1})]),_:1})]),_:1}),m(n,{class:`mb-4`},{default:a(()=>[m(r,{cols:`12`,md:`6`},{default:a(()=>[m(w,{modelValue:K.value,"onUpdate:modelValue":[o[0]||=e=>K.value=e,Ue],items:Pe.value,label:`Select Study Program`,variant:`outlined`,"prepend-inner-icon":`mdi-school`,clearable:``},null,8,[`modelValue`,`items`])]),_:1}),m(r,{cols:`12`,md:`6`,class:`d-flex align-center gap-2`},{default:a(()=>[m(v,{color:`secondary`,variant:`outlined`,onClick:o[1]||=e=>F.value=!0,disabled:!K.value},{default:a(()=>[m(i,{start:``},{default:a(()=>o[36]||=[p(`mdi-matrix`,-1)]),_:1,__:[36]}),o[37]||=p(` View Mapping Matrix `,-1)]),_:1,__:[37]},8,[`disabled`]),m(v,{color:`info`,variant:`outlined`,onClick:o[2]||=e=>I.value=!0,disabled:!K.value},{default:a(()=>[m(i,{start:``},{default:a(()=>o[38]||=[p(`mdi-chart-line`,-1)]),_:1,__:[38]}),o[39]||=p(` Achievement Report `,-1)]),_:1,__:[39]},8,[`disabled`])]),_:1})]),_:1}),m(t,{title:`Graduate Learning Outcomes (CPL)`,icon:`mdi-target`,"item-name":`CPL`,headers:je,items:H.value,loading:A.value,"total-items":W.value,filters:Q,onAdd:We,onEdit:Ge,onDelete:Je,onView:Ke,onRefresh:$,onSearch:et,onFilter:tt,"onUpdate:options":nt},{"item.code":a(({item:e})=>[m(C,{color:`primary`,variant:`tonal`,size:`small`,class:`font-weight-bold`},{default:a(()=>[p(s(e.code),1)]),_:2},1024)]),"item.category":a(({item:e})=>[m(C,{color:rt(e.category),variant:`tonal`,size:`small`},{default:a(()=>[m(i,{start:``,size:`small`},{default:a(()=>[p(s(it(e.category)),1)]),_:2},1024),p(` `+s(at(e.category)),1)]),_:2},1032,[`color`])]),"item.study_program":a(({item:e})=>[e.study_program_name?(g(),f(`div`,_e,[l(`div`,ve,s(e.study_program_name),1),l(`div`,ye,s(e.study_program_code),1)])):(g(),f(`span`,be,`-`))]),"item.description":a(({item:e})=>[l(`div`,xe,s(e.description),1)]),"item.actions":a(({item:e})=>[l(`div`,Se,[m(v,{icon:``,size:`small`,variant:`text`,onClick:t=>Ke(e)},{default:a(()=>[m(i,{size:`small`},{default:a(()=>o[40]||=[p(`mdi-eye`,-1)]),_:1,__:[40]}),m(D,{activator:`parent`},{default:a(()=>o[41]||=[p(`View Details`,-1)]),_:1,__:[41]})]),_:2},1032,[`onClick`]),m(v,{icon:``,size:`small`,variant:`text`,onClick:t=>Ge(e)},{default:a(()=>[m(i,{size:`small`},{default:a(()=>o[42]||=[p(`mdi-pencil`,-1)]),_:1,__:[42]}),m(D,{activator:`parent`},{default:a(()=>o[43]||=[p(`Edit CPL`,-1)]),_:1,__:[43]})]),_:2},1032,[`onClick`]),m(v,{icon:``,size:`small`,variant:`text`,onClick:t=>qe(e)},{default:a(()=>[m(i,{size:`small`},{default:a(()=>o[44]||=[p(`mdi-vector-link`,-1)]),_:1,__:[44]}),m(D,{activator:`parent`},{default:a(()=>o[45]||=[p(`View CPMK Mapping`,-1)]),_:1,__:[45]})]),_:2},1032,[`onClick`]),m(v,{icon:``,size:`small`,variant:`text`,color:`error`,onClick:t=>Je(e)},{default:a(()=>[m(i,{size:`small`},{default:a(()=>o[46]||=[p(`mdi-delete`,-1)]),_:1,__:[46]}),m(D,{activator:`parent`},{default:a(()=>o[47]||=[p(`Delete CPL`,-1)]),_:1,__:[47]})]),_:2},1032,[`onClick`])])]),_:1},8,[`items`,`loading`,`total-items`]),m(ae,{modelValue:N.value,"onUpdate:modelValue":o[11]||=e=>N.value=e,title:Me.value,icon:Ne.value,mode:q.value,loading:j.value,"max-width":`800`,onSubmit:Ze,onClose:$e},{default:a(()=>[m(n,null,{default:a(()=>[m(r,{cols:`12`,md:`6`},{default:a(()=>[m(E,{modelValue:J.code,"onUpdate:modelValue":o[3]||=e=>J.code=e,rules:Ie,label:`CPL Code *`,variant:`outlined`,"prepend-inner-icon":`mdi-identifier`,disabled:j.value||q.value===`view`,placeholder:`e.g., CPL-01`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(w,{modelValue:J.study_program_id,"onUpdate:modelValue":o[4]||=e=>J.study_program_id=e,items:Pe.value,rules:Le,label:`Study Program *`,variant:`outlined`,"prepend-inner-icon":`mdi-school`,disabled:j.value||q.value===`view`},null,8,[`modelValue`,`items`,`disabled`])]),_:1}),m(r,{cols:`12`},{default:a(()=>[m(w,{modelValue:J.category,"onUpdate:modelValue":o[5]||=e=>J.category=e,items:Fe,rules:Re,label:`CPL Category *`,variant:`outlined`,"prepend-inner-icon":`mdi-tag`,disabled:j.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`},{default:a(()=>[m(k,{modelValue:J.description,"onUpdate:modelValue":o[6]||=e=>J.description=e,rules:ze,label:`Description *`,variant:`outlined`,"prepend-inner-icon":`mdi-text`,rows:`3`,disabled:j.value||q.value===`view`,hint:`Brief description of the learning outcome`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`},{default:a(()=>[m(k,{modelValue:J.learning_outcome,"onUpdate:modelValue":o[7]||=e=>J.learning_outcome=e,rules:Be,label:`Learning Outcome Statement *`,variant:`outlined`,"prepend-inner-icon":`mdi-target`,rows:`4`,disabled:j.value||q.value===`view`,hint:`Detailed, measurable learning outcome statement`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(E,{modelValue:J.achievement_target,"onUpdate:modelValue":o[8]||=e=>J.achievement_target=e,rules:Ve,label:`Achievement Target`,type:`number`,variant:`outlined`,"prepend-inner-icon":`mdi-bullseye`,suffix:`%`,disabled:j.value||q.value===`view`,min:`0`,max:`100`,hint:`Target achievement percentage`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`,md:`6`},{default:a(()=>[m(e,{modelValue:J.is_active,"onUpdate:modelValue":o[9]||=e=>J.is_active=e,label:`Active`,color:`primary`,disabled:j.value||q.value===`view`},null,8,[`modelValue`,`disabled`])]),_:1}),m(r,{cols:`12`},{default:a(()=>[m(k,{modelValue:J.measurement_criteria,"onUpdate:modelValue":o[10]||=e=>J.measurement_criteria=e,label:`Measurement Criteria`,variant:`outlined`,"prepend-inner-icon":`mdi-ruler`,rows:`3`,disabled:j.value||q.value===`view`,hint:`Criteria for measuring achievement of this CPL`},null,8,[`modelValue`,`disabled`])]),_:1})]),_:1})]),_:1},8,[`modelValue`,`title`,`icon`,`mode`,`loading`]),m(T,{modelValue:P.value,"onUpdate:modelValue":o[14]||=e=>P.value=e,"max-width":`1200`,scrollable:``},{default:a(()=>[m(b,null,{default:a(()=>[m(S,{class:`d-flex align-center justify-space-between`},{default:a(()=>[l(`div`,Ce,[m(i,{class:`mr-2`},{default:a(()=>o[48]||=[p(`mdi-vector-link`,-1)]),_:1,__:[48]}),l(`span`,we,`CPMK Mapping for `+s(G.value?.code),1)]),m(v,{icon:``,variant:`text`,onClick:o[12]||=e=>P.value=!1},{default:a(()=>[m(i,null,{default:a(()=>o[49]||=[p(`mdi-close`,-1)]),_:1,__:[49]})]),_:1})]),_:1}),m(_),m(x,null,{default:a(()=>[G.value?(g(),u(oe,{key:0,cpl:G.value,onClose:o[13]||=e=>P.value=!1},null,8,[`cpl`])):d(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(T,{modelValue:F.value,"onUpdate:modelValue":o[17]||=e=>F.value=e,"max-width":`1400`,scrollable:``},{default:a(()=>[m(b,null,{default:a(()=>[m(S,{class:`d-flex align-center justify-space-between`},{default:a(()=>[l(`div`,Te,[m(i,{class:`mr-2`},{default:a(()=>o[50]||=[p(`mdi-matrix`,-1)]),_:1,__:[50]}),o[51]||=l(`span`,{class:`text-h6 font-weight-bold`},`CPL-CPMK Mapping Matrix`,-1)]),m(v,{icon:``,variant:`text`,onClick:o[15]||=e=>F.value=!1},{default:a(()=>[m(i,null,{default:a(()=>o[52]||=[p(`mdi-close`,-1)]),_:1,__:[52]})]),_:1})]),_:1}),m(_),m(x,null,{default:a(()=>[K.value?(g(),u(se,{key:0,"study-program-id":K.value,onClose:o[16]||=e=>F.value=!1},null,8,[`study-program-id`])):d(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(T,{modelValue:I.value,"onUpdate:modelValue":o[20]||=e=>I.value=e,"max-width":`1200`,scrollable:``},{default:a(()=>[m(b,null,{default:a(()=>[m(S,{class:`d-flex align-center justify-space-between`},{default:a(()=>[l(`div`,Ee,[m(i,{class:`mr-2`},{default:a(()=>o[53]||=[p(`mdi-chart-line`,-1)]),_:1,__:[53]}),o[54]||=l(`span`,{class:`text-h6 font-weight-bold`},`CPL Achievement Report`,-1)]),m(v,{icon:``,variant:`text`,onClick:o[18]||=e=>I.value=!1},{default:a(()=>[m(i,null,{default:a(()=>o[55]||=[p(`mdi-close`,-1)]),_:1,__:[55]})]),_:1})]),_:1}),m(_),m(x,null,{default:a(()=>[K.value?(g(),u(ce,{key:0,"study-program-id":K.value,onClose:o[19]||=e=>I.value=!1},null,8,[`study-program-id`])):d(``,!0)]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(T,{modelValue:L.value,"onUpdate:modelValue":o[22]||=e=>L.value=e,"max-width":`400`},{default:a(()=>[m(b,null,{default:a(()=>[m(S,{class:`text-h6`},{default:a(()=>o[56]||=[p(`Confirm Delete`,-1)]),_:1,__:[56]}),m(x,null,{default:a(()=>[p(` Are you sure you want to delete CPL "`+s(G.value?.code)+`"? This action cannot be undone and will also remove all CPMK mappings. `,1)]),_:1}),m(ie,null,{default:a(()=>[m(ne),m(v,{onClick:o[21]||=e=>L.value=!1},{default:a(()=>o[57]||=[p(`Cancel`,-1)]),_:1,__:[57]}),m(v,{color:`error`,loading:M.value,onClick:Qe},{default:a(()=>o[58]||=[p(` Delete `,-1)]),_:1,__:[58]},8,[`loading`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),m(O,{modelValue:R.value,"onUpdate:modelValue":o[24]||=e=>R.value=e,color:`success`,timeout:`3000`},{actions:a(()=>[m(v,{onClick:o[23]||=e=>R.value=!1},{default:a(()=>o[59]||=[p(`Close`,-1)]),_:1,__:[59]})]),default:a(()=>[p(s(B.value)+` `,1)]),_:1},8,[`modelValue`]),m(O,{modelValue:z.value,"onUpdate:modelValue":o[26]||=e=>z.value=e,color:`error`,timeout:`5000`},{actions:a(()=>[m(v,{onClick:o[25]||=e=>z.value=!1},{default:a(()=>o[60]||=[p(`Close`,-1)]),_:1,__:[60]})]),default:a(()=>[p(s(V.value)+` `,1)]),_:1},8,[`modelValue`])]))}}),V=B;export{V as default};