<?php

/**
 * Database Connectivity Tests
 * 
 * Tests database connection, migrations, seeders, and table structure
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('Database Connectivity Tests');

$tests = [
    'database_connection' => 'Database Connection',
    'database_service' => 'Database Service Status',
    'migration_status' => 'Migration Status',
    'table_structure' => 'Table Structure Validation',
    'seeder_data' => 'Seeder Data Verification',
    'foreign_keys' => 'Foreign Key Constraints',
    'database_performance' => 'Database Performance'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        switch ($testKey) {
            case 'database_service':
                $dbConfig = $config['database'];
                $host = $dbConfig['hostname'];
                $port = $dbConfig['port'];
                
                $testPassed = $testRunner->isServiceRunning($host, $port);
                $message = $testPassed 
                    ? "Database service is running on {$host}:{$port}"
                    : "Database service is not accessible on {$host}:{$port}";
                $details = [
                    'host' => $host,
                    'port' => $port,
                    'driver' => $dbConfig['DBDriver'],
                    'database' => $dbConfig['database']
                ];
                break;

            case 'database_connection':
                $dbConfig = $config['database'];
                
                try {
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                        PDO::ATTR_TIMEOUT => 5,
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    // Test a simple query
                    $stmt = $pdo->query("SELECT version() as version, current_database() as database");
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    $testPassed = true;
                    $message = "Successfully connected to database: {$result['database']}";
                    $details = [
                        'database_version' => $result['version'],
                        'current_database' => $result['database'],
                        'connection_successful' => true
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Database connection failed: " . $e->getMessage();
                    $details = [
                        'error' => $e->getMessage(),
                        'connection_successful' => false
                    ];
                }
                break;

            case 'migration_status':
                // Check if migrations table exists and get migration status
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
                    
                    // Check if migrations table exists
                    $stmt = $pdo->query("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'migrations')");
                    $migrationsTableExists = $stmt->fetchColumn();
                    
                    if ($migrationsTableExists) {
                        // Get migration count
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM migrations");
                        $migrationCount = $stmt->fetchColumn();
                        
                        // Get latest migration
                        $stmt = $pdo->query("SELECT version, filename FROM migrations ORDER BY version DESC LIMIT 1");
                        $latestMigration = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        $testPassed = $migrationCount > 0;
                        $message = $testPassed 
                            ? "Migrations applied successfully ({$migrationCount} migrations)"
                            : "No migrations found in database";
                        $details = [
                            'migrations_table_exists' => true,
                            'migration_count' => $migrationCount,
                            'latest_migration' => $latestMigration
                        ];
                    } else {
                        $testPassed = false;
                        $message = "Migrations table does not exist";
                        $details = [
                            'migrations_table_exists' => false,
                            'migration_count' => 0
                        ];
                    }
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Failed to check migration status: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'table_structure':
                // Check if required tables exist
                $requiredTables = [
                    'roles', 'users', 'faculties', 'study_programs', 
                    'cpl', 'courses', 'course_references', 'course_topics',
                    'cpmk', 'cpmk_cpl_relations', 'sub_cpmk', 
                    'assessment_methods', 'assessment_plans'
                ];
                
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
                    
                    $existingTables = [];
                    $missingTables = [];
                    
                    foreach ($requiredTables as $table) {
                        $stmt = $pdo->prepare("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)");
                        $stmt->execute([$table]);
                        $exists = $stmt->fetchColumn();
                        
                        if ($exists) {
                            $existingTables[] = $table;
                        } else {
                            $missingTables[] = $table;
                        }
                    }
                    
                    $testPassed = empty($missingTables);
                    $message = $testPassed 
                        ? "All required tables exist (" . count($existingTables) . "/" . count($requiredTables) . ")"
                        : "Missing tables: " . implode(', ', $missingTables);
                    $details = [
                        'required_tables' => $requiredTables,
                        'existing_tables' => $existingTables,
                        'missing_tables' => $missingTables
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Failed to check table structure: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'seeder_data':
                // Check if seeder data exists
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
                    
                    $tableData = [];
                    $tablesToCheck = ['roles', 'users', 'faculties', 'study_programs', 'assessment_methods'];
                    
                    foreach ($tablesToCheck as $table) {
                        try {
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                            $count = $stmt->fetchColumn();
                            $tableData[$table] = $count;
                        } catch (PDOException $e) {
                            $tableData[$table] = 'Error: ' . $e->getMessage();
                        }
                    }
                    
                    $hasData = array_sum(array_filter($tableData, 'is_numeric')) > 0;
                    $testPassed = $hasData;
                    $message = $testPassed 
                        ? "Seeder data found in database tables"
                        : "No seeder data found in database tables";
                    $details = [
                        'table_counts' => $tableData,
                        'has_data' => $hasData
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Failed to check seeder data: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'foreign_keys':
                // Check foreign key constraints
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
                    
                    $stmt = $pdo->query("
                        SELECT 
                            tc.table_name, 
                            kcu.column_name, 
                            ccu.table_name AS foreign_table_name,
                            ccu.column_name AS foreign_column_name 
                        FROM 
                            information_schema.table_constraints AS tc 
                            JOIN information_schema.key_column_usage AS kcu
                              ON tc.constraint_name = kcu.constraint_name
                            JOIN information_schema.constraint_column_usage AS ccu
                              ON ccu.constraint_name = tc.constraint_name
                        WHERE constraint_type = 'FOREIGN KEY'
                        ORDER BY tc.table_name
                    ");
                    
                    $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    $fkCount = count($foreignKeys);
                    
                    $testPassed = $fkCount > 0;
                    $message = $testPassed 
                        ? "Foreign key constraints found ({$fkCount} constraints)"
                        : "No foreign key constraints found";
                    $details = [
                        'foreign_key_count' => $fkCount,
                        'foreign_keys' => array_slice($foreignKeys, 0, 10) // Limit to first 10 for brevity
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Failed to check foreign keys: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'database_performance':
                // Test database performance with a simple query
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
                    
                    $queryStartTime = microtime(true);
                    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'");
                    $tableCount = $stmt->fetchColumn();
                    $queryDuration = round((microtime(true) - $queryStartTime) * 1000);
                    
                    $performanceThreshold = 100; // ms
                    $testPassed = $queryDuration < $performanceThreshold;
                    $message = $testPassed 
                        ? "Database performance is good ({$queryDuration}ms for table count query)"
                        : "Database performance is slow ({$queryDuration}ms for table count query)";
                    $details = [
                        'query_duration_ms' => $queryDuration,
                        'performance_threshold_ms' => $performanceThreshold,
                        'table_count' => $tableCount,
                        'performance_rating' => $queryDuration < 50 ? 'Excellent' : ($queryDuration < 100 ? 'Good' : 'Needs Improvement')
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Failed to test database performance: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('Database Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests === $totalTests) {
    $testRunner->logSuccess("All database tests passed! Database is ready for application testing.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("Database tests failed! {$failedTests} test(s) need attention.");
    return false;
}
