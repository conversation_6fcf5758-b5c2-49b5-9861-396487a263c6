import{A as e,E as t,F as n,Q as r,R as i,S as a,a3 as o,a4 as s,a9 as c,aC as l,aH as u,ak as d,al as f,an as p,ao as m,aw as h,ax as g,b4 as _,b6 as v,b9 as y,bD as b,bE as x,bI as S,bK as C,bL as w,bf as T,bg as E,bm as D,bp as O,bq as k,br as A,bv as j,d as M,e as N,f as P,g as F,z as I}from"./index-BSnscBhv.js";function L(e){let t=w(e()),n=-1;function r(){clearInterval(n)}function i(){r(),A(()=>t.value=e())}function a(i){let a=i?getComputedStyle(i):{transitionDuration:.2},o=parseFloat(a.transitionDuration)*1e3||200;if(r(),t.value<=0)return;let s=performance.now();n=window.setInterval(()=>{let n=performance.now()-s+o;t.value=Math.max(e()-n,0),t.value<=0&&r()},o)}return S(r),{clear:r,time:t,start:a,reset:i}}const R=u({multiLine:Boolean,text:String,timer:[Boolean,String],timeout:{type:[Number,String],default:5e3},vertical:Boolean,...n({location:`bottom`}),...I(),...o(),...i(),...p(),..._(P({transition:`v-snackbar-transition`}),[`persistent`,`noClickAnimation`,`scrim`,`scrollStrategy`])},`VSnackbar`),z=l()({name:`VSnackbar`,props:R(),emits:{"update:modelValue":e=>!0},setup(n,i){let{slots:o}=i,l=h(n,`modelValue`),{positionClasses:u}=e(n),{scopeId:p}=F(),{themeClasses:_}=m(n),{colorClasses:S,colorStyles:A,variantClasses:P}=a(n),{roundedClasses:I}=s(n),R=L(()=>Number(n.timeout)),z=C(),B=C(),V=w(!1),H=w(0),U=C(),W=O(d,void 0);y(()=>!!W,()=>{let e=f();x(()=>{U.value=e.mainStyles.value})}),b(l,K),b(()=>n.timeout,K),j(()=>{l.value&&K()});let G=-1;function K(){R.reset(),window.clearTimeout(G);let e=Number(n.timeout);if(!l.value||e===-1)return;let t=v(B.value);R.start(t),G=window.setTimeout(()=>{l.value=!1},e)}function q(){R.reset(),window.clearTimeout(G)}function J(){V.value=!0,q()}function Y(){V.value=!1,K()}function X(e){H.value=e.touches[0].clientY}function Z(e){Math.abs(H.value-e.changedTouches[0].clientY)>50&&(l.value=!1)}function Q(){V.value&&Y()}let $=T(()=>n.location.split(` `).reduce((e,t)=>(e[`v-snackbar--${t}`]=!0,e),{}));return g(()=>{let e=N.filterProps(n),i=!!(o.default||o.text||n.text);return D(N,k({ref:z,class:[`v-snackbar`,{"v-snackbar--active":l.value,"v-snackbar--multi-line":n.multiLine&&!n.vertical,"v-snackbar--timer":!!n.timer,"v-snackbar--vertical":n.vertical},$.value,u.value,n.class],style:[U.value,n.style]},e,{modelValue:l.value,"onUpdate:modelValue":e=>l.value=e,contentProps:k({class:[`v-snackbar__wrapper`,_.value,S.value,I.value,P.value],style:[A.value],onPointerenter:J,onPointerleave:Y},e.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:`none`,_disableGlobalStack:!0,onTouchstartPassive:X,onTouchend:Z,onAfterLeave:Q},p),{default:()=>[r(!1,`v-snackbar`),n.timer&&!V.value&&E(`div`,{key:`timer`,class:`v-snackbar__timer`},[D(t,{ref:B,color:typeof n.timer==`string`?n.timer:`info`,max:n.timeout,modelValue:R.time.value},null)]),i&&E(`div`,{key:`content`,class:`v-snackbar__content`,role:`status`,"aria-live":`polite`},[o.text?.()??n.text,o.default?.()]),o.actions&&D(c,{defaults:{VBtn:{variant:`text`,ripple:!1,slim:!0}}},{default:()=>[E(`div`,{class:`v-snackbar__actions`},[o.actions({isActive:l})])]})],activator:o.activator})}),M({},z)}});export{z as b};