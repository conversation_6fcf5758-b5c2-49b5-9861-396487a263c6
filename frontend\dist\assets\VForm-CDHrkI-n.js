import{aC as e,aG as t,aH as n,ax as r,bK as i,bQ as a,bR as o,bg as s,d as c}from"./index-BSnscBhv.js";import{j as l,k as u}from"./VTextField-BU8lnKH2.js";const d=n({...t(),...u()},`VForm`),f=e()({name:`VForm`,props:d(),emits:{"update:modelValue":e=>!0,submit:e=>!0},setup(e,t){let{slots:n,emit:u}=t,d=l(e),f=i();function p(e){e.preventDefault(),d.reset()}function m(e){let t=e,n=d.validate();t.then=n.then.bind(n),t.catch=n.catch.bind(n),t.finally=n.finally.bind(n),u(`submit`,t),t.defaultPrevented||n.then(e=>{let{valid:t}=e;t&&f.value?.submit()}),t.preventDefault()}return r(()=>s(`form`,{ref:f,class:a([`v-form`,e.class]),style:o(e.style),novalidate:!0,onReset:p,onSubmit:m},[n.default?.(d)])),c(d,f)}});export{f as b};