import{ai as e,aj as t}from"./index-BSnscBhv.js";const n=t.create({baseURL:`http://localhost:8080/api/v1`,timeout:parseInt(`30000`)||3e4,headers:{"Content-Type":`application/json`,Accept:`application/json`}});n.interceptors.request.use(e=>{let t=localStorage.getItem(`rps_auth_token`);return t&&(e.headers.Authorization=`Bearer ${t}`),console.log(`[API Request] ${e.method?.toUpperCase()} ${e.url}`,e.data),e},e=>(console.error(`[API Request Error]`,e),Promise.reject(e))),n.interceptors.response.use(e=>(console.log(`[API Response] ${e.config.method?.toUpperCase()} ${e.config.url}`,e.data),e),async t=>{let r=e();if(t.response?.status===401)try{if(await r.refreshAuthToken(),t.config)return n.request(t.config)}catch(e){console.error(`Token refresh failed:`,e),await r.logout(),window.location.href=`/login`}return t.response?.status===403&&console.error(`[API] Access forbidden:`,t.response.data),t.response?.status===500&&console.error(`[API] Server error:`,t.response.data),console.error(`[API Response Error]`,t.response?.data||t.message),Promise.reject(t)});const r={get:(e,t)=>n.get(e,{params:t}),post:(e,t)=>n.post(e,t),put:(e,t)=>n.put(e,t),patch:(e,t)=>n.patch(e,t),delete:e=>n.delete(e),upload:(e,t)=>n.post(e,t,{headers:{"Content-Type":`multipart/form-data`}}),download:(e,t)=>n.get(e,{responseType:`blob`}).then(e=>{let n=new Blob([e.data]),r=window.URL.createObjectURL(n),i=document.createElement(`a`);i.href=r,i.download=t||`download`,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(r)})},i={getStats:()=>r.get(`/dashboard/stats`),getChartData:e=>r.get(`/dashboard/charts/${e}`),getRecentActivities:()=>r.get(`/dashboard/activities`)},a={getAll:e=>r.get(`/users`,e),getById:e=>r.get(`/users/${e}`),create:e=>r.post(`/users`,e),update:(e,t)=>r.put(`/users/${e}`,t),delete:e=>r.delete(`/users/${e}`)},o={getAll:e=>r.get(`/faculties`,e),getById:e=>r.get(`/faculties/${e}`),create:e=>r.post(`/faculties`,e),update:(e,t)=>r.put(`/faculties/${e}`,t),delete:e=>r.delete(`/faculties/${e}`)},s={getAll:e=>r.get(`/study-programs`,e),getById:e=>r.get(`/study-programs/${e}`),create:e=>r.post(`/study-programs`,e),update:(e,t)=>r.put(`/study-programs/${e}`,t),delete:e=>r.delete(`/study-programs/${e}`)},c={getAll:e=>r.get(`/courses`,e),getById:e=>r.get(`/courses/${e}`),create:e=>r.post(`/courses`,e),update:(e,t)=>r.put(`/courses/${e}`,t),delete:e=>r.delete(`/courses/${e}`),getReferences:e=>r.get(`/courses/${e}/references`),createReference:(e,t)=>r.post(`/courses/${e}/references`,t),updateReference:(e,t,n)=>r.put(`/courses/${e}/references/${t}`,n),deleteReference:(e,t)=>r.delete(`/courses/${e}/references/${t}`),getTopics:e=>r.get(`/courses/${e}/topics`),createTopic:(e,t)=>r.post(`/courses/${e}/topics`,t),updateTopic:(e,t,n)=>r.put(`/courses/${e}/topics/${t}`,n),deleteTopic:(e,t)=>r.delete(`/courses/${e}/topics/${t}`),getCPMK:e=>r.get(`/courses/${e}/cpmk`)},l={getAll:e=>r.get(`/cpl`,e),getById:e=>r.get(`/cpl/${e}`),create:e=>r.post(`/cpl`,e),update:(e,t)=>r.put(`/cpl/${e}`,t),delete:e=>r.delete(`/cpl/${e}`),getByStudyProgram:e=>r.get(`/study-programs/${e}/cpl`),getMappingMatrix:e=>r.get(`/study-programs/${e}/cpl-mapping-matrix`),getAchievementReport:(e,t)=>r.get(`/study-programs/${e}/cpl-achievement`,t)},u={getAll:e=>r.get(`/cpmk`,e),getById:e=>r.get(`/cpmk/${e}`),create:e=>r.post(`/cpmk`,e),update:(e,t)=>r.put(`/cpmk/${e}`,t),delete:e=>r.delete(`/cpmk/${e}`),getByCourse:e=>r.get(`/courses/${e}/cpmk`),getSubCPMK:e=>r.get(`/cpmk/${e}/sub-cpmk`),createSubCPMK:(e,t)=>r.post(`/cpmk/${e}/sub-cpmk`,t),updateSubCPMK:(e,t,n)=>r.put(`/cpmk/${e}/sub-cpmk/${t}`,n),deleteSubCPMK:(e,t)=>r.delete(`/cpmk/${e}/sub-cpmk/${t}`),getCPLRelations:e=>r.get(`/cpmk/${e}/cpl-relations`),createCPLRelation:(e,t)=>r.post(`/cpmk/${e}/cpl-relations`,t),updateCPLRelation:(e,t,n)=>r.put(`/cpmk/${e}/cpl-relations/${t}`,n),deleteCPLRelation:(e,t)=>r.delete(`/cpmk/${e}/cpl-relations/${t}`),bulkCreateCPLRelations:(e,t)=>r.post(`/cpmk/${e}/cpl-relations/bulk`,{relations:t}),validateWeights:e=>r.get(`/courses/${e}/cpmk/validate-weights`)},d={getAll:e=>r.get(`/assessment-methods`,e),getById:e=>r.get(`/assessment-methods/${e}`),create:e=>r.post(`/assessment-methods`,e),update:(e,t)=>r.put(`/assessment-methods/${e}`,t),delete:e=>r.delete(`/assessment-methods/${e}`),getByType:e=>r.get(`/assessment-methods/type/${e}`),getByCategory:e=>r.get(`/assessment-methods/category/${e}`),getTemplates:()=>r.get(`/assessment-methods/templates`),getUsageAnalytics:e=>r.get(`/assessment-methods/${e}/analytics`)},f={getAll:e=>r.get(`/assessment-plans`,e),getById:e=>r.get(`/assessment-plans/${e}`),create:e=>r.post(`/assessment-plans`,e),update:(e,t)=>r.put(`/assessment-plans/${e}`,t),delete:e=>r.delete(`/assessment-plans/${e}`),getByCourse:(e,t)=>r.get(`/courses/${e}/assessment-plans`,t),createForCourse:(e,t)=>r.post(`/courses/${e}/assessment-plans`,t),getByCPMK:e=>r.get(`/cpmk/${e}/assessment-plans`),createForCPMK:(e,t)=>r.post(`/cpmk/${e}/assessment-plans`,t),getByWeek:(e,t)=>r.get(`/courses/${e}/assessment-plans/week/${t}`),validateWeights:e=>r.get(`/courses/${e}/assessment-plans/validate-weights`),validateCoverage:e=>r.get(`/courses/${e}/assessment-plans/validate-coverage`),getCalendar:(e,t)=>r.get(`/courses/${e}/assessment-plans/calendar`,t),getWeightDistribution:e=>r.get(`/courses/${e}/assessment-plans/weight-distribution`),bulkCreate:(e,t)=>r.post(`/courses/${e}/assessment-plans/bulk`,{plans:t}),bulkUpdate:(e,t)=>r.put(`/courses/${e}/assessment-plans/bulk`,{plans:t}),bulkDelete:(e,t)=>r.post(`/courses/${e}/assessment-plans/bulk-delete`,{ids:t}),updateRubric:(e,t)=>r.put(`/assessment-plans/${e}/rubric`,t),getRubricTemplates:()=>r.get(`/assessment-plans/rubric-templates`),validateRubric:e=>r.post(`/assessment-plans/validate-rubric`,e)},p={getTemplates:e=>r.get(`/reports/templates`,e),getTemplateById:e=>r.get(`/reports/templates/${e}`),createTemplate:e=>r.post(`/reports/templates`,e),updateTemplate:(e,t)=>r.put(`/reports/templates/${e}`,t),deleteTemplate:e=>r.delete(`/reports/templates/${e}`),duplicateTemplate:(e,t)=>r.post(`/reports/templates/${e}/duplicate`,{name:t}),generateReport:(e,t)=>r.post(`/reports/templates/${e}/generate`,t),getReportInstances:e=>r.get(`/reports/instances`,e),getReportInstance:e=>r.get(`/reports/instances/${e}`),downloadReport:e=>r.get(`/reports/instances/${e}/download`,{responseType:`blob`}),deleteReportInstance:e=>r.delete(`/reports/instances/${e}`),scheduleReport:(e,t)=>r.post(`/reports/templates/${e}/schedule`,t),getScheduledReports:e=>r.get(`/reports/scheduled`,e),updateSchedule:(e,t)=>r.put(`/reports/scheduled/${e}`,t),deleteSchedule:e=>r.delete(`/reports/scheduled/${e}`),getCPMKAchievement:e=>r.get(`/reports/cpmk-achievement`,e),getCPLMapping:e=>r.get(`/reports/cpl-mapping`,e),getAssessmentAnalytics:e=>r.get(`/reports/assessment-analytics`,e),getCoursePerformance:e=>r.get(`/reports/course-performance`,e),getStudentProgress:e=>r.get(`/reports/student-progress`,e),getFacultyOverview:e=>r.get(`/reports/faculty-overview`,e),getComplianceReport:e=>r.get(`/reports/compliance`,e),getDashboard:e=>r.get(`/reports/dashboard`,e),getDashboardWidgets:()=>r.get(`/reports/dashboard/widgets`),updateDashboardLayout:e=>r.put(`/reports/dashboard/layout`,{widgets:e}),getAnalyticsSummary:e=>r.get(`/reports/analytics/summary`,e),getTrendAnalysis:e=>r.get(`/reports/analytics/trends`,e),exportPDF:(e,t)=>r.get(`/reports/export/pdf/${e}`,{...t,responseType:`blob`}),exportExcel:(e,t)=>r.get(`/reports/export/excel/${e}`,{...t,responseType:`blob`}),exportCSV:(e,t)=>r.get(`/reports/export/csv/${e}`,{...t,responseType:`blob`}),exportJSON:(e,t)=>r.get(`/reports/export/json/${e}`,t),bulkExport:(e,t)=>n.post(`/reports/bulk-export`,{report_ids:e,format:t},{responseType:`blob`}),shareReport:(e,t)=>r.post(`/reports/instances/${e}/share`,t),getSharedReports:e=>r.get(`/reports/shared`,e),revokeShare:(e,t)=>r.delete(`/reports/instances/${e}/share/${t}`),getUsageAnalytics:e=>r.get(`/reports/analytics/usage`,e),getPopularReports:e=>r.get(`/reports/analytics/popular`,e),getPerformanceMetrics:e=>r.get(`/reports/analytics/performance`,e)};export{d as b,f as c,c as d,l as e,u as f,i as g,o as h,p as i,s as j,a as k};