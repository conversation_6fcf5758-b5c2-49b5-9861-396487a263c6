import{I as e,a2 as t,a7 as n,a8 as r,a9 as i,aC as a,aE as o,aG as s,aH as c,aO as l,aP as u,aQ as d,aR as f,aT as p,aU as m,aV as h,aZ as g,ae as _,am as v,ap as y,at as b,au as x,aw as S,ax as C,ay as w,b3 as T,b4 as E,b7 as D,b8 as O,b9 as k,bD as A,bE as j,bI as M,bK as N,bL as P,bM as F,bQ as I,bR as L,bd as R,bf as z,bg as B,bl as ee,bm as V,bq as H,br as te,bv as U,c as ne,d as W,j as G,k as K,l as re,m as ie,n as q,s as ae,t as oe}from"./index-BSnscBhv.js";import{b as se}from"./VChip-CBN0Kf2u.js";import{c as ce}from"./VDialog-VHlGBbps.js";import{b as J,c as Y,l as le}from"./VTextField-BU8lnKH2.js";const X=c({renderless:Boolean,...s()},`VVirtualScrollItem`),ue=a()({name:`VVirtualScrollItem`,inheritAttrs:!1,props:X(),emits:{"update:height":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:i}=t,{resizeRef:a,contentRect:o}=v(void 0,`border`);A(()=>o.value?.height,e=>{e!=null&&r(`update:height`,e)}),C(()=>e.renderless?B(R,null,[i.default?.({itemRef:a})]):B(`div`,H({ref:a,class:[`v-virtual-scroll__item`,e.class],style:e.style},n),[i.default?.()]))}}),de=-1,fe=1,Z=100,Q=c({itemHeight:{type:[Number,String],default:null},itemKey:{type:[String,Array,Function],default:null},height:[Number,String]},`virtual`);function $(e,t){let n=b(),r=P(0);j(()=>{r.value=parseFloat(e.itemHeight||0)});let i=P(0),a=P(Math.ceil((parseInt(e.height)||n.height.value)/(r.value||16))||1),o=P(0),s=P(0),c=N(),l=N(),u=0,{resizeRef:f,contentRect:m}=v();j(()=>{f.value=c.value});let h=z(()=>c.value===document.documentElement?n.height.value:m.value?.height||parseInt(e.height)||0),_=z(()=>!!(c.value&&l.value&&h.value&&r.value)),y=Array.from({length:t.value.length}),x=Array.from({length:t.value.length}),S=P(0),C=-1;function w(e){return y[e]||r.value}let T=p(()=>{let e=performance.now();x[0]=0;let n=t.value.length;for(let e=1;e<=n-1;e++)x[e]=(x[e-1]||0)+w(e-1);S.value=Math.max(S.value,performance.now()-e)},S),E=A(_,e=>{e&&(E(),u=l.value.offsetTop,T.immediate(),U(),~C&&te(()=>{O&&window.requestAnimationFrame(()=>{W(C),C=-1})}))});M(()=>{T.clear()});function D(e,t){let n=y[e],i=r.value;r.value=i?Math.min(r.value,t):t,(n!==t||i!==r.value)&&(y[e]=t,T())}function k(e){e=d(e,0,t.value.length-1);let n=Math.floor(e),r=e%1,i=n+1,a=x[n]||0,o=x[i]||a;return a+(o-a)*r}function F(e){return pe(x,e)}let I=0,L=0,R=0;A(h,(e,t)=>{t&&(U(),e<t&&requestAnimationFrame(()=>{L=0,U()}))});let B=-1;function ee(){if(!c.value||!l.value)return;let e=c.value.scrollTop,t=performance.now(),n=t-R;n>500?(L=Math.sign(e-I),u=l.value.offsetTop):L=e-I,I=e,R=t,window.clearTimeout(B),B=window.setTimeout(V,500),U()}function V(){!c.value||!l.value||(L=0,R=0,window.clearTimeout(B),U())}let H=-1;function U(){cancelAnimationFrame(H),H=requestAnimationFrame(ne)}function ne(){if(!c.value||!h.value||!r.value)return;let e=I-u,n=Math.sign(L),l=Math.max(0,e-Z),f=d(F(l),0,t.value.length),p=e+h.value+Z,m=d(F(p)+1,f+1,t.value.length);if((n!==de||f<i.value)&&(n!==fe||m>a.value)){let e=k(i.value)-k(f),n=k(m)-k(a.value),r=Math.max(e,n);r>Z?(i.value=f,a.value=m):(f<=0&&(i.value=f),m>=t.value.length&&(a.value=m))}o.value=k(i.value),s.value=k(t.value.length)-k(a.value)}function W(e){let t=k(e);!c.value||e&&!t?C=e:c.value.scrollTop=t}let G=z(()=>t.value.slice(i.value,a.value).map((t,n)=>{let r=n+i.value;return{raw:t,index:r,key:g(t,e.itemKey,r)}}));return A(t,()=>{y=Array.from({length:t.value.length}),x=Array.from({length:t.value.length}),T.immediate(),U()},{deep:1}),{calculateVisibleItems:U,containerRef:c,markerRef:l,computedItems:G,paddingTop:o,paddingBottom:s,scrollToIndex:W,handleScroll:ee,handleScrollend:V,handleItemResize:D}}function pe(e,t){let n=e.length-1,r=0,i=0,a=null,o=-1;if(e[n]<t)return n;for(;r<=n;)if(i=r+n>>1,a=e[i],a>t)n=i-1;else if(a<t)o=i,r=i+1;else if(a===t)return i;else return r;return o}const me=c({items:{type:Array,default:()=>[]},renderless:Boolean,...Q(),...s(),...n()},`VVirtualScroll`),he=a()({name:`VVirtualScroll`,props:me(),setup(e,t){let{slots:n}=t,i=o(`VVirtualScroll`),{dimensionStyles:a}=r(e),{calculateVisibleItems:s,containerRef:c,markerRef:l,handleScroll:u,handleScrollend:d,handleItemResize:p,scrollToIndex:m,paddingTop:h,paddingBottom:g,computedItems:_}=$(e,F(()=>e.items));return k(()=>e.renderless,()=>{function e(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,t=e?`addEventListener`:`removeEventListener`;c.value===document.documentElement?(document[t](`scroll`,u,{passive:!0}),document[t](`scrollend`,d)):(c.value?.[t](`scroll`,u,{passive:!0}),c.value?.[t](`scrollend`,d))}U(()=>{c.value=w(i.vnode.el,!0),e(!0)}),M(e)}),C(()=>{let t=_.value.map(t=>V(ue,{key:t.key,renderless:e.renderless,"onUpdate:height":e=>p(t.index,e)},{default:e=>n.default?.({item:t.raw,index:t.index,...e})}));return e.renderless?B(R,null,[B(`div`,{ref:l,class:`v-virtual-scroll__spacer`,style:{paddingTop:f(h.value)}},null),t,B(`div`,{class:`v-virtual-scroll__spacer`,style:{paddingBottom:f(g.value)}},null)]):B(`div`,{ref:c,class:I([`v-virtual-scroll`,e.class]),onScrollPassive:u,onScrollend:d,style:L([a.value,e.style])},[B(`div`,{ref:l,class:`v-virtual-scroll__container`,style:{paddingTop:f(h.value),paddingBottom:f(g.value)}},[t])])}),{calculateVisibleItems:s,scrollToIndex:m}}});function ge(e,t){let n=P(!1),r;function i(e){cancelAnimationFrame(r),n.value=!0,r=requestAnimationFrame(()=>{r=requestAnimationFrame(()=>{n.value=!1})})}async function a(){await new Promise(e=>requestAnimationFrame(e)),await new Promise(e=>requestAnimationFrame(e)),await new Promise(e=>requestAnimationFrame(e)),await new Promise(e=>{if(n.value){let t=A(n,()=>{t(),e()})}else e()})}async function o(n){if(n.key===`Tab`&&t.value?.focus(),![`PageDown`,`PageUp`,`Home`,`End`].includes(n.key))return;let r=e.value?.$el;if(!r)return;(n.key===`Home`||n.key===`End`)&&r.scrollTo({top:n.key===`Home`?0:r.scrollHeight,behavior:`smooth`}),await a();let i=r.querySelectorAll(`:scope > :not(.v-virtual-scroll__spacer)`);if(n.key===`PageDown`||n.key===`Home`){let e=r.getBoundingClientRect().top;for(let t of i)if(t.getBoundingClientRect().top>=e){t.focus();break}}else{let e=r.getBoundingClientRect().bottom;for(let t of[...i].reverse())if(t.getBoundingClientRect().bottom<=e){t.focus();break}}}return{onScrollPassive:i,onKeydown:o}}const _e=c({chips:Boolean,closableChips:Boolean,closeText:{type:String,default:`$vuetify.close`},openText:{type:String,default:`$vuetify.open`},eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,listProps:{type:Object},menu:Boolean,menuIcon:{type:y,default:`$dropdown`},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:`$vuetify.noDataText`},openOnClear:Boolean,itemColor:String,noAutoScroll:Boolean,...K({itemChildren:!1})},`Select`),ve=c({..._e(),...E(Y({modelValue:null,role:`combobox`}),[`validationValue`,`dirty`,`appendInnerIcon`]),...t({transition:{component:_}})},`VSelect`),ye=a()({name:`VSelect`,props:ve(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(t,n){let{slots:r}=n,{t:a}=x(),o=N(),s=N(),c=N(),{items:d,transformIn:f,transformOut:p}=re(t),g=S(t,`modelValue`,[],e=>f(e===null?[null]:D(e)),e=>{let n=p(e);return t.multiple?n:n[0]??null}),_=z(()=>typeof t.counterValue==`function`?t.counterValue(g.value):typeof t.counterValue==`number`?t.counterValue:g.value.length),v=le(t),y=z(()=>g.value.map(e=>e.value)),b=P(!1),w=``,E=-1,k,j=z(()=>t.hideSelected?d.value.filter(e=>!g.value.some(n=>(t.valueComparator||m)(n,e))):d.value),M=z(()=>t.hideNoData&&!j.value.length||v.isReadonly.value||v.isDisabled.value),I=S(t,`menu`),L=z({get:()=>I.value,set:e=>{I.value&&!e&&s.value?.ΨopenChildren.size||e&&M.value||(I.value=e)}}),U=F(()=>L.value?t.closeText:t.openText),K=z(()=>({...t.menuProps,activatorProps:{...t.menuProps?.activatorProps||{},"aria-haspopup":`listbox`}})),Y=N(),X=ge(Y,o);function ue(e){t.openOnClear&&(L.value=!0)}function de(){M.value||(L.value=!L.value)}function fe(e){u(e)&&Z(e)}function Z(e){if(!e.key||v.isReadonly.value)return;[`Enter`,` `,`ArrowDown`,`ArrowUp`,`Home`,`End`].includes(e.key)&&e.preventDefault(),[`Enter`,`ArrowDown`,` `].includes(e.key)&&(L.value=!0),[`Escape`,`Tab`].includes(e.key)&&(L.value=!1),e.key===`Home`?Y.value?.focus(`first`):e.key===`End`&&Y.value?.focus(`last`);let n=1e3;if(!u(e))return;let r=performance.now();r-k>n&&(w=``,E=-1),w+=e.key.toLowerCase(),k=r;let i=j.value;function a(){let t=o();return t||w.at(-1)===w.at(-2)&&(w=w.slice(0,-1),t=o(),t)||(E=-1,t=o(),t)?t:(w=e.key.toLowerCase(),o())}function o(){for(let e=E+1;e<i.length;e++){let t=i[e];if(t.title.toLowerCase().startsWith(w))return[t,e]}}let s=a();if(!s)return;let[c,l]=s;E=l,Y.value?.focus(l),t.multiple||(g.value=[c])}function Q(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!e.props.disabled)if(t.multiple){let r=g.value.findIndex(n=>(t.valueComparator||m)(n.value,e.value)),i=n??!~r;if(~r){let t=i?[...g.value,e]:[...g.value];t.splice(r,1),g.value=t}else i&&(g.value=[...g.value,e])}else{let t=n!==!1;g.value=t?[e]:[],te(()=>{L.value=!1})}}function $(e){Y.value?.$el.contains(e.relatedTarget)||(L.value=!1)}function pe(){t.eager&&c.value?.calculateVisibleItems()}function me(){b.value&&o.value?.focus()}function _e(e){b.value=!0}function ve(e){if(e==null)g.value=[];else if(T(o.value,`:autofill`)||T(o.value,`:-webkit-autofill`)){let t=d.value.find(t=>t.title===e);t&&Q(t)}else o.value&&(o.value.value=``)}return A(L,()=>{if(!t.hideSelected&&L.value&&g.value.length){let e=j.value.findIndex(e=>g.value.some(n=>(t.valueComparator||m)(n.value,e.value)));O&&!t.noAutoScroll&&window.requestAnimationFrame(()=>{e>=0&&c.value?.scrollToIndex(e)})}}),A(()=>t.items,(e,n)=>{L.value||b.value&&t.hideNoData&&!n.length&&e.length&&(L.value=!0)}),C(()=>{let n=!!(t.chips||r.chip),u=!!(!t.hideNoData||j.value.length||r[`prepend-item`]||r[`append-item`]||r[`no-data`]),d=g.value.length>0,f=J.filterProps(t),p=d||!b.value&&t.label&&!t.persistentPlaceholder?void 0:t.placeholder;return V(J,H({ref:o},f,{modelValue:g.value.map(e=>e.props.value).join(`, `),"onUpdate:modelValue":ve,focused:b.value,"onUpdate:focused":e=>b.value=e,validationValue:g.externalValue,counterValue:_.value,dirty:d,class:[`v-select`,{"v-select--active-menu":L.value,"v-select--chips":!!t.chips,[`v-select--${t.multiple?`multiple`:`single`}`]:!0,"v-select--selected":g.value.length,"v-select--selection-slot":!!r.selection},t.class],style:t.style,inputmode:`none`,placeholder:p,"onClick:clear":ue,"onMousedown:control":de,onBlur:$,onKeydown:Z,"aria-label":a(U.value),title:a(U.value)}),{...r,default:()=>B(R,null,[V(ne,H({ref:s,modelValue:L.value,"onUpdate:modelValue":e=>L.value=e,activator:`parent`,contentClass:`v-select__content`,disabled:M.value,eager:t.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:t.transition,onAfterEnter:pe,onAfterLeave:me},K.value),{default:()=>[u&&V(G,H({ref:Y,selected:y.value,selectStrategy:t.multiple?`independent`:`single-independent`,onMousedown:e=>e.preventDefault(),onKeydown:fe,onFocusin:_e,tabindex:`-1`,"aria-live":`polite`,"aria-label":`${t.label}-list`,color:t.itemColor??t.color},X,t.listProps),{default:()=>[r[`prepend-item`]?.(),!j.value.length&&!t.hideNoData&&(r[`no-data`]?.()??V(q,{key:`no-data`,title:a(t.noDataText)},null)),V(he,{ref:c,renderless:!0,items:j.value,itemKey:`value`},{default:n=>{let{item:i,index:a,itemRef:o}=n,s=l(i.props),c=H(i.props,{ref:o,key:i.value,onClick:()=>Q(i,null)});return i.type===`divider`?r.divider?.({props:i.raw,index:a})??V(ae,H(i.props,{key:`divider-${a}`}),null):i.type===`subheader`?r.subheader?.({props:i.raw,index:a})??V(ie,H(i.props,{key:`subheader-${a}`}),null):r.item?.({item:i,index:a,props:c})??V(q,H(c,{role:`option`}),{prepend:n=>{let{isSelected:r}=n;return B(R,null,[t.multiple&&!t.hideSelected?V(ce,{key:i.value,modelValue:r,ripple:!1,tabindex:`-1`},null):void 0,s.prependAvatar&&V(oe,{image:s.prependAvatar},null),s.prependIcon&&V(e,{icon:s.prependIcon},null)])}})}}),r[`append-item`]?.()]})]}),g.value.map((e,a)=>{function o(t){t.stopPropagation(),t.preventDefault(),Q(e,!1)}let s={"onClick:close":o,onKeydown(e){e.key!==`Enter`&&e.key!==` `||(e.preventDefault(),e.stopPropagation(),o(e))},onMousedown(e){e.preventDefault(),e.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},c=n?!!r.chip:!!r.selection,l=c?h(n?r.chip({item:e,index:a,props:s}):r.selection({item:e,index:a})):void 0;if(!(c&&!l))return B(`div`,{key:e.value,class:`v-select__selection`},[n?r.chip?V(i,{key:`chip-defaults`,defaults:{VChip:{closable:t.closableChips,size:`small`,text:e.title}}},{default:()=>[l]}):V(se,H({key:`chip`,closable:t.closableChips,size:`small`,text:e.title,disabled:e.props.disabled},s),null):l??B(`span`,{class:`v-select__selection-text`},[e.title,t.multiple&&a<g.value.length-1&&B(`span`,{class:`v-select__selection-comma`},[ee(`,`)])])])})]),"append-inner":function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return B(R,null,[r[`append-inner`]?.(...i),t.menuIcon?V(e,{class:`v-select__menu-icon`,color:o.value?.fieldIconColor,icon:t.menuIcon},null):void 0])}})}),W({isFocused:b,menu:L,select:Q},o)}});export{ye as b};