import{I as e,T as t,U as n,a5 as r,a6 as i,a9 as a,aC as o,aD as s,aG as c,aH as l,aU as u,aW as d,aX as f,ae as p,an as m,ap as h,aw as g,ax as _,b3 as v,b4 as y,b7 as b,b8 as x,bC as S,bD as C,bG as w,bI as T,bK as E,bL as D,bM as O,bQ as k,bR as A,bd as j,bf as M,bg as N,bm as P,bp as F,bq as I,br as L,bt as R,by as z,d as B,e as V,f as H,g as U,w as W}from"./index-BSnscBhv.js";import{n as G}from"./VTextField-BU8lnKH2.js";const K=Symbol.for(`vuetify:selection-control-group`),q=l({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:h,trueIcon:h,ripple:{type:[Boolean,Object],default:!0},multiple:{type:Boolean,default:null},name:String,readonly:{type:Boolean,default:null},modelValue:null,type:String,valueComparator:{type:Function,default:u},...c(),...t(),...m()},`SelectionControlGroup`),J=l({...q({defaultsTarget:`VSelectionControl`})},`VSelectionControlGroup`);o()({name:`VSelectionControlGroup`,props:J(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,r=g(e,`modelValue`),i=S(),a=O(()=>e.id||`v-selection-control-group-${i}`),o=O(()=>e.name||a.value),c=new Set;return z(K,{modelValue:r,forceUpdate:()=>{c.forEach(e=>e())},onForceUpdate:e=>{c.add(e),T(()=>{c.delete(e)})}}),s({[e.defaultsTarget]:{color:O(()=>e.color),disabled:O(()=>e.disabled),density:O(()=>e.density),error:O(()=>e.error),inline:O(()=>e.inline),modelValue:r,multiple:O(()=>!!e.multiple||e.multiple==null&&Array.isArray(r.value)),name:o,falseIcon:O(()=>e.falseIcon),trueIcon:O(()=>e.trueIcon),readonly:O(()=>e.readonly),ripple:O(()=>e.ripple),type:O(()=>e.type),valueComparator:O(()=>e.valueComparator)}}),_(()=>N(`div`,{class:k([`v-selection-control-group`,{"v-selection-control-group--inline":e.inline},e.class]),style:A(e.style),role:e.type===`radio`?`radiogroup`:void 0},[n.default?.()])),{}}});const Y=l({label:String,baseColor:String,trueValue:null,falseValue:null,value:null,...c(),...q()},`VSelectionControl`);function X(e){let t=F(K,void 0),{densityClasses:a}=n(e),o=g(e,`modelValue`),s=M(()=>e.trueValue===void 0?e.value===void 0?!0:e.value:e.trueValue),c=M(()=>e.falseValue===void 0?!1:e.falseValue),l=M(()=>!!e.multiple||e.multiple==null&&Array.isArray(o.value)),u=M({get(){let n=t?t.modelValue.value:o.value;return l.value?b(n).some(t=>e.valueComparator(t,s.value)):e.valueComparator(n,s.value)},set(n){if(e.readonly)return;let r=n?s.value:c.value,i=r;l.value&&(i=n?[...b(o.value),r]:b(o.value).filter(t=>!e.valueComparator(t,s.value))),t?t.modelValue.value=i:o.value=i}}),{textColorClasses:d,textColorStyles:f}=i(()=>{if(!(e.error||e.disabled))return u.value?e.color:e.baseColor}),{backgroundColorClasses:p,backgroundColorStyles:m}=r(()=>u.value&&!e.error&&!e.disabled?e.color:e.baseColor),h=M(()=>u.value?e.trueIcon:e.falseIcon);return{group:t,densityClasses:a,trueValue:s,falseValue:c,model:u,textColorClasses:d,textColorStyles:f,backgroundColorClasses:p,backgroundColorStyles:m,icon:h}}const Z=o()({name:`VSelectionControl`,directives:{vRipple:W},inheritAttrs:!1,props:Y(),emits:{"update:modelValue":e=>!0},setup(t,n){let{attrs:r,slots:i}=n,{group:a,densityClasses:o,icon:s,model:c,textColorClasses:l,textColorStyles:u,backgroundColorClasses:f,backgroundColorStyles:p,trueValue:m}=X(t),h=S(),g=D(!1),y=D(!1),b=E(),x=O(()=>t.id||`input-${h}`),C=O(()=>!t.disabled&&!t.readonly);a?.onForceUpdate(()=>{b.value&&(b.value.checked=c.value)});function T(e){C.value&&(g.value=!0,v(e.target,`:focus-visible`)!==!1&&(y.value=!0))}function M(){g.value=!1,y.value=!1}function F(e){e.stopPropagation()}function R(e){if(!C.value){b.value&&(b.value.checked=c.value);return}t.readonly&&a&&L(()=>a.forceUpdate()),c.value=e.target.checked}return _(()=>{let n=i.label?i.label({label:t.label,props:{for:x.value}}):t.label,[a,h]=d(r),_=N(`input`,I({ref:b,checked:c.value,disabled:!!t.disabled,id:x.value,onBlur:M,onFocus:T,onInput:R,"aria-disabled":!!t.disabled,"aria-label":t.label,type:t.type,value:m.value,name:t.name,"aria-checked":t.type===`checkbox`?c.value:void 0},h),null);return N(`div`,I({class:[`v-selection-control`,{"v-selection-control--dirty":c.value,"v-selection-control--disabled":t.disabled,"v-selection-control--error":t.error,"v-selection-control--focused":g.value,"v-selection-control--focus-visible":y.value,"v-selection-control--inline":t.inline},o.value,t.class]},a,{style:t.style}),[N(`div`,{class:k([`v-selection-control__wrapper`,l.value]),style:A(u.value)},[i.default?.({backgroundColorClasses:f,backgroundColorStyles:p}),w(N(`div`,{class:k([`v-selection-control__input`])},[i.input?.({model:c,textColorClasses:l,textColorStyles:u,backgroundColorClasses:f,backgroundColorStyles:p,inputNode:_,icon:s.value,props:{onFocus:T,onBlur:M,id:x.value}})??N(j,null,[s.value&&P(e,{key:`icon`,icon:s.value},null),_])]),[[W,!t.disabled&&!t.readonly&&t.ripple,null,{center:!0,circle:!0}]])]),n&&P(G,{for:x.value,onClick:F},{default:()=>[n]})])}),{isFocused:g,input:b}}}),Q=l({indeterminate:Boolean,indeterminateIcon:{type:h,default:`$checkboxIndeterminate`},...Y({falseIcon:`$checkboxOff`,trueIcon:`$checkboxOn`})},`VCheckboxBtn`),$=o()({name:`VCheckboxBtn`,props:Q(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,t){let{slots:n}=t,r=g(e,`indeterminate`),i=g(e,`modelValue`);function a(e){r.value&&=!1}let o=O(()=>r.value?e.indeterminateIcon:e.falseIcon),s=O(()=>r.value?e.indeterminateIcon:e.trueIcon);return _(()=>{let t=y(Z.filterProps(e),[`modelValue`]);return P(Z,I(t,{modelValue:i.value,"onUpdate:modelValue":[e=>i.value=e,a],class:[`v-checkbox-btn`,e.class],style:e.style,type:`checkbox`,falseIcon:o.value,trueIcon:s.value,"aria-checked":r.value?`mixed`:void 0}),n)}),{}}}),ee=l({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...H({origin:`center center`,scrollStrategy:`block`,transition:{component:p},zIndex:2400})},`VDialog`),te=o()({name:`VDialog`,props:ee(),emits:{"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,t){let{emit:n,slots:r}=t,i=g(e,`modelValue`),{scopeId:o}=U(),s=E();function c(e){let t=e.relatedTarget,n=e.target;if(t!==n&&s.value?.contentEl&&s.value?.globalTop&&![document,s.value.contentEl].includes(n)&&!s.value.contentEl.contains(n)){let e=f(s.value.contentEl);if(!e.length)return;let n=e[0],r=e[e.length-1];t===n?r.focus():n.focus()}}R(()=>{document.removeEventListener(`focusin`,c)}),x&&C(()=>i.value&&e.retainFocus,e=>{e?document.addEventListener(`focusin`,c):document.removeEventListener(`focusin`,c)},{immediate:!0});function l(){n(`afterEnter`),(e.scrim||e.retainFocus)&&s.value?.contentEl&&!s.value.contentEl.contains(document.activeElement)&&s.value.contentEl.focus({preventScroll:!0})}function u(){n(`afterLeave`)}return C(i,async e=>{e||(await L(),s.value.activatorEl?.focus({preventScroll:!0}))}),_(()=>{let t=V.filterProps(e),n=I({"aria-haspopup":`dialog`},e.activatorProps),c=I({tabindex:-1},e.contentProps);return P(V,I({ref:s,class:[`v-dialog`,{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},t,{modelValue:i.value,"onUpdate:modelValue":e=>i.value=e,"aria-modal":`true`,activatorProps:n,contentProps:c,height:e.fullscreen?void 0:e.height,width:e.fullscreen?void 0:e.width,maxHeight:e.fullscreen?void 0:e.maxHeight,maxWidth:e.fullscreen?void 0:e.maxWidth,role:`dialog`,onAfterEnter:l,onAfterLeave:u},o),{activator:r.activator,default:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return P(a,{root:`VDialog`},{default:()=>[r.default?.(...t)]})}})}),B({},s)}});export{te as b,$ as c,Q as d,Z as e,Y as f};