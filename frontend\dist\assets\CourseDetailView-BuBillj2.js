import{b as e,c as t}from"./VRow-Cvqvybmt.js";import{H as ee,I as n,ag as r,b as i,bB as a,bF as o,bK as s,bS as c,bd as te,bg as l,bh as ne,bi as u,bj as d,bl as f,bm as p,bn as m,bv as re,bx as h,bz as ie,j as ae,n as g,o as _,p as v,s as y,t as b,u as x}from"./index-BSnscBhv.js";import{d as S}from"./api-BWRuf0Vj.js";import{b as C,c as w,d as T}from"./VCard-DVRc-Pxh.js";import{b as E}from"./VChip-CBN0Kf2u.js";import"./VTimeline-uQEqPSC6.js";import"./VSelect-DqM1bu6y.js";import{b as D}from"./VDialog-VHlGBbps.js";import"./VTextField-BU8lnKH2.js";import"./FormModal-CAo97PhI.js";import"./VForm-CDHrkI-n.js";import"./VSnackbar-KpoxlJmd.js";import"./VTextarea-BciMMY-M.js";import{b as oe,c as O,d as se,e as k}from"./VTabs-Dp1ayKKb.js";import{b as A,c as j}from"./CourseTopics-0mf0fzpJ.js";const ce={key:0},le={class:`d-flex align-center mb-2`},ue={class:`text-h4 font-weight-bold text-primary`},M={class:`text-subtitle-1 text-medium-emphasis`},N={class:`d-flex align-center gap-2`},P={class:`mb-4`},F={class:`text-body-1`},I={class:`mb-4`},L={class:`text-body-1`},R={class:`mb-4`},z={class:`text-body-1`},B={class:`mb-4`},V={class:`text-body-1`},H={class:`mb-4`},U={class:`text-body-1`},de={class:`mb-4`},fe={class:`text-body-1`},pe={key:0,class:`mb-4`},me={class:`text-body-1`},he={key:1,class:`mb-4`},ge={class:`text-body-1`},_e={key:2},ve={class:`d-flex flex-wrap gap-2`},ye={class:`text-center py-8`},be={class:`d-flex align-center`},xe={class:`d-flex align-center`},Se={key:1,class:`d-flex align-center justify-center`,style:{height:`400px`}};var W=m({__name:`CourseDetailView`,setup(i){let m=r(),W=s(!1),G=s(null),K=s([]),q=s([]),J=s([]),Y=s(`references`),X=s(!1),Z=s(!1),Ce=async()=>{let e=parseInt(m.params.id);if(e){W.value=!0;try{let t=await S.getById(e);G.value=t.data.data,await Promise.all([Q(),$(),we()])}catch(e){console.error(`Failed to load course:`,e)}finally{W.value=!1}}},Q=async()=>{if(G.value)try{let e=await S.getReferences(G.value.id);K.value=e.data.data}catch(e){console.error(`Failed to load references:`,e)}},$=async()=>{if(G.value)try{let e=await S.getTopics(G.value.id);q.value=e.data.data}catch(e){console.error(`Failed to load topics:`,e)}},we=async()=>{if(G.value?.prerequisite_courses?.length)try{let e=G.value.prerequisite_courses.map(e=>S.getById(e)),t=await Promise.all(e);J.value=t.map(e=>e.data.data)}catch(e){console.error(`Failed to load prerequisites:`,e)}};return re(()=>{Ce()}),(r,i)=>{let s=a(`v-list-item-prepend`);return G.value?(h(),d(`div`,ce,[p(e,{class:`mb-6`},{default:o(()=>[p(t,null,{default:o(()=>[l(`div`,le,[p(x,{icon:``,variant:`text`,onClick:i[0]||=e=>r.$router.back(),class:`mr-2`},{default:o(()=>[p(n,null,{default:o(()=>i[13]||=[f(`mdi-arrow-left`,-1)]),_:1,__:[13]})]),_:1}),l(`div`,null,[l(`h1`,ue,c(G.value.name),1),l(`p`,M,c(G.value.code)+` • `+c(G.value.credits)+` SKS • Semester `+c(G.value.semester),1)])]),l(`div`,N,[p(E,{color:G.value.course_type===`wajib`?`warning`:`info`,variant:`tonal`},{default:o(()=>[f(c(G.value.course_type===`wajib`?`Mandatory`:`Elective`),1)]),_:1},8,[`color`]),p(E,{color:G.value.is_active?`success`:`error`,variant:`tonal`},{default:o(()=>[f(c(G.value.is_active?`Active`:`Inactive`),1)]),_:1},8,[`color`])])]),_:1})]),_:1}),p(e,{class:`mb-6`},{default:o(()=>[p(t,{cols:`12`,md:`8`},{default:o(()=>[p(C,{class:`mb-4`},{default:o(()=>[p(T,{class:`text-h6 font-weight-bold`},{default:o(()=>[p(n,{class:`mr-2`},{default:o(()=>i[14]||=[f(`mdi-information`,-1)]),_:1,__:[14]}),i[15]||=f(` Course Information `,-1)]),_:1,__:[15]}),p(w,null,{default:o(()=>[p(e,null,{default:o(()=>[p(t,{cols:`12`,md:`6`},{default:o(()=>[l(`div`,P,[i[16]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Course Code`,-1),l(`p`,F,c(G.value.code),1)]),l(`div`,I,[i[17]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Credits (SKS)`,-1),l(`p`,L,c(G.value.credits),1)]),l(`div`,R,[i[18]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Semester`,-1),l(`p`,z,c(G.value.semester),1)])]),_:1}),p(t,{cols:`12`,md:`6`},{default:o(()=>[l(`div`,B,[i[19]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Course Type`,-1),l(`p`,V,c(G.value.course_type===`wajib`?`Mandatory`:`Elective`),1)]),l(`div`,H,[i[20]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Coordinator`,-1),l(`p`,U,c(G.value.coordinator_name||`Not assigned`),1)]),l(`div`,de,[i[21]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Status`,-1),l(`p`,fe,c(G.value.is_active?`Active`:`Inactive`),1)])]),_:1})]),_:1}),G.value.description?(h(),d(`div`,pe,[i[22]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Description`,-1),l(`p`,me,c(G.value.description),1)])):u(``,!0),G.value.learning_objectives?(h(),d(`div`,he,[i[23]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-1`},`Learning Objectives`,-1),l(`p`,ge,c(G.value.learning_objectives),1)])):u(``,!0),J.value.length>0?(h(),d(`div`,_e,[i[24]||=l(`h4`,{class:`text-subtitle-2 font-weight-bold mb-2`},`Prerequisites`,-1),l(`div`,ve,[(h(!0),d(te,null,ie(J.value,e=>(h(),ne(E,{key:e.id,size:`small`,variant:`outlined`,onClick:t=>r.$router.push(`/courses/${e.id}`),class:`cursor-pointer`},{default:o(()=>[f(c(e.code)+` - `+c(e.name),1)]),_:2},1032,[`onClick`]))),128))])])):u(``,!0)]),_:1})]),_:1})]),_:1}),p(t,{cols:`12`,md:`4`},{default:o(()=>[p(C,null,{default:o(()=>[p(T,{class:`text-h6 font-weight-bold`},{default:o(()=>[p(n,{class:`mr-2`},{default:o(()=>i[25]||=[f(`mdi-lightning-bolt`,-1)]),_:1,__:[25]}),i[26]||=f(` Quick Actions `,-1)]),_:1,__:[26]}),p(w,null,{default:o(()=>[p(ae,null,{default:o(()=>[p(g,{onClick:i[1]||=e=>r.$router.push(`/courses/${G.value.id}/edit`)},{default:o(()=>[p(s,null,{default:o(()=>[p(b,{color:`primary`,size:`40`},{default:o(()=>[p(n,{color:`white`},{default:o(()=>i[27]||=[f(`mdi-pencil`,-1)]),_:1,__:[27]})]),_:1})]),_:1}),p(_,null,{default:o(()=>i[28]||=[f(`Edit Course`,-1)]),_:1,__:[28]}),p(v,null,{default:o(()=>i[29]||=[f(`Update course information`,-1)]),_:1,__:[29]})]),_:1}),p(g,{onClick:i[2]||=e=>X.value=!0},{default:o(()=>[p(s,null,{default:o(()=>[p(b,{color:`success`,size:`40`},{default:o(()=>[p(n,{color:`white`},{default:o(()=>i[30]||=[f(`mdi-book-multiple`,-1)]),_:1,__:[30]})]),_:1})]),_:1}),p(_,null,{default:o(()=>i[31]||=[f(`Manage References`,-1)]),_:1,__:[31]}),p(v,null,{default:o(()=>[f(c(K.value.length)+` references`,1)]),_:1})]),_:1}),p(g,{onClick:i[3]||=e=>Z.value=!0},{default:o(()=>[p(s,null,{default:o(()=>[p(b,{color:`warning`,size:`40`},{default:o(()=>[p(n,{color:`white`},{default:o(()=>i[32]||=[f(`mdi-format-list-numbered`,-1)]),_:1,__:[32]})]),_:1})]),_:1}),p(_,null,{default:o(()=>i[33]||=[f(`Manage Topics`,-1)]),_:1,__:[33]}),p(v,null,{default:o(()=>[f(c(q.value.length)+` topics planned`,1)]),_:1})]),_:1}),p(g,{onClick:i[4]||=e=>r.$router.push(`/courses/${G.value.id}/cpmk`)},{default:o(()=>[p(s,null,{default:o(()=>[p(b,{color:`info`,size:`40`},{default:o(()=>[p(n,{color:`white`},{default:o(()=>i[34]||=[f(`mdi-bullseye-arrow`,-1)]),_:1,__:[34]})]),_:1})]),_:1}),p(_,null,{default:o(()=>i[35]||=[f(`Manage CPMK`,-1)]),_:1,__:[35]}),p(v,null,{default:o(()=>i[36]||=[f(`Learning outcomes`,-1)]),_:1,__:[36]})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),p(C,null,{default:o(()=>[p(oe,{modelValue:Y.value,"onUpdate:modelValue":i[5]||=e=>Y.value=e,"bg-color":`primary`,dark:``},{default:o(()=>[p(k,{value:`references`},{default:o(()=>[p(n,{start:``},{default:o(()=>i[37]||=[f(`mdi-book-multiple`,-1)]),_:1,__:[37]}),f(` References (`+c(K.value.length)+`) `,1)]),_:1}),p(k,{value:`topics`},{default:o(()=>[p(n,{start:``},{default:o(()=>i[38]||=[f(`mdi-format-list-numbered`,-1)]),_:1,__:[38]}),f(` Topics (`+c(q.value.length)+`) `,1)]),_:1}),p(k,{value:`cpmk`},{default:o(()=>[p(n,{start:``},{default:o(()=>i[39]||=[f(`mdi-bullseye-arrow`,-1)]),_:1,__:[39]}),i[40]||=f(` CPMK `,-1)]),_:1,__:[40]})]),_:1},8,[`modelValue`]),p(w,null,{default:o(()=>[p(se,{modelValue:Y.value,"onUpdate:modelValue":i[6]||=e=>Y.value=e},{default:o(()=>[p(O,{value:`references`},{default:o(()=>[p(j,{course:G.value,onUpdated:Q},null,8,[`course`])]),_:1}),p(O,{value:`topics`},{default:o(()=>[p(A,{course:G.value,onUpdated:$},null,8,[`course`])]),_:1}),p(O,{value:`cpmk`},{default:o(()=>[l(`div`,ye,[p(n,{size:`64`,color:`grey-lighten-2`},{default:o(()=>i[41]||=[f(`mdi-bullseye-arrow`,-1)]),_:1,__:[41]}),i[42]||=l(`p`,{class:`text-h6 text-medium-emphasis mt-4`},`CPMK Management`,-1),i[43]||=l(`p`,{class:`text-body-2 text-medium-emphasis`},` Course Learning Outcomes management will be available in the next phase `,-1)])]),_:1})]),_:1},8,[`modelValue`])]),_:1})]),_:1}),p(D,{modelValue:X.value,"onUpdate:modelValue":i[9]||=e=>X.value=e,"max-width":`1000`,scrollable:``},{default:o(()=>[p(C,null,{default:o(()=>[p(T,{class:`d-flex align-center justify-space-between`},{default:o(()=>[l(`div`,be,[p(n,{class:`mr-2`},{default:o(()=>i[44]||=[f(`mdi-book-multiple`,-1)]),_:1,__:[44]}),i[45]||=l(`span`,{class:`text-h6 font-weight-bold`},`Course References`,-1)]),p(x,{icon:``,variant:`text`,onClick:i[7]||=e=>X.value=!1},{default:o(()=>[p(n,null,{default:o(()=>i[46]||=[f(`mdi-close`,-1)]),_:1,__:[46]})]),_:1})]),_:1}),p(y),p(w,{class:`pa-0`},{default:o(()=>[p(j,{course:G.value,onClose:i[8]||=e=>X.value=!1,onUpdated:Q},null,8,[`course`])]),_:1})]),_:1})]),_:1},8,[`modelValue`]),p(D,{modelValue:Z.value,"onUpdate:modelValue":i[12]||=e=>Z.value=e,"max-width":`1200`,scrollable:``},{default:o(()=>[p(C,null,{default:o(()=>[p(T,{class:`d-flex align-center justify-space-between`},{default:o(()=>[l(`div`,xe,[p(n,{class:`mr-2`},{default:o(()=>i[47]||=[f(`mdi-format-list-numbered`,-1)]),_:1,__:[47]}),i[48]||=l(`span`,{class:`text-h6 font-weight-bold`},`Course Topics`,-1)]),p(x,{icon:``,variant:`text`,onClick:i[10]||=e=>Z.value=!1},{default:o(()=>[p(n,null,{default:o(()=>i[49]||=[f(`mdi-close`,-1)]),_:1,__:[49]})]),_:1})]),_:1}),p(y),p(w,{class:`pa-0`},{default:o(()=>[p(A,{course:G.value,onClose:i[11]||=e=>Z.value=!1,onUpdated:$},null,8,[`course`])]),_:1})]),_:1})]),_:1},8,[`modelValue`])])):(h(),d(`div`,Se,[p(ee,{indeterminate:``,color:`primary`,size:`64`})]))}}}),G=i(W,[[`__scopeId`,`data-v-5da1f739`]]);export{G as default};