import{I as e,aC as t,aH as n,aw as r,ax as i,b as a,b4 as o,bA as s,bC as c,bF as l,bK as u,bM as d,bS as f,bc as p,bf as m,bg as h,bh as g,bi as _,bl as v,bm as y,bn as b,bq as x,bx as S,d as C,e as w,f as T,g as E,q as D,s as O,u as k}from"./index-BSnscBhv.js";import{b as A,c as j,d as M,e as N}from"./VCard-DVRc-Pxh.js";import{b as P}from"./VDialog-VHlGBbps.js";import{b as F}from"./VForm-CDHrkI-n.js";const I=n({id:String,interactive:Boolean,text:String,...o(T({closeOnBack:!1,location:`end`,locationStrategy:`connected`,eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:`auto`,scrim:!1,scrollStrategy:`reposition`,transition:null}),[`absolute`,`persistent`])},`VTooltip`),L=t()({name:`VTooltip`,props:I(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t,a=r(e,`modelValue`),{scopeId:o}=E(),s=c(),l=d(()=>e.id||`v-tooltip-${s}`),f=u(),p=m(()=>e.location.split(` `).length>1?e.location:e.location+` center`),h=m(()=>e.origin===`auto`||e.origin===`overlap`||e.origin.split(` `).length>1||e.location.split(` `).length>1?e.origin:e.origin+` center`),g=d(()=>e.transition==null?a.value?`scale-transition`:`fade-transition`:e.transition),_=m(()=>x({"aria-describedby":l.value},e.activatorProps));return i(()=>{let t=w.filterProps(e);return y(w,x({ref:f,class:[`v-tooltip`,{"v-tooltip--interactive":e.interactive},e.class],style:e.style,id:l.value},t,{modelValue:a.value,"onUpdate:modelValue":e=>a.value=e,transition:g.value,absolute:!0,location:p.value,origin:h.value,persistent:!0,role:`tooltip`,activatorProps:_.value,_disableGlobalStack:!0},o),{activator:n.activator,default:function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return n.default?.(...r)??e.text}})}),C({},f)}}),R={class:`d-flex align-center`},z={class:`text-h6 font-weight-bold`};var B=b({__name:`FormModal`,props:{modelValue:{type:Boolean},title:{},icon:{},maxWidth:{default:600},persistent:{type:Boolean,default:!1},scrollable:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},mode:{default:`create`}},emits:[`update:modelValue`,`submit`,`close`],setup(t,{expose:n,emit:r}){let i=t,a=r,o=u(),c=u(!1),d=m(()=>{switch(i.mode){case`create`:return`Create`;case`edit`:return`Update`;case`view`:return`Close`;default:return`Save`}}),b=m(()=>{switch(i.mode){case`create`:return`mdi-plus`;case`edit`:return`mdi-content-save`;case`view`:return`mdi-close`;default:return`mdi-check`}}),x=async()=>{if(i.mode===`view`){C();return}let{valid:e}=await o.value.validate();e&&a(`submit`)},C=()=>{i.loading||(a(`close`),a(`update:modelValue`,!1))};return n({validate:()=>o.value?.validate(),reset:()=>o.value?.reset(),resetValidation:()=>o.value?.resetValidation()}),(t,n)=>(S(),g(P,{"model-value":t.modelValue,"onUpdate:modelValue":n[1]||=e=>t.$emit(`update:modelValue`,e),"max-width":t.maxWidth,persistent:t.persistent,scrollable:t.scrollable},{default:l(()=>[y(A,null,{default:l(()=>[y(M,{class:`d-flex align-center justify-space-between`},{default:l(()=>[h(`div`,R,[t.icon?(S(),g(e,{key:0,class:`mr-2`},{default:l(()=>[v(f(t.icon),1)]),_:1})):_(``,!0),h(`span`,z,f(t.title),1)]),y(k,{icon:``,variant:`text`,onClick:C,disabled:t.loading},{default:l(()=>[y(e,null,{default:l(()=>n[2]||=[v(`mdi-close`,-1)]),_:1,__:[2]})]),_:1},8,[`disabled`])]),_:1}),y(O),y(j,{class:`pa-6`},{default:l(()=>[y(F,{ref_key:`form`,ref:o,modelValue:c.value,"onUpdate:modelValue":n[0]||=e=>c.value=e,onSubmit:p(x,[`prevent`])},{default:l(()=>[s(t.$slots,`default`,{loading:t.loading,valid:c.value},void 0,!0)]),_:3},8,[`modelValue`])]),_:3}),y(N,{class:`px-6 pb-6`},{default:l(()=>[y(D),y(k,{variant:`text`,onClick:C,disabled:t.loading},{default:l(()=>n[3]||=[v(` Cancel `,-1)]),_:1,__:[3]},8,[`disabled`]),y(k,{color:`primary`,loading:t.loading,disabled:!c.value||t.loading,onClick:x},{default:l(()=>[y(e,{start:``},{default:l(()=>[v(f(b.value),1)]),_:1}),v(` `+f(d.value),1)]),_:1},8,[`loading`,`disabled`])]),_:1})]),_:3})]),_:3},8,[`model-value`,`max-width`,`persistent`,`scrollable`]))}}),V=a(B,[[`__scopeId`,`data-v-900872c8`]]);export{V as b,L as c};